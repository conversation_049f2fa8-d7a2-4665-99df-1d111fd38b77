# Activities by Interaction Type API Documentation

## Overview

The Activities by Interaction Type API endpoint allows you to retrieve all activities filtered by their interaction type. This enables you to get all call activities at once (without visits) or all visit activities at once (without calls), providing clean separation of different interaction types.

## Endpoint

**GET** `/api/v1/activities/by-interaction-type`

## Features

- ✅ **Filtered by Interaction Type**: Get only 'call' or 'visit' activities
- ✅ **Complete Data**: Includes lead details, performer info, and purpose data
- ✅ **Case Insensitive**: Accepts 'call', 'CALL', 'Call', etc.
- ✅ **Ordered Results**: Activities ordered by most recent first
- ✅ **Comprehensive Response**: Includes total count and metadata
- ✅ **Validation**: Proper error handling for invalid types

## Request Format

### Basic Request
```bash
GET /api/v1/activities/by-interaction-type?type=call
GET /api/v1/activities/by-interaction-type?type=visit
```

## Query Parameters

| Parameter | Type | Required | Description | Valid Values |
|-----------|------|----------|-------------|--------------|
| `type` | string | Yes | Interaction type to filter by | `call`, `visit` (case insensitive) |

## Response Format

```json
{
  "data": [
    {
      "id": "3c24426f-548a-46f1-a624-7d2b7df3d3b5",
      "lead_id": "lead-uuid",
      "lead_name": "Alice Johns",
      "lead_phone": "+************",
      "lead_client_id": "CLI001",
      "interaction_type": "call",
      "activity_type": "Follow Up",
      "call_status": "Success",
      "visit_status": null,
      "notes": "Customer interested in product demo",
      "call_duration_minutes": 15,
      "next_followup_date": "2025-08-05T10:00:00.000Z",
      "performed_by": {
        "id": "user-uuid",
        "name": "John Smith",
        "email": "<EMAIL>",
        "rm_code": "RM001"
      },
      "purpose": {
        "id": "purpose-uuid",
        "name": "Product Demo",
        "description": "Demonstrate product features"
      },
      "created_at": "2025-07-30T10:59:31.042Z",
      "updated_at": "2025-07-30T10:59:31.042Z"
    }
  ],
  "total": 2,
  "interaction_type": "call",
  "message": "Retrieved 2 call activities successfully"
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `data` | array | Array of activity objects |
| `total` | number | Total number of activities found |
| `interaction_type` | string | The interaction type that was filtered |
| `message` | string | Success message with count |

### Activity Object Structure

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Activity UUID |
| `lead_id` | string | Associated lead UUID |
| `lead_name` | string | Lead/customer name |
| `lead_phone` | string\|null | Lead phone number |
| `lead_client_id` | string\|null | Lead client ID |
| `interaction_type` | string | Type of interaction ('call' or 'visit') |
| `activity_type` | string | Specific activity type |
| `call_status` | string\|null | Call status (for call activities) |
| `visit_status` | string\|null | Visit status (for visit activities) |
| `notes` | string\|null | Activity notes |
| `call_duration_minutes` | number\|null | Call duration (for call activities) |
| `next_followup_date` | string\|null | Next follow-up date (ISO 8601) |
| `performed_by` | object | User who performed the activity |
| `purpose` | object\|null | Activity purpose details |
| `created_at` | string | Activity creation date (ISO 8601) |
| `updated_at` | string | Last update date (ISO 8601) |

## Usage Examples

### Example 1: Get All Call Activities

**Request:**
```bash
curl -X GET "http://localhost:3000/api/v1/activities/by-interaction-type?type=call" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "data": [
    {
      "id": "3c24426f-548a-46f1-a624-7d2b7df3d3b5",
      "lead_name": "Alice Johns",
      "interaction_type": "call",
      "activity_type": "Follow Up",
      "call_status": "Success",
      "call_duration_minutes": 15,
      "performed_by": {
        "name": "John Smith",
        "email": "<EMAIL>"
      },
      "created_at": "2025-07-30T10:59:31.042Z"
    }
  ],
  "total": 1,
  "interaction_type": "call",
  "message": "Retrieved 1 call activities successfully"
}
```

### Example 2: Get All Visit Activities

**Request:**
```bash
curl -X GET "http://localhost:3000/api/v1/activities/by-interaction-type?type=visit" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "data": [
    {
      "id": "b6ddc0b0-4d7a-4210-aa58-fc7cea5f43db",
      "lead_name": "Alice John",
      "interaction_type": "visit",
      "activity_type": "First Visit",
      "visit_status": "Successful",
      "call_duration_minutes": null,
      "performed_by": {
        "name": "John Smith",
        "email": "<EMAIL>"
      },
      "created_at": "2025-07-31T06:00:07.314Z"
    }
  ],
  "total": 1,
  "interaction_type": "visit",
  "message": "Retrieved 1 visit activities successfully"
}
```

### Example 3: JavaScript/Frontend Usage

```javascript
// Get all call activities
async function getAllCallActivities() {
  try {
    const response = await fetch('/api/v1/activities/by-interaction-type?type=call');
    const data = await response.json();
    
    console.log(`Found ${data.total} call activities`);
    return data.data;
  } catch (error) {
    console.error('Error fetching call activities:', error);
  }
}

// Get all visit activities
async function getAllVisitActivities() {
  try {
    const response = await fetch('/api/v1/activities/by-interaction-type?type=visit');
    const data = await response.json();
    
    console.log(`Found ${data.total} visit activities`);
    return data.data;
  } catch (error) {
    console.error('Error fetching visit activities:', error);
  }
}

// Generic function for any interaction type
async function getActivitiesByType(interactionType) {
  try {
    const response = await fetch(
      `/api/v1/activities/by-interaction-type?type=${encodeURIComponent(interactionType)}`
    );
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching ${interactionType} activities:`, error);
    throw error;
  }
}
```

### Example 4: React Component

```jsx
import { useState, useEffect } from 'react';

const ActivitiesByType = ({ interactionType }) => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/v1/activities/by-interaction-type?type=${interactionType}`
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch activities');
        }
        
        const data = await response.json();
        setActivities(data.data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, [interactionType]);

  if (loading) return <div>Loading {interactionType} activities...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>{interactionType.charAt(0).toUpperCase() + interactionType.slice(1)} Activities ({activities.length})</h2>
      {activities.map(activity => (
        <div key={activity.id} className="activity-card">
          <h3>{activity.lead_name}</h3>
          <p>Type: {activity.activity_type}</p>
          <p>Status: {activity.call_status || activity.visit_status}</p>
          <p>By: {activity.performed_by.name}</p>
          <p>Date: {new Date(activity.created_at).toLocaleDateString()}</p>
        </div>
      ))}
    </div>
  );
};

// Usage
<ActivitiesByType interactionType="call" />
<ActivitiesByType interactionType="visit" />
```

## Error Handling

### Common Error Responses

| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | Bad Request | Invalid or missing interaction type |
| 500 | Internal Server Error | Database or server error |

### Error Response Format

```json
{
  "statusCode": 400,
  "message": "Invalid interaction type. Must be \"call\" or \"visit\"",
  "error": "Bad Request"
}
```

### Frontend Error Handling

```javascript
async function getActivitiesWithErrorHandling(type) {
  try {
    const response = await fetch(`/api/v1/activities/by-interaction-type?type=${type}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    if (error.message.includes('Invalid interaction type')) {
      console.error('Invalid type provided. Use "call" or "visit"');
    } else if (error.message.includes('fetch')) {
      console.error('Network error. Please check your connection.');
    } else {
      console.error('Unexpected error:', error.message);
    }
    throw error;
  }
}
```

## Use Cases

### 1. **Call Activity Dashboard**
```javascript
// Display all call activities for call center dashboard
const callActivities = await getActivitiesByType('call');
const totalCalls = callActivities.total;
const successfulCalls = callActivities.data.filter(a => a.call_status === 'Success').length;
```

### 2. **Visit Activity Reports**
```javascript
// Generate visit activity reports
const visitActivities = await getActivitiesByType('visit');
const visitsByStatus = visitActivities.data.reduce((acc, visit) => {
  acc[visit.visit_status] = (acc[visit.visit_status] || 0) + 1;
  return acc;
}, {});
```

### 3. **Performance Analytics**
```javascript
// Analyze performance by interaction type
const [calls, visits] = await Promise.all([
  getActivitiesByType('call'),
  getActivitiesByType('visit')
]);

const analytics = {
  totalCalls: calls.total,
  totalVisits: visits.total,
  callSuccessRate: calls.data.filter(c => c.call_status === 'Success').length / calls.total,
  visitSuccessRate: visits.data.filter(v => v.visit_status === 'Successful').length / visits.total
};
```

### 4. **Activity Type Filtering**
```javascript
// Filter activities by specific criteria within interaction type
const callActivities = await getActivitiesByType('call');
const followUpCalls = callActivities.data.filter(a => a.activity_type === 'Follow Up');
const prospectingCalls = callActivities.data.filter(a => a.activity_type === 'Prospecting');
```

## Performance Considerations

- **Database Indexing**: Ensure `interaction_type` field is indexed for optimal performance
- **Large Datasets**: Consider pagination for very large result sets
- **Caching**: Implement caching for frequently accessed data
- **Concurrent Requests**: API handles multiple simultaneous requests efficiently

## Security & Authorization

- **Authentication**: Endpoint respects existing authentication requirements
- **Data Access**: Users only see activities they have permission to view
- **Input Validation**: Strict validation of interaction type parameter

The Activities by Interaction Type API provides efficient access to filtered activity data, enabling specialized dashboards, reports, and analytics for different types of customer interactions.
