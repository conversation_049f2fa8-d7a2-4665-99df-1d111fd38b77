# API Endpoints Testing Guide

## Customer Categories API

### Base URL
```
http://localhost:3000/api/v1
```

### Authentication
All endpoints require JWT authentication. Include the Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Endpoints

#### 1. GET /customer-categories
**Description**: Get all customer categories
**Response Format**:
```json
{
  "success": true,
  "message": "Customer categories fetched successfully",
  "data": [
    {
      "id": "7d9f6a45-2e83-42a9-9e7d-89ffb6e921ab",
      "name": "VIP",
      "customers_count": 5,
      "added_by": "admin",
      "added_on": "2025-08-20T09:15:00.000Z"
    }
  ]
}
```

#### 2. POST /customer-categories
**Description**: Create a new customer category
**Request Body**:
```json
{
  "name": "Regular"
}
```
**Response Format**:
```json
{
  "success": true,
  "message": "Customer category created successfully",
  "data": {
    "id": "d41d8cd9-8f00-3204-a980-0998ecf8427e",
    "name": "Regular",
    "customers_count": 0,
    "added_by": "admin",
    "added_on": "2025-08-20T09:20:00.000Z"
  }
}
```

#### 3. PATCH /customer-categories/:id
**Description**: Update customer category name
**Request Body**:
```json
{
  "name": "Updated Regular"
}
```
**Response Format**:
```json
{
  "success": true,
  "message": "Customer category updated successfully",
  "data": {
    "id": "d41d8cd9-8f00-3204-a980-0998ecf8427e",
    "name": "Updated Regular",
    "customers_count": 0,
    "added_by": "admin",
    "added_on": "2025-08-20T09:20:00.000Z"
  }
}
```

#### 4. DELETE /customer-categories/:id
**Description**: Delete customer category
**Response Format**:
```json
{
  "success": true,
  "message": "Customer category deleted successfully",
  "data": null
}
```

## Customer Feedback Categories API

### Endpoints

#### 1. GET /customer-feedback-categories
**Description**: Get all customer feedback categories
**Response Format**:
```json
{
  "success": true,
  "message": "Customer feedback categories fetched successfully",
  "data": [
    {
      "id": "7d9f6a45-2e83-42a9-9e7d-89ffb6e921ab",
      "name": "Product Inquiry",
      "added_by": "admin",
      "added_on": "2025-08-20T09:15:00.000Z"
    }
  ]
}
```

#### 2. POST /customer-feedback-categories
**Description**: Create a new customer feedback category
**Request Body**:
```json
{
  "name": "Service Complaint"
}
```
**Response Format**:
```json
{
  "success": true,
  "message": "Customer feedback category created successfully",
  "data": {
    "id": "d41d8cd9-8f00-3204-a980-0998ecf8427e",
    "name": "Service Complaint",
    "added_by": "admin",
    "added_on": "2025-08-20T09:20:00.000Z"
  }
}
```

#### 3. PATCH /customer-feedback-categories/:id
**Description**: Update customer feedback category name
**Request Body**:
```json
{
  "name": "Updated Service Complaint"
}
```

#### 4. DELETE /customer-feedback-categories/:id
**Description**: Delete customer feedback category
**Response Format**:
```json
{
  "success": true,
  "message": "Customer feedback category deleted successfully",
  "data": null
}
```

## Testing Notes

1. **Database Migration**: The Prisma schema has been updated to include `created_at`, `updated_at`, and `added_by` fields for both models.

2. **Authentication Required**: All endpoints require JWT authentication.

3. **User Context**: The `added_by` field is automatically populated from the authenticated user's context.

4. **Validation**: 
   - Category names are required and must be unique (case-insensitive)
   - Maximum length of 255 characters for names

5. **Error Handling**: 
   - 400: Bad Request (validation errors)
   - 401: Unauthorized (missing/invalid token)
   - 404: Not Found (category doesn't exist)
   - 409: Conflict (duplicate name or cannot delete with associations)

6. **Swagger Documentation**: Available at `http://localhost:3000/api/docs`
