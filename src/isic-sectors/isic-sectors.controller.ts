import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { IsicSectorsService } from './isic-sectors.service';
import {
  CreateIsicSectorDto,
  BulkCreateIsicSectorsDto,
  ExcelUploadDto,
  ExcelUploadResponseDto
} from './dto/create-isic-sector.dto';
import { UpdateIsicSectorDto } from './dto/update-isic-sector.dto';
import { IsicSectorResponseDto } from './dto/isic-sector-response.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';

/**
 * Controller handling all ISIC sector-related HTTP endpoints
 * Provides RESTful API for ISIC sector management
 */
@ApiTags('ISIC Sectors')
@Controller('isic-sectors')
export class IsicSectorsController {
  constructor(private readonly isicSectorsService: IsicSectorsService) {}

  /**
   * Creates a new ISIC sector
   * POST /isic-sectors
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new ISIC sector',
    description:
      'Creates a new ISIC sector with the provided name and optional code. Both name and code must be unique (case-insensitive). If no code is provided, it will be set as empty.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'ISIC sector created successfully',
    type: IsicSectorResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({
    description: 'ISIC sector name or code already exists',
  })
  async create(
    @Body(ValidationPipe) createIsicSectorDto: CreateIsicSectorDto,
  ): Promise<IsicSectorResponseDto> {
    return this.isicSectorsService.create(createIsicSectorDto);
  }

  /**
   * Creates multiple ISIC sectors in bulk
   * POST /isic-sectors/bulk
   */
  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create multiple ISIC sectors in bulk',
    description:
      'Creates multiple ISIC sectors at once. Returns detailed results including successes and failures.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk creation completed',
    schema: {
      type: 'object',
      properties: {
        totalProcessed: { type: 'number', example: 10 },
        totalCreated: { type: 'number', example: 8 },
        totalFailed: { type: 'number', example: 2 },
        createdSectors: {
          type: 'array',
          items: { $ref: '#/components/schemas/IsicSectorResponseDto' },
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              index: { type: 'number' },
              error: { type: 'string' },
              sectorData: { type: 'object' },
            },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Some ISIC sectors could not be created due to conflicts' })
  async createBulk(
    @Body(ValidationPipe) bulkCreateIsicSectorsDto: BulkCreateIsicSectorsDto,
  ) {
    return this.isicSectorsService.createBulk(bulkCreateIsicSectorsDto);
  }

  /**
   * Creates ISIC sectors from an uploaded Excel file
   * POST /isic-sectors/upload-excel
   */
  @Post('upload-excel')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FileInterceptor('file', {
    fileFilter: (req, file, callback) => {
      if (!file.originalname.match(/\.(xlsx|xls)$/)) {
        return callback(new BadRequestException('Only Excel files (.xlsx, .xls) are allowed'), false);
      }
      callback(null, true);
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Excel file containing ISIC sector data',
    type: ExcelUploadDto,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel file (.xlsx or .xls)',
        },
      },
    },
  })
  @ApiOperation({
    summary: 'Create ISIC sectors from Excel file',
    description:
      'Uploads an Excel file and creates ISIC sectors from the data. Supports intelligent column mapping and comprehensive error reporting. Expected columns: ISIC Code (optional), Sector Name/Description (required).',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Excel file processed successfully',
    type: ExcelUploadResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or parsing errors'
  })
  @ApiConflictResponse({
    description: 'Some ISIC sectors could not be created due to conflicts'
  })
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ExcelUploadResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.isicSectorsService.createFromExcel(file);
  }

  /**
   * Retrieves all ISIC sectors with pagination and search
   * GET /isic-sectors
   */
  @Get()
  @ApiOperation({
    summary: 'Get all ISIC sectors',
    description:
      'Retrieves a paginated list of ISIC sectors with optional search functionality. Search works on both code and name fields. Includes lead count for each sector.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for ISIC code or name',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'ISIC sectors retrieved successfully',
    type: PaginatedResponseDto<IsicSectorResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<IsicSectorResponseDto>> {
    return this.isicSectorsService.findAll(paginationDto);
  }

  /**
   * Retrieves a single ISIC sector by ID
   * GET /isic-sectors/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get ISIC sector by ID',
    description:
      'Retrieves a single ISIC sector by its UUID. Includes lead count and basic sector information.',
  })
  @ApiParam({ name: 'id', description: 'ISIC sector UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'ISIC sector retrieved successfully',
    type: IsicSectorResponseDto,
  })
  @ApiNotFoundResponse({ description: 'ISIC sector not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<IsicSectorResponseDto> {
    return this.isicSectorsService.findOne(id);
  }

  /**
   * Updates an existing ISIC sector
   * PATCH /isic-sectors/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update ISIC sector',
    description:
      'Updates an existing ISIC sector. Only provided fields (code and/or name) will be updated. Code and name must remain unique (case-insensitive).',
  })
  @ApiParam({ name: 'id', description: 'ISIC sector UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'ISIC sector updated successfully',
    type: IsicSectorResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'ISIC sector not found' })
  @ApiConflictResponse({
    description: 'ISIC sector code or name already exists',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateIsicSectorDto: UpdateIsicSectorDto,
  ): Promise<IsicSectorResponseDto> {
    return this.isicSectorsService.update(id, updateIsicSectorDto);
  }

  /**
   * Deletes an ISIC sector
   * DELETE /isic-sectors/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete ISIC sector',
    description:
      'Deletes an ISIC sector by ID. Cannot delete sectors that have associated leads.',
  })
  @ApiParam({ name: 'id', description: 'ISIC sector UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'ISIC sector deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'ISIC sector not found' })
  @ApiConflictResponse({
    description: 'ISIC sector has associated leads and cannot be deleted',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.isicSectorsService.remove(id);
  }

  /**
   * Retrieves all leads for a specific ISIC sector
   * GET /isic-sectors/:id/leads
   */
  @Get(':id/leads')
  @ApiOperation({
    summary: 'Get leads by ISIC sector',
    description:
      'Retrieves all leads belonging to a specific ISIC sector with pagination and search.',
  })
  @ApiParam({ name: 'id', description: 'ISIC sector UUID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for customer name or client ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leads retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'ISIC sector not found' })
  async getSectorLeads(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ) {
    return this.isicSectorsService.getSectorLeads(id, paginationDto);
  }
}
