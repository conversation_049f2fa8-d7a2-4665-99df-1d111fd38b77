import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateIsicSectorDto,
  BulkCreateIsicSectorsDto,
  ExcelUploadResponseDto
} from './dto/create-isic-sector.dto';
import { UpdateIsicSectorDto } from './dto/update-isic-sector.dto';
import { IsicSectorResponseDto } from './dto/isic-sector-response.dto';
import * as XLSX from 'xlsx';
import { Express } from 'express';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all ISIC sector-related business logic
 * Implements CRUD operations with optimized database queries
 */
@Injectable()
export class IsicSectorsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new ISIC sector
   * @param createIsicSectorDto - Data for creating the ISIC sector
   * @returns Promise<IsicSectorResponseDto> - The created ISIC sector
   * @throws ConflictException if sector name or code already exists
   */
  async create(
    createIsicSectorDto: CreateIsicSectorDto,
  ): Promise<IsicSectorResponseDto> {
    try {
      // Check if ISIC sector with same name or code already exists (case-insensitive)
      const conflictConditions: Prisma.ISICSectorWhereInput[] = [];

      // Always check for name conflicts
      conflictConditions.push({
        name: {
          equals: createIsicSectorDto.name,
          mode: 'insensitive' as const,
        },
      });

      // Check for code conflicts if code is provided
      if (createIsicSectorDto.code) {
        conflictConditions.push({
          code: {
            equals: createIsicSectorDto.code,
            mode: 'insensitive' as const,
          },
        });
      }

      const existingSector = await this.prisma.iSICSector.findFirst({
        where: {
          OR: conflictConditions,
        },
      });

      if (existingSector) {
        if (
          existingSector.name.toLowerCase() ===
          createIsicSectorDto.name.toLowerCase()
        ) {
          throw new ConflictException(
            `ISIC sector with name '${createIsicSectorDto.name}' already exists`,
          );
        }
        if (
          createIsicSectorDto.code &&
          existingSector.code?.toLowerCase() ===
            createIsicSectorDto.code.toLowerCase()
        ) {
          throw new ConflictException(
            `ISIC sector with code '${createIsicSectorDto.code}' already exists`,
          );
        }
      }

      // Create the new ISIC sector
      const sector = await this.prisma.iSICSector.create({
        data: {
          name: createIsicSectorDto.name,
          code: createIsicSectorDto.code || '', // Use provided code or empty string
        },
      });

      return {
        id: sector.id,
        code: sector.code,
        name: sector.name,
        addedOnDate: sector.created_at,
        addedBy: '', // Return empty string as per requirements
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create ISIC sector');
    }
  }

  /**
   * Retrieves all ISIC sectors with pagination and optional search
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<IsicSectorResponseDto>> - Paginated list of ISIC sectors
   */
  async findAll(
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<IsicSectorResponseDto>> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build where clause for search functionality
    const whereClause: Prisma.ISICSectorWhereInput = search
      ? {
          OR: [
            {
              code: {
                contains: search,
                mode: 'insensitive', // Case-insensitive search
              },
            },
            {
              name: {
                contains: search,
                mode: 'insensitive', // Case-insensitive search
              },
            },
          ],
        }
      : {};

    // Execute both count and data queries in parallel for better performance
    const [sectors, total] = await Promise.all([
      this.prisma.iSICSector.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: [
          { code: 'asc' }, // Sort by code first
          { name: 'asc' }, // Then by name
        ],
      }),
      this.prisma.iSICSector.count({
        where: whereClause,
      }),
    ]);

    // Transform data to response DTOs
    const data = sectors.map((sector) => ({
      id: sector.id,
      code: sector.code,
      name: sector.name,
      addedOnDate: sector.created_at,
      addedBy: '', // Return empty string as per requirements
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves a single ISIC sector by ID
   * @param id - The UUID of the ISIC sector
   * @returns Promise<IsicSectorResponseDto> - The ISIC sector data
   * @throws NotFoundException if ISIC sector doesn't exist
   */
  async findOne(id: string): Promise<IsicSectorResponseDto> {
    const sector = await this.prisma.iSICSector.findUnique({
      where: { id },
    });

    if (!sector) {
      throw new NotFoundException(`ISIC sector with ID '${id}' not found`);
    }

    return {
      id: sector.id,
      code: sector.code,
      name: sector.name,
      addedOnDate: sector.created_at,
      addedBy: '', // Return empty string as per requirements
    };
  }

  /**
   * Updates an existing ISIC sector
   * @param id - The UUID of the ISIC sector to update
   * @param updateIsicSectorDto - Data for updating the ISIC sector (only provided fields will be updated)
   * @returns Promise<IsicSectorResponseDto> - The updated ISIC sector
   * @throws NotFoundException if ISIC sector doesn't exist
   * @throws ConflictException if new code or name conflicts with existing sector
   */
  async update(
    id: string,
    updateIsicSectorDto: UpdateIsicSectorDto,
  ): Promise<IsicSectorResponseDto> {
    // Check if ISIC sector exists
    const existingSector = await this.prisma.iSICSector.findUnique({
      where: { id },
    });

    if (!existingSector) {
      throw new NotFoundException(`ISIC sector with ID '${id}' not found`);
    }

    // If no fields to update, return the existing sector
    if (!updateIsicSectorDto.code && !updateIsicSectorDto.name) {
      return {
        id: existingSector.id,
        code: existingSector.code,
        name: existingSector.name,
        addedOnDate: existingSector.created_at,
        addedBy: '', // Return empty string as per requirements
      };
    }

    // Check for conflicts if code or name is being updated
    const conflictConditions: Prisma.ISICSectorWhereInput[] = [];

    if (
      updateIsicSectorDto.code !== undefined &&
      updateIsicSectorDto.code !== existingSector.code
    ) {
      conflictConditions.push({
        code: {
          equals: updateIsicSectorDto.code,
          mode: 'insensitive' as const,
        },
      });
    }

    if (
      updateIsicSectorDto.name !== undefined &&
      updateIsicSectorDto.name !== existingSector.name
    ) {
      conflictConditions.push({
        name: {
          equals: updateIsicSectorDto.name,
          mode: 'insensitive' as const,
        },
      });
    }

    if (conflictConditions.length > 0) {
      const conflictingSector = await this.prisma.iSICSector.findFirst({
        where: {
          OR: conflictConditions,
          id: {
            not: id, // Exclude current sector from conflict check
          },
        },
      });

      if (conflictingSector) {
        if (
          updateIsicSectorDto.code !== undefined &&
          conflictingSector.code === updateIsicSectorDto.code
        ) {
          throw new ConflictException(
            `ISIC sector with code '${updateIsicSectorDto.code}' already exists`,
          );
        }
        if (
          updateIsicSectorDto.name !== undefined &&
          conflictingSector.name === updateIsicSectorDto.name
        ) {
          throw new ConflictException(
            `ISIC sector with name '${updateIsicSectorDto.name}' already exists`,
          );
        }
      }
    }

    try {
      // Build update data object with only provided fields
      const updateData: Partial<{ code: string; name: string }> = {};

      if (updateIsicSectorDto.code !== undefined) {
        updateData.code = updateIsicSectorDto.code;
      }

      if (updateIsicSectorDto.name !== undefined) {
        updateData.name = updateIsicSectorDto.name;
      }

      const updatedSector = await this.prisma.iSICSector.update({
        where: { id },
        data: updateData,
      });

      return {
        id: updatedSector.id,
        code: updatedSector.code,
        name: updatedSector.name,
        addedOnDate: updatedSector.created_at,
        addedBy: '', // Return empty string as per requirements
      };
    } catch (error) {
      throw new BadRequestException('Failed to update ISIC sector');
    }
  }
  /**
   * Deletes an ISIC sector by ID
   * @param id - The UUID of the ISIC sector to delete
   * @returns Promise<void>
   * @throws NotFoundException if ISIC sector doesn't exist
   * @throws ConflictException if ISIC sector has associated leads
   */
  async remove(id: string): Promise<void> {
    // Check if ISIC sector exists and has leads
    const sector = await this.prisma.iSICSector.findUnique({
      where: { id },
      include: {
        _count: {
          select: { leads: true },
        },
      },
    });

    if (!sector) {
      throw new NotFoundException(`ISIC sector with ID '${id}' not found`);
    }

    // Prevent deletion if ISIC sector has leads
    if ((sector as any)._count.leads > 0) {
      throw new ConflictException(
        `Cannot delete ISIC sector '${sector.name || sector.code}' because it has ${(sector as any)._count.leads} associated lead(s). Please reassign or remove these leads first.`,
        `Cannot delete ISIC sector '${sector.name || sector.code}' because it has ${sector._count.leads} associated lead(s). Please reassign or remove these leads first.`,
      );
    }

    try {
      await this.prisma.iSICSector.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException('Failed to delete ISIC sector');
    }
  }

  /**
   * Retrieves all leads for a specific ISIC sector
   * @param sectorId - The UUID of the ISIC sector
   * @param paginationDto - Pagination parameters
   * @returns Promise<PaginatedResponseDto<any>> - Paginated list of leads
   * @throws NotFoundException if ISIC sector doesn't exist
   */
  async getSectorLeads(sectorId: string, paginationDto: PaginationDto) {
    // Verify ISIC sector exists
    const sector = await this.prisma.iSICSector.findUnique({
      where: { id: sectorId },
    });

    if (!sector) {
      throw new NotFoundException(
        `ISIC sector with ID '${sectorId}' not found`,
      );
    }

    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: Prisma.LeadWhereInput = {
      isic_sector_id: sectorId,
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ],
      }),
    };

    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        skip,
        take: limit,
        select: {
          id: true,
          customer_name: true,
          account_number: true,
          account_number_assigned_at: true,
          phone_number: true,
          type_of_lead: true,
        },
        orderBy: {
          customer_name: 'asc',
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: leads,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Creates multiple ISIC sectors in bulk
   * @param bulkCreateDto - Data containing array of ISIC sectors to create
   * @returns Object with creation results and errors
   */
  async createBulk(bulkCreateDto: BulkCreateIsicSectorsDto) {
    const { sectors } = bulkCreateDto;
    const results = {
      totalProcessed: sectors.length,
      totalCreated: 0,
      totalFailed: 0,
      createdSectors: [] as IsicSectorResponseDto[],
      errors: [] as Array<{
        index: number;
        error: string;
        sectorData: CreateIsicSectorDto;
      }>,
    };

    for (let i = 0; i < sectors.length; i++) {
      try {
        const createdSector = await this.create(sectors[i]);
        results.createdSectors.push(createdSector);
        results.totalCreated++;
      } catch (error) {
        results.errors.push({
          index: i,
          error: error.message,
          sectorData: sectors[i],
        });
        results.totalFailed++;
      }
    }

    return results;
  }

  /**
   * Creates ISIC sectors from an uploaded Excel file
   * @param file - The uploaded Excel file
   * @returns Promise<ExcelUploadResponseDto> - Results of the import operation
   */
  async createFromExcel(file: Express.Multer.File): Promise<ExcelUploadResponseDto> {
    try {
      // Parse Excel file
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];

      if (!sheetName) {
        throw new BadRequestException('Excel file contains no sheets');
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      if (rawData.length === 0) {
        throw new BadRequestException('Excel file is empty');
      }

      // Extract headers and data rows
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1) as any[][];

      if (dataRows.length === 0) {
        throw new BadRequestException('Excel file contains no data rows');
      }

      // Map Excel columns to ISIC sector fields
      const columnMappings = this.mapExcelColumns(headers);

      // Convert Excel rows to ISIC sector objects
      const { sectors, parseErrors } = this.parseExcelRows(
        dataRows,
        headers,
        columnMappings,
      );

      // Create ISIC sectors using bulk creation logic
      const bulkResult = await this.createBulk({ sectors });

      // Combine parsing errors with creation errors
      const allErrors = [
        ...parseErrors,
        ...bulkResult.errors.map((error) => ({
          row: error.index + 2, // +2 because index is 0-based and we skip header row
          error: error.error,
          data: error.sectorData,
        })),
      ];

      return {
        success: allErrors.length === 0,
        message: `Successfully processed ${dataRows.length} rows from Excel file`,
        totalRows: rawData.length,
        processedRows: dataRows.length,
        successfulCreations: bulkResult.totalCreated,
        failedCreations: allErrors.length,
        createdSectors: bulkResult.createdSectors,
        errors: allErrors,
        columnMappings,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to process Excel file: ${error.message}`);
    }
  }

  /**
   * Maps Excel column headers to ISIC sector field names
   * @param headers - Array of Excel column headers
   * @returns Object mapping Excel headers to field names
   */
  private mapExcelColumns(headers: string[]): Record<string, string> {
    const mappings: Record<string, string> = {};

    headers.forEach((header) => {
      const normalizedHeader = header.toLowerCase().trim();

      // Map various possible column names to our fields
      if (normalizedHeader.includes('code') || normalizedHeader.includes('isic')) {
        mappings[header] = 'code';
      } else if (
        normalizedHeader.includes('name') ||
        normalizedHeader.includes('sector') ||
        normalizedHeader.includes('description') ||
        normalizedHeader.includes('title')
      ) {
        mappings[header] = 'name';
      }
    });

    return mappings;
  }

  /**
   * Parses Excel rows into ISIC sector objects
   * @param dataRows - Array of Excel data rows
   * @param headers - Array of Excel headers
   * @param columnMappings - Mapping of Excel columns to field names
   * @returns Object containing parsed sectors and any errors
   */
  private parseExcelRows(
    dataRows: any[][],
    headers: string[],
    columnMappings: Record<string, string>,
  ) {
    const sectors: CreateIsicSectorDto[] = [];
    const parseErrors: Array<{ row: number; error: string; data: any }> = [];

    for (let i = 0; i < dataRows.length; i++) {
      const rowData = dataRows[i];
      const rowNumber = i + 2; // +2 because arrays are 0-based and we skip header row

      try {
        const sectorData: any = {};

        // Map Excel columns to ISIC sector fields
        headers.forEach((header, index) => {
          const fieldName = columnMappings[header];
          if (fieldName && rowData[index] !== undefined && rowData[index] !== null && rowData[index] !== '') {
            sectorData[fieldName] = String(rowData[index]).trim();
          }
        });

        // Validate required fields
        if (!sectorData.name) {
          parseErrors.push({
            row: rowNumber,
            error: 'Sector name is required',
            data: sectorData,
          });
          continue;
        }

        // Ensure code is optional but if provided, it should be a string
        if (sectorData.code && typeof sectorData.code !== 'string') {
          sectorData.code = String(sectorData.code);
        }

        sectors.push(sectorData);
      } catch (error) {
        parseErrors.push({
          row: rowNumber,
          error: `Failed to parse row: ${error.message}`,
          data: rowData,
        });
      }
    }

    return {
      sectors,
      parseErrors,
    };
  }
}
