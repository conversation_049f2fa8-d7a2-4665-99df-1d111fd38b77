import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  IsO<PERSON>al,
  IsArray,
  ArrayNotEmpty,
  ValidateNested
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsicSectorResponseDto } from './isic-sector-response.dto';

/**
 * Data Transfer Object for creating a new ISIC sector
 * Validates input data and provides API documentation
 */
export class CreateIsicSectorDto {
  @ApiPropertyOptional({
    description: 'The ISIC code for the sector',
    example: 'A01',
    maxLength: 10,
  })
  @IsOptional()
  @IsString({ message: 'ISIC code must be a string' })
  @MaxLength(10, { message: 'ISIC code cannot exceed 10 characters' })
  code?: string;

  @ApiProperty({
    description: 'The name of the ISIC sector',
    example: 'Agriculture, forestry and fishing',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Sector name is required' })
  @IsString({ message: 'Sector name must be a string' })
  @MaxLength(255, { message: 'Sector name cannot exceed 255 characters' })
  name: string;
}

/**
 * Data Transfer Object for bulk ISIC sector creation
 */
export class BulkCreateIsicSectorsDto {
  @ApiProperty({
    description: 'Array of ISIC sectors to create',
    type: [CreateIsicSectorDto],
    example: [
      {
        code: 'A01',
        name: 'Agriculture, forestry and fishing'
      },
      {
        code: 'B05',
        name: 'Mining of coal and lignite'
      }
    ],
  })
  @IsArray({ message: 'ISIC sectors must be an array' })
  @ArrayNotEmpty({ message: 'At least one ISIC sector is required' })
  @ValidateNested({ each: true })
  @Type(() => CreateIsicSectorDto)
  sectors: CreateIsicSectorDto[];
}

/**
 * Data Transfer Object for Excel file upload
 */
export class ExcelUploadDto {
  // No additional fields needed - file comes from multipart form data
}

/**
 * Data Transfer Object for Excel file upload response
 */
export class ExcelUploadResponseDto {
  @ApiProperty({
    description: 'Whether the upload was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Summary message of the upload operation',
    example: 'Successfully processed 50 rows from Excel file',
  })
  message: string;

  @ApiProperty({
    description: 'Total number of rows found in Excel file',
    example: 52,
  })
  totalRows: number;

  @ApiProperty({
    description: 'Number of data rows processed (excluding headers)',
    example: 50,
  })
  processedRows: number;

  @ApiProperty({
    description: 'Number of ISIC sectors successfully created',
    example: 45,
  })
  successfulCreations: number;

  @ApiProperty({
    description: 'Number of ISIC sectors that failed to create',
    example: 5,
  })
  failedCreations: number;

  @ApiProperty({
    description: 'Array of successfully created ISIC sectors',
    type: [IsicSectorResponseDto],
  })
  createdSectors: IsicSectorResponseDto[];

  @ApiProperty({
    description: 'Array of parsing and creation errors',
    example: [
      {
        row: 3,
        error: 'Sector name is required',
        data: { code: 'A01' }
      }
    ],
  })
  errors: Array<{
    row: number;
    error: string;
    data: any;
  }>;

  @ApiProperty({
    description: 'Detected column mappings from Excel headers',
    example: {
      'ISIC Code': 'code',
      'Sector Name': 'name',
      'Description': 'name'
    },
  })
  columnMappings: Record<string, string>;
}
