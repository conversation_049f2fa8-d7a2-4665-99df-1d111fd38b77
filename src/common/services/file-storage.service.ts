import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const unlink = promisify(fs.unlink);
const access = promisify(fs.access);

export interface SavedFileInfo {
  filename: string;
  originalName: string;
  path: string;
  url: string;
  size: number;
  mimetype: string;
}

@Injectable()
export class FileStorageService {
  private readonly logger = new Logger(FileStorageService.name);
  private readonly uploadDir: string;
  private readonly baseUrl: string;

  constructor(private configService: ConfigService) {
    // Get upload directory from config or use default
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
    this.baseUrl = this.configService.get<string>('BASE_URL') || 'http://localhost:3000';
    
    // Ensure upload directory exists
    this.ensureUploadDirExists();
  }

  /**
   * Saves a file to disk and returns file information
   */
  async saveFile(
    file: Express.Multer.File,
    subDirectory: string = 'general'
  ): Promise<SavedFileInfo> {
    try {
      // Generate unique filename to prevent conflicts
      const fileExtension = path.extname(file.originalname);
      const uniqueFilename = `${uuidv4()}${fileExtension}`;
      
      // Create full directory path
      const fullDir = path.join(this.uploadDir, subDirectory);
      await this.ensureDirectoryExists(fullDir);
      
      // Create full file path
      const filePath = path.join(fullDir, uniqueFilename);
      
      // Validate file
      this.validateFile(file);
      
      // Save file to disk
      await writeFile(filePath, file.buffer);
      
      // Generate URL for accessing the file
      const fileUrl = `${this.baseUrl}/uploads/${subDirectory}/${uniqueFilename}`;
      
      this.logger.log(`File saved successfully: ${filePath}`);
      
      return {
        filename: uniqueFilename,
        originalName: file.originalname,
        path: filePath,
        url: fileUrl,
        size: file.size,
        mimetype: file.mimetype,
      };
    } catch (error) {
      this.logger.error(`Failed to save file: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to save file: ${error.message}`);
    }
  }

  /**
   * Saves multiple files to disk
   */
  async saveFiles(
    files: Express.Multer.File[],
    subDirectory: string = 'general'
  ): Promise<SavedFileInfo[]> {
    const savedFiles: SavedFileInfo[] = [];
    
    for (const file of files) {
      try {
        const savedFile = await this.saveFile(file, subDirectory);
        savedFiles.push(savedFile);
      } catch (error) {
        // If any file fails, clean up previously saved files
        await this.cleanupFiles(savedFiles);
        throw error;
      }
    }
    
    return savedFiles;
  }

  /**
   * Deletes a file from disk
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      await access(filePath, fs.constants.F_OK);
      await unlink(filePath);
      this.logger.log(`File deleted successfully: ${filePath}`);
    } catch (error) {
      if (error.code === 'ENOENT') {
        this.logger.warn(`File not found for deletion: ${filePath}`);
      } else {
        this.logger.error(`Failed to delete file: ${filePath}`, error.stack);
        throw new BadRequestException(`Failed to delete file: ${error.message}`);
      }
    }
  }

  /**
   * Deletes multiple files from disk
   */
  async deleteFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      await this.deleteFile(filePath);
    }
  }

  /**
   * Checks if a file exists
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      await access(filePath, fs.constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Gets file information without reading the file
   */
  async getFileInfo(filePath: string): Promise<fs.Stats | null> {
    try {
      return fs.statSync(filePath);
    } catch {
      return null;
    }
  }

  /**
   * Validates uploaded file
   */
  private validateFile(file: Express.Multer.File): void {
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size exceeds 10MB limit');
    }

    // Check file type (basic validation)
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type ${file.mimetype} is not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`
      );
    }

    // Check filename
    if (!file.originalname || file.originalname.trim() === '') {
      throw new BadRequestException('File must have a valid filename');
    }
  }

  /**
   * Ensures the main upload directory exists
   */
  private async ensureUploadDirExists(): Promise<void> {
    try {
      await access(this.uploadDir, fs.constants.F_OK);
    } catch {
      await mkdir(this.uploadDir, { recursive: true });
      this.logger.log(`Created upload directory: ${this.uploadDir}`);
    }
  }

  /**
   * Ensures a specific directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await access(dirPath, fs.constants.F_OK);
    } catch {
      await mkdir(dirPath, { recursive: true });
      this.logger.log(`Created directory: ${dirPath}`);
    }
  }

  /**
   * Cleans up files (used when an error occurs during multi-file upload)
   */
  private async cleanupFiles(savedFiles: SavedFileInfo[]): Promise<void> {
    for (const file of savedFiles) {
      try {
        await this.deleteFile(file.path);
      } catch (error) {
        this.logger.error(`Failed to cleanup file: ${file.path}`, error.stack);
      }
    }
  }

  /**
   * Gets the upload directory path
   */
  getUploadDir(): string {
    return this.uploadDir;
  }

  /**
   * Gets the base URL for file access
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}
