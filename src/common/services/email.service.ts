import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Send OTP email to user
   * @param to - Recipient email address
   * @param context - Template context variables
   */
  async sendOtpEmail(
    to: string,
    context: {
      name: string;
      otp: string;
      otp_validity_minutes: number;
      timestamp: string;
      app_name: string;
    },
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to,
        subject: `Your One-Time Password (OTP) for ${context.app_name}`,
        template: 'otp-email',
        context,
      });

      this.logger.log(`OTP email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send OTP email to ${to}:`, error);

      // Provide more specific error messages
      if (error.code === 'ESOCKET') {
        throw new Error(
          'Email service connection failed. Please check SMTP configuration.',
        );
      } else if (error.code === 'EAUTH') {
        throw new Error(
          'Email authentication failed. Please check email credentials.',
        );
      } else if (error.code === 'EENVELOPE') {
        throw new Error('Invalid email address format.');
      } else {
        throw new Error(
          `Failed to send OTP email: ${error.message || 'Unknown error'}`,
        );
      }
    }
  }

  /**
   * Send a generic email
   * @param to - Recipient email address
   * @param subject - Email subject
   * @param template - Template name
   * @param context - Template context variables
   */
  async sendEmail(
    to: string,
    subject: string,
    template: string,
    context: any,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to,
        subject,
        template,
        context,
      });

      this.logger.log(`Email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}:`, error);

      // Provide more specific error messages
      if (error.code === 'ESOCKET') {
        throw new Error(
          'Email service connection failed. Please check SMTP configuration.',
        );
      } else if (error.code === 'EAUTH') {
        throw new Error(
          'Email authentication failed. Please check email credentials.',
        );
      } else if (error.code === 'EENVELOPE') {
        throw new Error('Invalid email address format.');
      } else {
        throw new Error(
          `Failed to send email: ${error.message || 'Unknown error'}`,
        );
      }
    }
  }
}
