/**
 * Utility functions for OTP generation and validation
 */

/**
 * Generate a 6-character alphanumeric OTP code
 * Uses uppercase letters and numbers for better readability
 * @returns string - 6-character OTP code
 */
export function generateOtp(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let otp = '';
  
  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    otp += characters[randomIndex];
  }
  
  return otp;
}

/**
 * Check if an OTP has expired
 * @param expiresAt - The expiration date of the OTP
 * @returns boolean - true if expired, false if still valid
 */
export function isOtpExpired(expiresAt: Date): boolean {
  return new Date() > expiresAt;
}

/**
 * Generate OTP expiration date
 * @param minutes - Number of minutes from now when OTP should expire
 * @returns Date - Expiration date
 */
export function generateOtpExpiry(minutes: number = 5): Date {
  const now = new Date();
  return new Date(now.getTime() + minutes * 60 * 1000);
}

/**
 * Format timestamp for email display
 * @param date - Date to format
 * @returns string - Formatted timestamp
 */
export function formatTimestamp(date: Date = new Date()): string {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short',
  });
}
