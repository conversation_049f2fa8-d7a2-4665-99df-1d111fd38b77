import { isWeekend as dateFnsIsWeekend } from 'date-fns';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Utility functions for date operations and business logic
 */

/**
 * Check if a given date is a weekend (Saturday or Sunday)
 * @param date - The date to check
 * @returns boolean - true if weekend, false otherwise
 */
export function isWeekend(date: Date): boolean {
  return dateFnsIsWeekend(date);
}

/**
 * Check if a given date is a holiday by querying the database
 * @param date - The date to check
 * @param prisma - Prisma service instance
 * @returns Promise<boolean> - true if holiday, false otherwise
 */
export async function isHoliday(date: Date, prisma: PrismaService): Promise<boolean> {
  // Normalize the date to start of day for comparison
  const normalizedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  const holiday = await prisma.holiday.findFirst({
    where: {
      date: {
        gte: normalizedDate,
        lt: new Date(normalizedDate.getTime() + 24 * 60 * 60 * 1000), // Next day
      },
    },
  });
  
  return !!holiday;
}

/**
 * Check if a date is applicable for target tracking (not weekend or holiday)
 * @param date - The date to check
 * @param prisma - Prisma service instance
 * @returns Promise<boolean> - true if applicable, false if weekend or holiday
 */
export async function isApplicable(date: Date, prisma: PrismaService): Promise<boolean> {
  // Check if it's a weekend
  if (isWeekend(date)) {
    return false;
  }
  
  // Check if it's a holiday
  const holiday = await isHoliday(date, prisma);
  if (holiday) {
    return false;
  }
  
  return true;
}

/**
 * Create a date with 2 AM time for consistent period boundaries
 * @param date - The base date
 * @returns Date - Date with time set to 2:00 AM
 */
export function createDateAt2AM(date: Date): Date {
  const result = new Date(date);
  result.setHours(2, 0, 0, 0);
  return result;
}

/**
 * Generate an array of dates between start and end dates (inclusive)
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Date[] - Array of dates
 */
export function generateDateRange(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return dates;
}

/**
 * Check if two dates are the same day (ignoring time)
 * @param date1 - First date
 * @param date2 - Second date
 * @returns boolean - true if same day, false otherwise
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

/**
 * Get today's date with 2 AM time
 * @returns Date - Today's date at 2:00 AM
 */
export function getTodayAt2AM(): Date {
  return createDateAt2AM(new Date());
}
