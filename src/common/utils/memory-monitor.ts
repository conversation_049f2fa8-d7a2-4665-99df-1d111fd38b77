import { Logger } from '@nestjs/common';

export class MemoryMonitor {
  private static readonly logger = new Logger(MemoryMonitor.name);
  private static monitoringInterval: NodeJS.Timeout | null = null;

  /**
   * Start monitoring memory usage
   * @param intervalMs Monitoring interval in milliseconds (default: 30 seconds)
   * @param memoryThresholdMB Memory threshold in MB to trigger warnings (default: 1500MB)
   */
  static startMonitoring(intervalMs: number = 30000, memoryThresholdMB: number = 1500) {
    if (this.monitoringInterval) {
      this.logger.warn('Memory monitoring is already running');
      return;
    }

    this.logger.log(`Starting memory monitoring (interval: ${intervalMs}ms, threshold: ${memoryThresholdMB}MB)`);

    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage(memoryThresholdMB);
    }, intervalMs);
  }

  /**
   * Stop memory monitoring
   */
  static stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.logger.log('Memory monitoring stopped');
    }
  }

  /**
   * Check current memory usage and log warnings if threshold is exceeded
   */
  static checkMemoryUsage(thresholdMB: number = 1500) {
    const memUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
    const rssMB = Math.round(memUsage.rss / 1024 / 1024);
    const externalMB = Math.round(memUsage.external / 1024 / 1024);

    // Log memory stats
    this.logger.debug(`Memory Usage - RSS: ${rssMB}MB, Heap Used: ${heapUsedMB}MB, Heap Total: ${heapTotalMB}MB, External: ${externalMB}MB`);

    // Check if memory usage exceeds threshold
    if (heapUsedMB > thresholdMB) {
      this.logger.warn(`⚠️  High memory usage detected: ${heapUsedMB}MB (threshold: ${thresholdMB}MB)`);
      
      // Force garbage collection if available
      if (global.gc) {
        this.logger.log('Forcing garbage collection...');
        global.gc();
        
        // Log memory after GC
        const afterGC = process.memoryUsage();
        const afterHeapUsedMB = Math.round(afterGC.heapUsed / 1024 / 1024);
        this.logger.log(`Memory after GC: ${afterHeapUsedMB}MB (freed: ${heapUsedMB - afterHeapUsedMB}MB)`);
      }
    }

    return {
      rss: rssMB,
      heapUsed: heapUsedMB,
      heapTotal: heapTotalMB,
      external: externalMB,
    };
  }

  /**
   * Force garbage collection if available
   */
  static forceGC() {
    if (global.gc) {
      const beforeGC = process.memoryUsage();
      const beforeHeapUsedMB = Math.round(beforeGC.heapUsed / 1024 / 1024);
      
      global.gc();
      
      const afterGC = process.memoryUsage();
      const afterHeapUsedMB = Math.round(afterGC.heapUsed / 1024 / 1024);
      
      this.logger.log(`Forced GC: ${beforeHeapUsedMB}MB -> ${afterHeapUsedMB}MB (freed: ${beforeHeapUsedMB - afterHeapUsedMB}MB)`);
      
      return {
        before: beforeHeapUsedMB,
        after: afterHeapUsedMB,
        freed: beforeHeapUsedMB - afterHeapUsedMB,
      };
    } else {
      this.logger.warn('Garbage collection not available. Start Node.js with --expose-gc flag.');
      return null;
    }
  }

  /**
   * Get current memory usage statistics
   */
  static getMemoryStats() {
    const memUsage = process.memoryUsage();
    return {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
    };
  }
}
