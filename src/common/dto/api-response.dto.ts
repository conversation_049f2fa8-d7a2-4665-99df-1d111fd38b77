import { ApiProperty } from '@nestjs/swagger';

/**
 * Standard API response wrapper
 * All API endpoints should return responses in this format
 */
export class ApiResponseDto<T = any> {
  @ApiProperty({
    description: 'Indicates if the request was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Human-readable message describing the result',
    example: 'Operation completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'The actual data payload (can be object, array, or null)',
    nullable: true,
  })
  data: T | null;

  constructor(success: boolean, message: string, data: T | null = null) {
    this.success = success;
    this.message = message;
    this.data = data;
  }

  /**
   * Create a successful response
   */
  static success<T>(message: string, data: T | null = null): ApiResponseDto<T> {
    return new ApiResponseDto(true, message, data);
  }

  /**
   * Create an error response
   */
  static error(message: string): ApiResponseDto<null> {
    return new ApiResponseDto(false, message, null);
  }
}
