import { IsOptional, IsPositive, IsInt, Min, Max, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for pagination parameters
 * Provides standardized pagination across all endpoints
 */
export class PaginationDto {
  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    example: 1,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Search term to filter results',
    example: 'Central',
  })
  @IsOptional()
  search?: string;
}

/**
 * Data Transfer Object for pagination parameters with branch filtering
 * Extends PaginationDto to include useBranch parameter for user endpoints
 */
export class UserPaginationDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Whether to filter users by the current user\'s branch',
    example: 'true',
  })
  @IsOptional()
  @IsString()
  useBranch?: string;
}

/**
 * Generic response wrapper for paginated results
 */
export class PaginatedResponseDto<T> {
  @ApiPropertyOptional({
    description: 'Array of items for the current page',
  })
  data: T[];

  @ApiPropertyOptional({
    description: 'Pagination metadata',
  })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
