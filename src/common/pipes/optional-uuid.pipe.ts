import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { isUUID } from 'class-validator';

/**
 * Custom pipe for optional UUID validation
 * Allows undefined/null values but validates UUID format when provided
 */
@Injectable()
export class OptionalUUIDPipe implements PipeTransform<string, string | undefined> {
  transform(value: string): string | undefined {
    // If value is undefined, null, or empty string, return undefined
    if (value === undefined || value === null || value === '') {
      return undefined;
    }

    // If value is provided, validate it as UUID
    if (!isUUID(value, 4)) {
      throw new BadRequestException('Validation failed (uuid is expected)');
    }

    return value;
  }
}
