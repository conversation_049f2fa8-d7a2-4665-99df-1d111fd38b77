import { Module } from '@nestjs/common';
import { LoggerModule } from 'nestjs-pino';

@Module({
  imports: [
    LoggerModule.forRoot({
      pinoHttp: {
        // Enable automatic request/response logging
        autoLogging: true,

        // Custom success message format
        customSuccessMessage: (req: any, res: any) => {
          return `${req.method} ${req.url} ${res.statusCode} - ${res.responseTime}ms`;
        },

        // Custom error message format
        customErrorMessage: (req: any, res: any, err: any) => {
          return `${req.method} ${req.url} ${res.statusCode} - ${res.responseTime}ms - Error: ${err.message}`;
        },

        // Disable custom properties to reduce log verbosity
        customProps: () => ({}),

        // Transport configuration for pretty printing in development
        transport:
          process.env.NODE_ENV !== 'production'
            ? {
                target: 'pino-pretty',
                options: {
                  colorize: true,
                  translateTime: 'SYS:standard',
                  ignore: 'pid,hostname',
                  messageFormat: '{msg}',
                  singleLine: true,
                },
              }
            : undefined,

        // Base configuration
        level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',

        // Custom timestamp format
        timestamp: () => `,"time":"${new Date().toISOString()}"`,

        // Redact sensitive information
        redact: {
          paths: [
            'req.headers.authorization',
            'req.headers.cookie',
            'req.body.password',
            'req.body.currentPassword',
            'req.body.newPassword',
            'req.body.token',
          ],
          censor: '[REDACTED]',
        },

        // Simplified serializers to reduce log verbosity
        serializers: {
          req: () => undefined, // Disable request serialization
          res: () => undefined, // Disable response serialization
        },
      },
    }),
  ],
  exports: [LoggerModule],
})
export class LoggingModule {}
