import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { join } from 'path';
import { existsSync } from 'fs';
import { LoggingModule } from './logging/logging.module';
import { CommonModule } from './common/common.module';
import { PrismaModule } from './prisma/prisma.module';
import { QueueModule } from './queue/queue.module';
import { ScheduledTaskModule } from './scheduled-tasks/scheduled-task.module';
import { SchedulerModule as SchedulerServiceModule } from './scheduler/scheduler.module';

/**
 * Scheduler Module - Dedicated module for cron jobs and recurring tasks
 * This module handles scheduled tasks without the HTTP server
 */
@Module({
  imports: [
    LoggingModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // Determine template directory based on environment
        const templateDirs = [
          join(__dirname, '..', 'templates'), // Production (dist/templates)
          join(process.cwd(), 'templates'), // Development (root/templates)
        ];

        const templateDir =
          templateDirs.find((dir) => existsSync(dir)) || templateDirs[0];

        return {
          transport: {
            host: configService.get<string>('MAIL_HOST') || 'smtp.gmail.com',
            port: parseInt(configService.get<string>('MAIL_PORT') || '587'),
            secure: false, // true for 465, false for other ports
            auth: {
              user: configService.get<string>('MAIL_USER'),
              pass: configService.get<string>('MAIL_PASS'),
            },
            tls: {
              // Do not fail on invalid certs
              rejectUnauthorized: false,
            },
            // Additional options for Gmail
            requireTLS: true,
            connectionTimeout: 60000,
            greetingTimeout: 30000,
            socketTimeout: 60000,
          },
          defaults: {
            from: configService.get<string>('MAIL_FROM'),
          },
          template: {
            dir: templateDir,
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
    CommonModule,
    PrismaModule,
    QueueModule,
    ScheduledTaskModule,
    SchedulerServiceModule,
  ],
  controllers: [],
  providers: [],
})
export class SchedulerModule {}
