import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for customer response
 * Returns customer information from leads table
 */
export class CustomerResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the customer (lead ID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Account number of the customer',
    example: 'ACC123456789',
    nullable: true,
  })
  account_number: string | null;

  @ApiPropertyOptional({
    description: 'Date and time when the account number was assigned (lead converted to client)',
    example: '2025-08-04T22:15:30.000Z',
    nullable: true,
  })
  account_number_assigned_at: Date | null;

  @ApiProperty({
    description: 'Name of the customer',
    example: 'John Do<PERSON>',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Phone number of the customer',
    example: '+************',
    nullable: true,
  })
  phone_number: string | null;

  @ApiPropertyOptional({
    description: 'Branch information',
    nullable: true,
  })
  branch: {
    id: string;
    name: string;
  } | null;
}
