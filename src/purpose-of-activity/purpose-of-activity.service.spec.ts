import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PurposeOfActivityService } from './purpose-of-activity.service';
import { PrismaService } from '../prisma/prisma.service';

/**
 * PurposeOfActivityService Unit Tests
 * 
 * Comprehensive test suite covering all service methods with various scenarios:
 * - Success cases
 * - Error handling
 * - Edge cases
 * - Database interaction mocking
 */
describe('PurposeOfActivityService', () => {
  let service: PurposeOfActivityService;
  let prismaService: PrismaService;

  // Mock data for testing
  const mockPurpose = {
    id: '550e8400-e29b-41d4-a716-446655440000',
    name: 'Product Demo',
    description: 'Demonstrate product features',
    _count: {
      general_activities: 5,
      activities: 10,
    },
  };

  const mockPrismaService = {
    purposeOfActivity: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    generalActivity: {
      count: jest.fn(),
    },
    activity: {
      count: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PurposeOfActivityService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<PurposeOfActivityService>(PurposeOfActivityService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    const createDto = {
      name: 'Product Demo',
      description: 'Demonstrate product features',
    };

    it('should create a purpose successfully', async () => {
      mockPrismaService.purposeOfActivity.findFirst.mockResolvedValue(null);
      mockPrismaService.purposeOfActivity.create.mockResolvedValue(mockPurpose);

      const result = await service.create(createDto);

      expect(result).toEqual({
        id: mockPurpose.id,
        name: mockPurpose.name,
        description: mockPurpose.description,
        general_activities_count: 5,
        activities_count: 10,
        total_activities_count: 15,
        is_in_use: true,
      });

      expect(mockPrismaService.purposeOfActivity.findFirst).toHaveBeenCalledWith({
        where: {
          name: {
            equals: createDto.name,
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      });
    });

    it('should throw ConflictException if name already exists', async () => {
      mockPrismaService.purposeOfActivity.findFirst.mockResolvedValue(mockPurpose);

      await expect(service.create(createDto)).rejects.toThrow(ConflictException);
      expect(mockPrismaService.purposeOfActivity.create).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      mockPrismaService.purposeOfActivity.findFirst.mockRejectedValue(new Error('Database error'));

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    const mockPurposes = [mockPurpose];

    it('should return all purposes without search', async () => {
      mockPrismaService.purposeOfActivity.findMany.mockResolvedValue(mockPurposes);
      mockPrismaService.purposeOfActivity.count.mockResolvedValue(1);

      const result = await service.findAll();

      expect(result.data).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.count).toBe(1);
      expect(result.message).toContain('Retrieved 1 purposes');
    });

    it('should return filtered purposes with search', async () => {
      mockPrismaService.purposeOfActivity.findMany.mockResolvedValue(mockPurposes);
      mockPrismaService.purposeOfActivity.count.mockResolvedValue(1);

      const result = await service.findAll('demo');

      expect(mockPrismaService.purposeOfActivity.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            {
              name: {
                contains: 'demo',
                mode: 'insensitive',
              },
            },
            {
              description: {
                contains: 'demo',
                mode: 'insensitive',
              },
            },
          ],
        },
        include: {
          _count: {
            select: {
              general_activities: true,
              activities: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });

      expect(result.message).toContain('matching "demo"');
    });
  });

  describe('findOne', () => {
    it('should return a purpose by ID', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(mockPurpose);

      const result = await service.findOne(mockPurpose.id);

      expect(result.id).toBe(mockPurpose.id);
      expect(result.name).toBe(mockPurpose.name);
    });

    it('should throw NotFoundException if purpose not found', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    const updateDto = {
      name: 'Updated Demo',
      description: 'Updated description',
    };

    it('should update a purpose successfully', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(mockPurpose);
      mockPrismaService.purposeOfActivity.findFirst.mockResolvedValue(null);
      mockPrismaService.purposeOfActivity.update.mockResolvedValue({
        ...mockPurpose,
        ...updateDto,
      });

      const result = await service.update(mockPurpose.id, updateDto);

      expect(result.name).toBe(updateDto.name);
      expect(result.description).toBe(updateDto.description);
    });

    it('should throw NotFoundException if purpose not found', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(null);

      await expect(service.update('non-existent-id', updateDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException if name conflicts', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(mockPurpose);
      mockPrismaService.purposeOfActivity.findFirst.mockResolvedValue({
        id: 'different-id',
        name: updateDto.name,
      });

      await expect(service.update(mockPurpose.id, updateDto)).rejects.toThrow(ConflictException);
    });
  });

  describe('remove', () => {
    it('should delete a purpose successfully when not in use', async () => {
      const unusedPurpose = {
        ...mockPurpose,
        _count: {
          general_activities: 0,
          activities: 0,
        },
      };

      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(unusedPurpose);
      mockPrismaService.purposeOfActivity.delete.mockResolvedValue(unusedPurpose);

      const result = await service.remove(mockPurpose.id);

      expect(result.message).toContain('deleted successfully');
      expect(mockPrismaService.purposeOfActivity.delete).toHaveBeenCalledWith({
        where: { id: mockPurpose.id },
      });
    });

    it('should throw ConflictException if purpose is in use', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(mockPurpose);

      await expect(service.remove(mockPurpose.id)).rejects.toThrow(ConflictException);
      expect(mockPrismaService.purposeOfActivity.delete).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if purpose not found', async () => {
      mockPrismaService.purposeOfActivity.findUnique.mockResolvedValue(null);

      await expect(service.remove('non-existent-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUsageStatistics', () => {
    it('should return comprehensive usage statistics', async () => {
      const mockStatistics = [mockPurpose];
      mockPrismaService.purposeOfActivity.findMany.mockResolvedValue(mockStatistics);
      mockPrismaService.generalActivity.count.mockResolvedValue(100);
      mockPrismaService.activity.count.mockResolvedValue(200);

      const result = await service.getUsageStatistics();

      expect(result.total_purposes).toBe(1);
      expect(result.used_purposes).toBe(1);
      expect(result.unused_purposes).toBe(0);
      expect(result.total_general_activities).toBe(100);
      expect(result.total_activities).toBe(200);
      expect(result.most_used_purposes).toHaveLength(1);
    });
  });
});
