import { PartialType } from '@nestjs/swagger';
import { CreatePurposeOfActivityDto } from './create-purpose-of-activity.dto';

/**
 * UpdatePurposeOfActivityDto
 * 
 * Data Transfer Object for updating an existing Purpose of Activity.
 * Extends CreatePurposeOfActivityDto but makes all fields optional,
 * allowing for partial updates of the entity.
 * 
 * This approach ensures consistency between create and update operations
 * while providing flexibility for partial updates.
 */
export class UpdatePurposeOfActivityDto extends PartialType(CreatePurposeOfActivityDto) {
  // All fields from CreatePurposeOfActivityDto are now optional
  // This allows for partial updates where only specific fields are modified
}
