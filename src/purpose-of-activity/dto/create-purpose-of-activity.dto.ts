import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, <PERSON><PERSON>ength, <PERSON><PERSON>ength, IsUUID } from 'class-validator';

/**
 * CreatePurposeOfActivityDto
 * 
 * Data Transfer Object for creating a new Purpose of Activity.
 * Validates input data to ensure data integrity and provides
 * clear API documentation through Swagger decorators.
 */
export class CreatePurposeOfActivityDto {
  /**
   * Name of the activity purpose
   * 
   * This field represents the primary identifier/title for the purpose.
   * Examples: "Product Demo", "Follow Up", "Initial Consultation"
   */
  @ApiProperty({
    description: 'Name of the activity purpose',
    example: 'Product Demo',
    minLength: 2,
    maxLength: 100,
  })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name: string;

  /**
   * Detailed description of the activity purpose
   * 
   * Optional field providing additional context about when and how
   * this purpose should be used for activities.
   */
  @ApiProperty({
    description: 'Detailed description of the activity purpose',
    example: 'Demonstrate product features and capabilities to potential customers',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  /**
   * UUID of the purpose category this purpose belongs to
   *
   * This field is mandatory and establishes the relationship between
   * the purpose and its category for better organization.
   */
  @ApiProperty({
    description: 'UUID of the purpose category this purpose belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString({ message: 'Purpose category ID must be a string' })
  @IsNotEmpty({ message: 'Purpose category ID is required' })
  @IsUUID(4, { message: 'Purpose category ID must be a valid UUID' })
  purpose_category_id: string;
}
