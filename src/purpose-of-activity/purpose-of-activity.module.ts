import { Module } from '@nestjs/common';
import { PurposeOfActivityController } from './purpose-of-activity.controller';
import { PurposeOfActivityService } from './purpose-of-activity.service';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * PurposeOfActivityModule
 * 
 * This module handles all operations related to Purpose of Activity entities.
 * It provides CRUD operations for managing activity purposes that can be
 * associated with general activities and specific activities.
 * 
 * Features:
 * - Create new activity purposes
 * - Read/List activity purposes with optional filtering
 * - Update existing activity purposes
 * - Delete activity purposes (with relationship validation)
 * - Optimized database queries with proper indexing
 */
@Module({
  imports: [PrismaModule],
  controllers: [PurposeOfActivityController],
  providers: [PurposeOfActivityService],
  exports: [PurposeOfActivityService], // Export service for use in other modules
})
export class PurposeOfActivityModule {}
