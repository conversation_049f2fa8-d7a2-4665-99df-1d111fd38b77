import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { 
  CreatePurposeOfActivityDto, 
  UpdatePurposeOfActivityDto,
  PurposeOfActivityResponseDto,
  PurposeOfActivityListResponseDto
} from './dto';

/**
 * PurposeOfActivityService
 * 
 * Service class handling all business logic for Purpose of Activity operations.
 * Implements optimized database queries, proper error handling, and data validation.
 * 
 * Key Features:
 * - Optimized queries with selective field inclusion
 * - Relationship counting for usage statistics
 * - Duplicate name prevention
 * - Cascade delete protection
 * - Comprehensive error handling
 */
@Injectable()
export class PurposeOfActivityService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new Purpose of Activity
   * 
   * Validates that the name is unique before creation to prevent duplicates.
   * Returns the created entity with usage statistics.
   * 
   * @param createDto - Data for creating the purpose
   * @returns Promise<PurposeOfActivityResponseDto> - Created purpose with metadata
   * @throws ConflictException - If name already exists
   * @throws BadRequestException - If validation fails
   */
  async create(createDto: CreatePurposeOfActivityDto): Promise<PurposeOfActivityResponseDto> {
    try {
      // Check if purpose category exists
      const category = await this.prisma.purposeCategory.findUnique({
        where: { id: createDto.purpose_category_id },
        select: { id: true, name: true },
      });

      if (!category) {
        throw new NotFoundException(
          `Purpose category with ID "${createDto.purpose_category_id}" not found`
        );
      }

      // Check for duplicate name (case-insensitive)
      const existingPurpose = await this.prisma.purposeOfActivity.findFirst({
        where: {
          name: {
            equals: createDto.name,
            mode: 'insensitive', // Case-insensitive comparison
          },
        },
        select: { id: true, name: true }, // Only select needed fields for performance
      });

      if (existingPurpose) {
        throw new ConflictException(
          `Purpose of activity with name "${createDto.name}" already exists`
        );
      }

      // Create the new purpose
      const purpose = await this.prisma.purposeOfActivity.create({
        data: {
          name: createDto.name.trim(), // Trim whitespace
          description: createDto.description?.trim() || null,
          purpose_category_id: createDto.purpose_category_id,
        },
        // Include relationship counts and category for immediate response
        include: {
          purpose_category: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              activities: true,
            },
          },
        },
      });

      return this.formatPurposeResponse(purpose);
    } catch (error) {
      if (error instanceof ConflictException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create purpose of activity: ${error.message}`
      );
    }
  }

  /**
   * Retrieves all purposes with optional search filtering
   * 
   * Implements efficient querying with relationship counting.
   * Supports case-insensitive search across name and description fields.
   * 
   * @param search - Optional search term for filtering
   * @returns Promise<PurposeOfActivityListResponseDto> - List of purposes with metadata
   */
  async findAll(search?: string): Promise<PurposeOfActivityListResponseDto> {
    try {
      // Build search conditions if search term provided
      const whereClause = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
              {
                description: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            ],
          }
        : {};

      // Execute queries in parallel for better performance
      const [purposes, total] = await Promise.all([
        this.prisma.purposeOfActivity.findMany({
          where: whereClause,
          include: {
            purpose_category: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            _count: {
              select: {
                general_activities: true,
                activities: true,
              },
            },
          },
          orderBy: {
            name: 'asc', // Alphabetical ordering for better UX
          },
        }),
        this.prisma.purposeOfActivity.count({
          where: whereClause,
        }),
      ]);

      // Format response data
      const formattedPurposes = purposes.map(purpose => this.formatPurposeResponse(purpose));

      return {
        data: formattedPurposes,
        total,
        count: formattedPurposes.length,
        message: `Retrieved ${formattedPurposes.length} purposes of activity successfully${
          search ? ` matching "${search}"` : ''
        }`,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve purposes of activity: ${error.message}`
      );
    }
  }

  /**
   * Retrieves a single purpose by ID
   * 
   * Includes comprehensive relationship data and usage statistics.
   * 
   * @param id - UUID of the purpose to retrieve
   * @returns Promise<PurposeOfActivityResponseDto> - Purpose with metadata
   * @throws NotFoundException - If purpose not found
   */
  async findOne(id: string): Promise<PurposeOfActivityResponseDto> {
    try {
      const purpose = await this.prisma.purposeOfActivity.findUnique({
        where: { id },
        include: {
          purpose_category: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              activities: true,
            },
          },
        },
      });

      if (!purpose) {
        throw new NotFoundException(`Purpose of activity with ID "${id}" not found`);
      }

      return this.formatPurposeResponse(purpose);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve purpose of activity: ${error.message}`
      );
    }
  }

  /**
   * Updates an existing purpose
   * 
   * Validates name uniqueness (excluding current record) and handles partial updates.
   * Only updates fields that are provided in the DTO.
   * 
   * @param id - UUID of the purpose to update
   * @param updateDto - Data for updating the purpose
   * @returns Promise<PurposeOfActivityResponseDto> - Updated purpose with metadata
   * @throws NotFoundException - If purpose not found
   * @throws ConflictException - If name conflicts with another purpose
   */
  async update(id: string, updateDto: UpdatePurposeOfActivityDto): Promise<PurposeOfActivityResponseDto> {
    try {
      // First, verify the purpose exists
      const existingPurpose = await this.prisma.purposeOfActivity.findUnique({
        where: { id },
        select: { id: true, name: true },
      });

      if (!existingPurpose) {
        throw new NotFoundException(`Purpose of activity with ID "${id}" not found`);
      }

      // Check if purpose category exists if being updated
      if (updateDto.purpose_category_id) {
        const category = await this.prisma.purposeCategory.findUnique({
          where: { id: updateDto.purpose_category_id },
          select: { id: true, name: true },
        });

        if (!category) {
          throw new NotFoundException(
            `Purpose category with ID "${updateDto.purpose_category_id}" not found`
          );
        }
      }

      // Check for name conflicts if name is being updated
      if (updateDto.name && updateDto.name !== existingPurpose.name) {
        const conflictingPurpose = await this.prisma.purposeOfActivity.findFirst({
          where: {
            name: {
              equals: updateDto.name,
              mode: 'insensitive',
            },
            NOT: { id }, // Exclude current record
          },
          select: { id: true, name: true },
        });

        if (conflictingPurpose) {
          throw new ConflictException(
            `Purpose of activity with name "${updateDto.name}" already exists`
          );
        }
      }

      // Build update data object with only provided fields
      const updateData: any = {};
      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name.trim();
      }
      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description?.trim() || null;
      }
      if (updateDto.purpose_category_id !== undefined) {
        updateData.purpose_category_id = updateDto.purpose_category_id;
      }

      // Perform the update
      const updatedPurpose = await this.prisma.purposeOfActivity.update({
        where: { id },
        data: updateData,
        include: {
          purpose_category: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              activities: true,
            },
          },
        },
      });

      return this.formatPurposeResponse(updatedPurpose);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update purpose of activity: ${error.message}`
      );
    }
  }

  /**
   * Deletes a purpose of activity
   *
   * Implements cascade protection by checking for related activities before deletion.
   * Prevents deletion if the purpose is currently in use to maintain data integrity.
   *
   * @param id - UUID of the purpose to delete
   * @returns Promise<{ message: string }> - Success confirmation
   * @throws NotFoundException - If purpose not found
   * @throws ConflictException - If purpose is in use by activities
   */
  async remove(id: string): Promise<{ message: string }> {
    try {
      // First, verify the purpose exists and get usage counts
      const purpose = await this.prisma.purposeOfActivity.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              general_activities: true,
              activities: true,
            },
          },
        },
      });

      if (!purpose) {
        throw new NotFoundException(`Purpose of activity with ID "${id}" not found`);
      }

      // Check if purpose is in use
      const totalUsage = purpose._count.general_activities + purpose._count.activities;
      if (totalUsage > 0) {
        throw new ConflictException(
          `Cannot delete purpose "${purpose.name}" as it is currently used by ${totalUsage} activities. ` +
          `Please reassign or remove these activities first.`
        );
      }

      // Safe to delete
      await this.prisma.purposeOfActivity.delete({
        where: { id },
      });

      return {
        message: `Purpose of activity "${purpose.name}" deleted successfully`,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete purpose of activity: ${error.message}`
      );
    }
  }

  /**
   * Gets usage statistics for all purposes
   *
   * Provides comprehensive analytics about purpose usage across the system.
   * Useful for reporting and identifying unused purposes.
   *
   * @returns Promise<any> - Usage statistics and analytics
   */
  async getUsageStatistics(): Promise<any> {
    try {
      const [purposes, totalGeneralActivities, totalActivities] = await Promise.all([
        this.prisma.purposeOfActivity.findMany({
          include: {
            _count: {
              select: {
                general_activities: true,
                activities: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.generalActivity.count(),
        this.prisma.activity.count(),
      ]);

      const usedPurposes = purposes.filter((p: any) =>
        p._count.general_activities > 0 || p._count.activities > 0
      );
      const unusedPurposes = purposes.filter((p: any) =>
        p._count.general_activities === 0 && p._count.activities === 0
      );

      return {
        total_purposes: purposes.length,
        used_purposes: usedPurposes.length,
        unused_purposes: unusedPurposes.length,
        total_general_activities: totalGeneralActivities,
        total_activities: totalActivities,
        most_used_purposes: usedPurposes.slice(0, 5).map((p: any) => ({
          id: p.id,
          name: p.name,
          general_activities_count: p._count.general_activities,
          activities_count: p._count.activities,
          total_usage: p._count.general_activities + p._count.activities,
        })),
        unused_purpose_list: unusedPurposes.map((p: any) => ({
          id: p.id,
          name: p.name,
          description: p.description,
        })),
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve usage statistics: ${error.message}`
      );
    }
  }

  /**
   * Formats a purpose entity for API response
   *
   * Private helper method that transforms database entity into response DTO format.
   * Calculates derived fields and ensures consistent response structure.
   *
   * @param purpose - Raw purpose entity from database
   * @returns PurposeOfActivityResponseDto - Formatted response object
   */
  private formatPurposeResponse(purpose: any): PurposeOfActivityResponseDto {
    const generalActivitiesCount = purpose._count?.general_activities || 0;
    const activitiesCount = purpose._count?.activities || 0;
    const totalActivitiesCount = generalActivitiesCount + activitiesCount;

    return {
      id: purpose.id,
      name: purpose.name,
      description: purpose.description,
      purpose_category_id: purpose.purpose_category_id,
      purpose_category: {
        id: purpose.purpose_category.id,
        name: purpose.purpose_category.name,
        description: purpose.purpose_category.description,
      },
      general_activities_count: generalActivitiesCount,
      activities_count: activitiesCount,
      total_activities_count: totalActivitiesCount,
      is_in_use: totalActivitiesCount > 0,
      created_at: purpose.created_at.toISOString(),
    };
  }
}
