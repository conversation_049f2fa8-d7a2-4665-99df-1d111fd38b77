import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { PurposeOfActivityService } from './purpose-of-activity.service';
import {
  CreatePurposeOfActivityDto,
  UpdatePurposeOfActivityDto,
  PurposeOfActivityResponseDto,
  PurposeOfActivityListResponseDto,
} from './dto';

/**
 * PurposeOfActivityController
 * 
 * RESTful API controller for managing Purpose of Activity entities.
 * Provides comprehensive CRUD operations with proper HTTP status codes,
 * validation, error handling, and API documentation.
 * 
 * Base URL: /api/v1/purpose-of-activities
 * 
 * Features:
 * - Full CRUD operations (Create, Read, Update, Delete)
 * - Search and filtering capabilities
 * - Usage statistics and analytics
 * - Comprehensive error handling
 * - Swagger API documentation
 * - Input validation and sanitization
 */
@ApiTags('Purpose of Activities')
@Controller('purpose-of-activities')
export class PurposeOfActivityController {
  constructor(private readonly purposeOfActivityService: PurposeOfActivityService) {}

  /**
   * Create a new Purpose of Activity
   * POST /purpose-of-activities
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new purpose of activity',
    description: 
      'Creates a new purpose of activity with the provided name and description. ' +
      'The name must be unique (case-insensitive). Returns the created purpose with usage statistics.',
  })
  @ApiCreatedResponse({
    description: 'Purpose of activity created successfully',
    type: PurposeOfActivityResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or validation failed',
  })
  @ApiConflictResponse({
    description: 'Purpose of activity with this name already exists',
  })
  async create(
    @Body(ValidationPipe) createPurposeOfActivityDto: CreatePurposeOfActivityDto,
  ): Promise<PurposeOfActivityResponseDto> {
    return this.purposeOfActivityService.create(createPurposeOfActivityDto);
  }

  /**
   * Get all purposes of activity with optional search
   * GET /purpose-of-activities
   */
  @Get()
  @ApiOperation({
    summary: 'Get all purposes of activity',
    description:
      'Retrieves all purposes of activity with optional search filtering. ' +
      'Search is performed across name and description fields (case-insensitive). ' +
      'Results include usage statistics for each purpose.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term to filter purposes by name or description',
    example: 'demo',
  })
  @ApiOkResponse({
    description: 'Purposes of activity retrieved successfully',
    type: PurposeOfActivityListResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters',
  })
  async findAll(
    @Query('search') search?: string,
  ): Promise<PurposeOfActivityListResponseDto> {
    return this.purposeOfActivityService.findAll(search);
  }

  /**
   * Get usage statistics for all purposes
   * GET /purpose-of-activities/statistics
   */
  @Get('statistics')
  @ApiOperation({
    summary: 'Get usage statistics for purposes of activity',
    description:
      'Provides comprehensive analytics about purpose usage across the system. ' +
      'Includes total counts, most used purposes, and unused purposes list.',
  })
  @ApiOkResponse({
    description: 'Usage statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total_purposes: { type: 'number', example: 25 },
        used_purposes: { type: 'number', example: 18 },
        unused_purposes: { type: 'number', example: 7 },
        total_general_activities: { type: 'number', example: 150 },
        total_activities: { type: 'number', example: 300 },
        most_used_purposes: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              general_activities_count: { type: 'number' },
              activities_count: { type: 'number' },
              total_usage: { type: 'number' },
            },
          },
        },
        unused_purpose_list: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string', nullable: true },
            },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Failed to retrieve statistics',
  })
  async getStatistics() {
    return this.purposeOfActivityService.getUsageStatistics();
  }

  /**
   * Get a specific purpose of activity by ID
   * GET /purpose-of-activities/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get a purpose of activity by ID',
    description:
      'Retrieves a specific purpose of activity by its UUID. ' +
      'Includes comprehensive usage statistics and relationship data.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the purpose of activity to retrieve',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Purpose of activity retrieved successfully',
    type: PurposeOfActivityResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Purpose of activity not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PurposeOfActivityResponseDto> {
    return this.purposeOfActivityService.findOne(id);
  }

  /**
   * Update a purpose of activity
   * PATCH /purpose-of-activities/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update a purpose of activity',
    description:
      'Updates an existing purpose of activity with the provided data. ' +
      'Supports partial updates - only provided fields will be updated. ' +
      'Name uniqueness is validated (excluding the current record).',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the purpose of activity to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Purpose of activity updated successfully',
    type: PurposeOfActivityResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Purpose of activity not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or UUID format',
  })
  @ApiConflictResponse({
    description: 'Purpose of activity with this name already exists',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updatePurposeOfActivityDto: UpdatePurposeOfActivityDto,
  ): Promise<PurposeOfActivityResponseDto> {
    return this.purposeOfActivityService.update(id, updatePurposeOfActivityDto);
  }

  /**
   * Delete a purpose of activity
   * DELETE /purpose-of-activities/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete a purpose of activity',
    description:
      'Deletes a purpose of activity by its UUID. ' +
      'Deletion is prevented if the purpose is currently in use by any activities ' +
      'to maintain data integrity. Returns confirmation message on success.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the purpose of activity to delete',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Purpose of activity deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Purpose of activity "Product Demo" deleted successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Purpose of activity not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format',
  })
  @ApiConflictResponse({
    description: 'Cannot delete purpose as it is currently in use by activities',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<{ message: string }> {
    return this.purposeOfActivityService.remove(id);
  }
}
