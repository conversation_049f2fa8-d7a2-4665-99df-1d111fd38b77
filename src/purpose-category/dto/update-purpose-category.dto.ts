import { PartialType } from '@nestjs/swagger';
import { CreatePurposeCategoryDto } from './create-purpose-category.dto';

/**
 * UpdatePurposeCategoryDto
 * 
 * Data Transfer Object for updating an existing Purpose Category.
 * Extends CreatePurposeCategoryDto but makes all fields optional,
 * allowing for partial updates of the entity.
 * 
 * This approach ensures consistency between create and update operations
 * while providing flexibility for partial updates.
 */
export class UpdatePurposeCategoryDto extends PartialType(CreatePurposeCategoryDto) {
  // All fields from CreatePurposeCategoryDto are now optional
  // This allows for partial updates where only specific fields are modified
}
