import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, MaxLength, MinLength } from 'class-validator';

/**
 * CreatePurposeCategoryDto
 * 
 * Data Transfer Object for creating a new Purpose Category.
 * Validates input data to ensure data integrity and provides
 * clear API documentation through Swagger decorators.
 */
export class CreatePurposeCategoryDto {
  /**
   * Name of the purpose category
   * 
   * This field represents the primary identifier/title for the category.
   * Examples: "Sales Activities", "Customer Support", "Marketing"
   */
  @ApiProperty({
    description: 'Name of the purpose category',
    example: 'Sales Activities',
    minLength: 2,
    maxLength: 100,
  })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name: string;

  /**
   * Detailed description of the purpose category
   * 
   * Optional field providing additional context about what types
   * of purposes belong to this category.
   */
  @ApiProperty({
    description: 'Detailed description of the purpose category',
    example: 'Category for all sales-related activity purposes including demos, follow-ups, and closing activities',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;
}
