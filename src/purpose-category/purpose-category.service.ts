import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { 
  CreatePurposeCategoryDto, 
  UpdatePurposeCategoryDto,
  PurposeCategoryResponseDto,
  PurposeCategoryListResponseDto
} from './dto';

/**
 * PurposeCategoryService
 * 
 * Service class handling all business logic for Purpose Category operations.
 * Implements optimized database queries, proper error handling, and data validation.
 * 
 * Key Features:
 * - Optimized queries with selective field inclusion
 * - Relationship counting for usage statistics
 * - Duplicate name prevention
 * - Cascade delete protection
 * - Comprehensive error handling
 */
@Injectable()
export class PurposeCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new Purpose Category
   * 
   * Validates that the name is unique before creation to prevent duplicates.
   * Returns the created entity with usage statistics.
   * 
   * @param createDto - Data for creating the category
   * @returns Promise<PurposeCategoryResponseDto> - Created category with metadata
   * @throws ConflictException - If name already exists
   * @throws BadRequestException - If validation fails
   */
  async create(createDto: CreatePurposeCategoryDto): Promise<PurposeCategoryResponseDto> {
    try {
      // Check for duplicate name (case-insensitive)
      const existingCategory = await this.prisma.purposeCategory.findFirst({
        where: {
          name: {
            equals: createDto.name,
            mode: 'insensitive', // Case-insensitive comparison
          },
        },
        select: { id: true, name: true }, // Only select needed fields for performance
      });

      if (existingCategory) {
        throw new ConflictException(
          `Purpose category with name "${createDto.name}" already exists`
        );
      }

      // Create the new category
      const category = await this.prisma.purposeCategory.create({
        data: {
          name: createDto.name.trim(), // Trim whitespace
          description: createDto.description?.trim() || null,
        },
        // Include relationship counts for immediate response
        include: {
          _count: {
            select: {
              purpose_of_activities: true,
            },
          },
        },
      });

      return this.formatCategoryResponse(category);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create purpose category: ${error.message}`
      );
    }
  }

  /**
   * Retrieves all categories with optional search filtering
   * 
   * Implements efficient querying with relationship counting.
   * Supports case-insensitive search across name and description fields.
   * 
   * @param search - Optional search term for filtering
   * @returns Promise<PurposeCategoryListResponseDto> - List of categories with metadata
   */
  async findAll(search?: string): Promise<PurposeCategoryListResponseDto> {
    try {
      // Build search conditions if search term provided
      const whereClause = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
              {
                description: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            ],
          }
        : {};

      // Execute queries in parallel for better performance
      const [categories, total] = await Promise.all([
        this.prisma.purposeCategory.findMany({
          where: whereClause,
          include: {
            _count: {
              select: {
                purpose_of_activities: true,
              },
            },
          },
          orderBy: {
            name: 'asc', // Alphabetical ordering for better UX
          },
        }),
        this.prisma.purposeCategory.count({
          where: whereClause,
        }),
      ]);

      // Format response data
      const formattedCategories = categories.map(category => this.formatCategoryResponse(category));

      return {
        data: formattedCategories,
        total,
        count: formattedCategories.length,
        message: `Retrieved ${formattedCategories.length} purpose categories successfully${
          search ? ` matching "${search}"` : ''
        }`,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve purpose categories: ${error.message}`
      );
    }
  }

  /**
   * Retrieves a single category by ID
   * 
   * Includes comprehensive relationship data and usage statistics.
   * 
   * @param id - UUID of the category to retrieve
   * @returns Promise<PurposeCategoryResponseDto> - Category with metadata
   * @throws NotFoundException - If category not found
   */
  async findOne(id: string): Promise<PurposeCategoryResponseDto> {
    try {
      const category = await this.prisma.purposeCategory.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              purpose_of_activities: true,
            },
          },
        },
      });

      if (!category) {
        throw new NotFoundException(`Purpose category with ID "${id}" not found`);
      }

      return this.formatCategoryResponse(category);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve purpose category: ${error.message}`
      );
    }
  }

  /**
   * Updates an existing category
   * 
   * Validates name uniqueness (excluding current record) and handles partial updates.
   * Only updates fields that are provided in the DTO.
   * 
   * @param id - UUID of the category to update
   * @param updateDto - Data for updating the category
   * @returns Promise<PurposeCategoryResponseDto> - Updated category with metadata
   * @throws NotFoundException - If category not found
   * @throws ConflictException - If name conflicts with another category
   */
  async update(id: string, updateDto: UpdatePurposeCategoryDto): Promise<PurposeCategoryResponseDto> {
    try {
      // First, verify the category exists
      const existingCategory = await this.prisma.purposeCategory.findUnique({
        where: { id },
        select: { id: true, name: true },
      });

      if (!existingCategory) {
        throw new NotFoundException(`Purpose category with ID "${id}" not found`);
      }

      // Check for name conflicts if name is being updated
      if (updateDto.name && updateDto.name !== existingCategory.name) {
        const conflictingCategory = await this.prisma.purposeCategory.findFirst({
          where: {
            name: {
              equals: updateDto.name,
              mode: 'insensitive',
            },
            NOT: { id }, // Exclude current record
          },
          select: { id: true, name: true },
        });

        if (conflictingCategory) {
          throw new ConflictException(
            `Purpose category with name "${updateDto.name}" already exists`
          );
        }
      }

      // Build update data object with only provided fields
      const updateData: any = {};
      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name.trim();
      }
      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description?.trim() || null;
      }

      // Perform the update
      const updatedCategory = await this.prisma.purposeCategory.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              purpose_of_activities: true,
            },
          },
        },
      });

      return this.formatCategoryResponse(updatedCategory);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update purpose category: ${error.message}`
      );
    }
  }

  /**
   * Deletes a purpose category
   *
   * Implements cascade protection by checking for related purposes before deletion.
   * Prevents deletion if the category is currently in use to maintain data integrity.
   *
   * @param id - UUID of the category to delete
   * @returns Promise<{ message: string }> - Success confirmation
   * @throws NotFoundException - If category not found
   * @throws ConflictException - If category is in use by purposes
   */
  async remove(id: string): Promise<{ message: string }> {
    try {
      // First, verify the category exists and get usage counts
      const category = await this.prisma.purposeCategory.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              purpose_of_activities: true,
            },
          },
        },
      });

      if (!category) {
        throw new NotFoundException(`Purpose category with ID "${id}" not found`);
      }

      // Check if category is in use
      const purposesCount = category._count.purpose_of_activities;
      if (purposesCount > 0) {
        throw new ConflictException(
          `Cannot delete category "${category.name}" as it is currently used by ${purposesCount} purposes. ` +
          `Please reassign or remove these purposes first.`
        );
      }

      // Safe to delete
      await this.prisma.purposeCategory.delete({
        where: { id },
      });

      return {
        message: `Purpose category "${category.name}" deleted successfully`,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete purpose category: ${error.message}`
      );
    }
  }

  /**
   * Gets usage statistics for all categories
   *
   * Provides comprehensive analytics about category usage across the system.
   * Useful for reporting and identifying unused categories.
   *
   * @returns Promise<any> - Usage statistics and analytics
   */
  async getUsageStatistics(): Promise<any> {
    try {
      const [categories, totalPurposes] = await Promise.all([
        this.prisma.purposeCategory.findMany({
          include: {
            _count: {
              select: {
                purpose_of_activities: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.purposeOfActivity.count(),
      ]);

      const usedCategories = categories.filter((c: any) => c._count.purpose_of_activities > 0);
      const unusedCategories = categories.filter((c: any) => c._count.purpose_of_activities === 0);

      return {
        total_categories: categories.length,
        used_categories: usedCategories.length,
        unused_categories: unusedCategories.length,
        total_purposes: totalPurposes,
        most_used_categories: usedCategories.slice(0, 5).map((c: any) => ({
          id: c.id,
          name: c.name,
          purposes_count: c._count.purpose_of_activities,
        })),
        unused_category_list: unusedCategories.map((c: any) => ({
          id: c.id,
          name: c.name,
          description: c.description,
        })),
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve usage statistics: ${error.message}`
      );
    }
  }

  /**
   * Formats a category entity for API response
   *
   * Private helper method that transforms database entity into response DTO format.
   * Calculates derived fields and ensures consistent response structure.
   *
   * @param category - Raw category entity from database
   * @returns PurposeCategoryResponseDto - Formatted response object
   */
  private formatCategoryResponse(category: any): PurposeCategoryResponseDto {
    const purposesCount = category._count?.purpose_of_activities || 0;

    return {
      id: category.id,
      name: category.name,
      description: category.description,
      purposes_count: purposesCount,
      is_in_use: purposesCount > 0,
      created_at: category.created_at.toISOString(),
    };
  }
}
