import { Module } from '@nestjs/common';
import { PurposeCategoryController } from './purpose-category.controller';
import { PurposeCategoryService } from './purpose-category.service';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * PurposeCategoryModule
 * 
 * This module handles all operations related to Purpose Category entities.
 * Purpose categories are used to organize and categorize different types
 * of activity purposes, providing better organization and filtering capabilities.
 * 
 * Features:
 * - Create new purpose categories
 * - Read/List purpose categories with optional filtering
 * - Update existing purpose categories
 * - Delete purpose categories (with relationship validation)
 * - Optimized database queries with proper indexing
 * - Relationship management with PurposeOfActivity entities
 */
@Module({
  imports: [PrismaModule],
  controllers: [PurposeCategoryController],
  providers: [PurposeCategoryService],
  exports: [PurposeCategoryService], // Export service for use in other modules
})
export class PurposeCategoryModule {}
