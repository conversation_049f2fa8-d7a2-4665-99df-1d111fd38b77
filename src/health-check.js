#!/usr/bin/env node

/**
 * Simple health check script for Docker containers
 * Checks if the application is responding on the expected port
 */

const http = require('http');

const port = process.env.PORT || 3000;
const host = process.env.HOST || 'localhost';

const options = {
  host: host,
  port: port,
  path: '/api/v1/health',
  method: 'GET',
  timeout: 2000,
};

const request = http.request(options, (res) => {
  if (res.statusCode === 200) {
    console.log('Health check passed');
    process.exit(0);
  } else {
    console.log(`Health check failed with status code: ${res.statusCode}`);
    process.exit(1);
  }
});

request.on('error', (err) => {
  console.log(`Health check failed: ${err.message}`);
  process.exit(1);
});

request.on('timeout', () => {
  console.log('Health check timed out');
  request.destroy();
  process.exit(1);
});

request.end();
