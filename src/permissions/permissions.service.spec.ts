import { Test, TestingModule } from '@nestjs/testing';
import { PermissionsService } from './permissions.service';
import { PrismaService } from '../prisma/prisma.service';

describe('PermissionsService', () => {
  let service: PermissionsService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    permission: {
      findFirst: jest.fn(),
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    role: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PermissionsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<PermissionsService>(PermissionsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserPermissions', () => {
    it('should return user permissions', async () => {
      const mockUser = {
        id: 'user-123',
        role: {
          role_permissions: [
            {
              permission: {
                id: 'users.create',
                name: 'Create Users',
                description: 'Can create users',
              },
            },
            {
              permission: {
                id: 'users.read',
                name: 'Read Users',
                description: 'Can view users',
              },
            },
          ],
        },
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);

      const result = await service.getUserPermissions('user-123');

      expect(result).toEqual([
        { id: 'users.create', name: 'Create Users', description: 'Can create users' },
        { id: 'users.read', name: 'Read Users', description: 'Can view users' },
      ]);
      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        include: {
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });
    });

    it('should return empty array when user has no role', async () => {
      const mockUser = {
        id: 'user-123',
        role: null,
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);

      const result = await service.getUserPermissions('user-123');

      expect(result).toEqual([]);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(service.getUserPermissions('user-123')).rejects.toThrow('User not found');
    });
  });
});
