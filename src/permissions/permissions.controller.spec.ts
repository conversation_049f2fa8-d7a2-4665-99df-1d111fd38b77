import { Test, TestingModule } from '@nestjs/testing';
import { PermissionsController } from './permissions.controller';
import { PermissionsService } from './permissions.service';

describe('PermissionsController', () => {
  let controller: PermissionsController;
  let service: PermissionsService;

  const mockPermissionsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getPermissionRoles: jest.fn(),
    getUserPermissions: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PermissionsController],
      providers: [
        {
          provide: PermissionsService,
          useValue: mockPermissionsService,
        },
      ],
    }).compile();

    controller = module.get<PermissionsController>(PermissionsController);
    service = module.get<PermissionsService>(PermissionsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getMyPermissions', () => {
    it('should return user permissions', async () => {
      const mockPermissions = [
        { id: 'users.create', name: 'Create Users', description: 'Can create users' },
        { id: 'users.read', name: 'Read Users', description: 'Can view users' },
      ];
      const mockRequest = { user: { id: 'user-123' } };

      mockPermissionsService.getUserPermissions.mockResolvedValue(mockPermissions);

      const result = await controller.getMyPermissions(mockRequest);

      expect(result).toEqual(mockPermissions);
      expect(mockPermissionsService.getUserPermissions).toHaveBeenCalledWith('user-123');
    });
  });
});
