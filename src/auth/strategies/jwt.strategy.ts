import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
  ) {
    const jwtSecret = configService.get<string>('JWT_SECRET') || 'fallback-secret-key';
    if (!configService.get<string>('JWT_SECRET')) {
      console.warn('⚠️  JWT_SECRET not found, using fallback secret. Set JWT_SECRET in .env for production.');
    }
    
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtSecret,
    });
  }

  async validate(payload: any) {
    const user = await this.prisma.user.findUnique({
      where: { id: payload.sub },
      select: {
        id: true,
        name: true,
        email: true,
        rm_code: true,
        role_id: true,
        branch_id: true,
        role: {
          select: {
            id: true,
            name: true,
            role_permissions: {
              select: {
                permission: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Extract permissions array for easy access (using permission IDs)
    const permissions = user.role?.role_permissions.map(rp => rp.permission.id) || [];

    return {
      ...user,
      permissions,
    };
  }
} 