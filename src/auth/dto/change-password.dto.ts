import { ApiProperty } from '@nestjs/swagger';
import { IsString, MinLength } from 'class-validator';

export class ChangePasswordDto {
  @ApiProperty({
    description: 'Current password',
    example: 'currentPassword123',
  })
  @IsString()
  @MinLength(8)
  currentPassword: string;

  @ApiProperty({
    description: 'New password',
    example: 'newPassword123',
  })
  @IsString()
  @MinLength(8)
  newPassword: string;
}

export class PasswordStatusResponseDto {
  @ApiProperty({
    description: 'Email of the logged in user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Last password update timestamp',
    example: '2025-08-11T08:36:57.915Z',
    nullable: true,
  })
  lastPasswordUpdate: Date | null;

  @ApiProperty({
    description: 'Available MFA methods',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        method: { type: 'string', enum: ['EMAIL', 'SMS'] },
        contact: { type: 'string', nullable: true },
        verified: { type: 'boolean' },
        enabled: { type: 'boolean' },
        added_at: { type: 'string', format: 'date-time', nullable: true },
        last_used_at: { type: 'string', format: 'date-time', nullable: true },
        active: { type: 'boolean' },
      },
    },
  })
  mfaMethods: Array<{
    id: string;
    method: 'EMAIL' | 'SMS';
    contact: string | null;
    verified: boolean;
    enabled: boolean;
    added_at: Date | null;
    last_used_at: Date | null;
    active: boolean;
  }>;
}
