import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean } from 'class-validator';

export class EnableMfaDto {
  @ApiProperty({
    description: 'ID of the MFA method',
    example: 'abc123-def456',
  })
  @IsString()
  mfaMethodId: string;

  @ApiProperty({
    description: 'Whether to enable or disable the method',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;
}

export class UpdatePhoneDto {
  @ApiProperty({
    description: 'Phone number for SMS verification',
    example: '+254712345678',
  })
  @IsString()
  phoneNumber: string;

  @ApiProperty({
    description: 'ID of the SMS MFA method',
    example: 'abc123-def456',
  })
  @IsString()
  methodId: string;
}

export class VerifyPhoneDto {
  @ApiProperty({
    description: 'Verification code',
    example: '123456',
  })
  @IsString()
  code: string;
}

