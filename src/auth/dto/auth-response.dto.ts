import { ApiProperty } from '@nestjs/swagger';
import { PermissionResponseDto } from '../../permissions/dto/permission-response.dto';

export class AuthResponseDto {
  @ApiProperty({
    description: 'Access token for API authentication',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token for getting new access tokens',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 900,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'User information',
  })
  user: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
    role: {
      id: string;
      name: string;
    };
    permissions: PermissionResponseDto[];
  };

  @ApiProperty({
    description: 'Enabled MFA methods',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        method: { type: 'string', enum: ['EMAIL', 'SMS'] },
        contact: { type: 'string', nullable: true },
        verified: { type: 'boolean' },
        enabled: { type: 'boolean' },
        added_at: { type: 'string', format: 'date-time', nullable: true },
        last_used_at: { type: 'string', format: 'date-time', nullable: true },
        active: { type: 'boolean' },
      },
    },
    required: false,
  })
  mfaMethods?: Array<{
    id: string;
    method: 'EMAIL' | 'SMS';
    contact: string | null;
    verified: boolean;
    enabled: boolean;
    added_at: Date | null;
    last_used_at: Date | null;
    active: boolean;
  }>;
}

export class TokenResponseDto {
  @ApiProperty({
    description: 'New access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 900,
  })
  expiresIn: number;
}

export class SignupResponseDto {
  @ApiProperty({
    description: 'User information',
  })
  user: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
    role: {
      id: string;
      name: string;
    };
    permissions: PermissionResponseDto[];
  };

  @ApiProperty({
    description: 'Success message',
    example: 'User created successfully',
  })
  message: string;
}
