import { ApiProperty } from '@nestjs/swagger';

export class MfaMethodResponseDto {
  @ApiProperty({
    description: 'Email of the logged in user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Available MFA methods',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        method: { type: 'string', enum: ['EMAIL', 'SMS'] },
        contact: { type: 'string', nullable: true },
        verified: { type: 'boolean' },
        enabled: { type: 'boolean' },
        added_at: { type: 'string', format: 'date-time', nullable: true },
        last_used_at: { type: 'string', format: 'date-time', nullable: true },
        active: { type: 'boolean' },
      },
    },
  })
  mfaMethods: Array<{
    id: string;
    method: 'EMAIL' | 'SMS';
    contact: string | null;
    verified: boolean;
    enabled: boolean;
    added_at: Date | null;
    last_used_at: Date | null;
    active: boolean;
  }>;
}
