import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class SendMfaCodeDto {
  @ApiProperty({
    description: 'ID of the user MFA method',
    example: 'abc123-def456',
  })
  @IsString()
  @IsNotEmpty()
  methodId: string;
}

export class VerifyMfaCodeDto {
  @ApiProperty({
    description: 'ID of the user MFA method',
    example: 'abc123-def456',
  })
  @IsString()
  @IsNotEmpty()
  methodId: string;

  @ApiProperty({
    description: 'OTP code to verify',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  code: string;
}
