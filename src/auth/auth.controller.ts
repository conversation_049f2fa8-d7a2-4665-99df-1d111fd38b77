import {
  Controller,
  Post,
  Get,
  Body,
  HttpStatus,
  HttpCode,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { SignupDto } from './dto/signup.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetVerificationDto } from './dto/reset-verification.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { PasswordStatusResponseDto } from './dto/password-status.dto';
import { MfaMethodResponseDto } from './dto/mfa-status.dto';
import { SendMfaCodeDto, VerifyMfaCodeDto } from './dto/mfa.dto';
import { EnableMfaDto } from './dto/enable-mfa.dto';
import {
  AuthResponseDto,
  TokenResponseDto,
  SignupResponseDto,
} from './dto/auth-response.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * User login endpoint
   * POST /auth/login
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description:
      'Authenticates user credentials and returns access and refresh tokens',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Login successful',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    return this.authService.login(loginDto);
  }

  /**
   * User signup endpoint
   * POST /auth/signup
   */
  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'User signup',
    description: 'Creates a new user account with auto-generated RM code',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User created successfully',
    type: SignupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'User already exists',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async signup(@Body() signupDto: SignupDto): Promise<SignupResponseDto> {
    return this.authService.signup(signupDto);
  }

  /**
   * Refresh access token endpoint
   * POST /auth/token/refresh
   */
  @Post('token/refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh access token',
    description: 'Uses refresh token to get a new access token',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token refreshed successfully',
    type: TokenResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or expired refresh token',
  })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<TokenResponseDto> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  /**
   * User logout endpoint
   * POST /auth/logout
   */
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User logout',
    description: 'Invalidates the refresh token',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Logout successful',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  async logout(@Request() req: any): Promise<{ message: string }> {
    // Note: In a real implementation, you might want to pass the refresh token
    // in the request body or headers for logout
    return { message: 'Logout successful' };
  }

  /**
   * Forgot password endpoint
   * POST /auth/forgot-password
   */
  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Forgot password',
    description: 'Sends OTP to user email for password reset',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OTP sent successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'OTP has been sent to your email address',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<{ message: string }> {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  /**
   * Reset verification endpoint
   * POST /auth/reset-verification
   */
  @Post('reset-verification')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify OTP for password reset',
    description: 'Verifies the OTP code for password reset',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OTP verification result',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'OTP verified successfully',
        },
        valid: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  async resetVerification(
    @Body() resetVerificationDto: ResetVerificationDto,
  ): Promise<{ message: string; valid: boolean }> {
    return this.authService.resetVerification(resetVerificationDto);
  }

  /**
   * Reset password endpoint
   * POST /auth/reset-password
   */
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reset password',
    description: 'Resets user password using verified OTP',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Password reset successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid or expired token',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<{ message: string }> {
    return this.authService.resetPassword(resetPasswordDto);
  }

  /**
   * Change password endpoint
   * POST /auth/change-password
   */
  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Change user password',
    description: 'Changes the password of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password changed successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Password changed successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Current password is incorrect',
  })
  async changePassword(
    @Request() req: any,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    return this.authService.changePassword(
      req.user.id,
      changePasswordDto.currentPassword,
      changePasswordDto.newPassword,
    );
  }

  /**
   * Get password status endpoint
   * GET /auth/change-password
   */
  @Get('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get password status',
    description:
      'Returns last password update timestamp for the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password status retrieved successfully',
    type: PasswordStatusResponseDto,
  })
  async getPasswordStatus(@Request() req: any) {
    return this.authService.getPasswordStatus(req.user.id);
  }

  /**
   * Get MFA status endpoint
   * GET /auth/mfa
   */
  @Get('mfa')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get MFA status',
    description: 'Returns MFA methods and status for the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'MFA status retrieved successfully',
    type: MfaMethodResponseDto,
  })
  async getMfaStatus(@Request() req: any) {
    return this.authService.getMfaStatus(req.user.id);
  }

  /**
   * Enable/disable MFA method
   * POST /auth/mfa
   */
  @Post('mfa')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Enable or disable MFA method',
    description: 'Enables or disables a specific MFA method for the user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'MFA method updated',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'MFA method enabled',
        },
      },
    },
  })
  async enableMfa(
    @Request() req: any,
    @Body() enableMfaDto: EnableMfaDto,
  ): Promise<{ message: string }> {
    return this.authService.enableMfa(
      req.user.id,
      enableMfaDto.mfaMethodId,
      enableMfaDto.enabled,
    );
  }

  /**
   * Send MFA verification code
   * POST /auth/mfa/send-code
   */
  @Post('mfa/send-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send MFA verification code',
    description: 'Sends a verification code for the specified MFA method',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Code sent successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Verification code has been sent',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'MFA method not found or not enabled',
  })
  async sendMfaCode(
    @Body() sendMfaCodeDto: SendMfaCodeDto,
  ): Promise<{ message: string }> {
    return this.authService.sendMfaCode(sendMfaCodeDto.methodId);
  }

  /**
   * Verify MFA code
   * POST /auth/mfa/verify-code
   */
  @Post('mfa/verify-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify MFA code',
    description: 'Verifies the provided MFA verification code',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Code verification successful',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Verification successful',
        },
        verified: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid or expired verification code',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Invalid verification code',
        },
        error: {
          type: 'string',
          example: 'Bad Request',
        },
        statusCode: {
          type: 'number',
          example: 400,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'MFA method not found or not enabled',
  })
  async verifyMfaCode(
    @Body() verifyMfaCodeDto: VerifyMfaCodeDto,
  ): Promise<{ message: string; verified: boolean }> {
    return this.authService.verifyMfaCode(
      verifyMfaCodeDto.methodId,
      verifyMfaCodeDto.code,
    );
  }
}
