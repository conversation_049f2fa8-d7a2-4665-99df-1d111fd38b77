import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../common/services/email.service';
import { PermissionsService } from '../permissions/permissions.service';
import { LoginDto } from './dto/login.dto';
import { SignupDto } from './dto/signup.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetVerificationDto } from './dto/reset-verification.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import {
  AuthResponseDto,
  TokenResponseDto,
  SignupResponseDto,
} from './dto/auth-response.dto';
import {
  generateOtp,
  generateOtpExpiry,
  formatTimestamp,
  isOtpExpired,
} from '../common/utils/otp.utils';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private emailService: EmailService,
    private permissionsService: PermissionsService,
  ) {
    // Check if JWT secrets are configured
    const jwtSecret = this.configService.get<string>('JWT_SECRET');
    const jwtRefreshSecret =
      this.configService.get<string>('JWT_REFRESH_SECRET');

    if (!jwtSecret || !jwtRefreshSecret) {
      console.warn(
        '⚠️  JWT_SECRET and JWT_REFRESH_SECRET environment variables are not set.',
      );
      console.warn('   Please set them in your .env file for production use.');
    }
  }

  /**
   * Authenticates a user and returns access and refresh tokens
   */
  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { email: loginDto.email },
      include: {
        role: true,
        branch: true,
      },
    });

    if (!user || !user.password) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(
      loginDto.password,
      user.password,
    );
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login
    await this.prisma.user.update({
      where: { id: user.id },
      data: { last_login: new Date() },
    });

    const tokens = await this.generateTokens(user.id);

    // Get enabled MFA methods for the user
    const userMfaMethods = await this.prisma.userMfaMethod.findMany({
      where: {
        user_id: user.id,
        enabled: true,
      },
      include: {
        method: true,
      },
    });

    // Map MFA methods to response format
    const mfaMethods = userMfaMethods.map((um) => ({
      id: um.id, // Using the association record ID
      method: um.method.method,
      contact: um.contact,
      verified: um.verified,
      enabled: um.enabled,
      added_at: um.added_at,
      last_used_at: um.last_used_at,
      active: um.method.active,
    }));

    // Get user permissions
    const permissions = await this.permissionsService.getUserPermissions(user.id);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: 900, // 15 minutes
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        rm_code: user.rm_code,
        role: {
          id: user.role.id,
          name: user.role.name,
        },
        permissions,
      },
      ...(mfaMethods.length > 0 ? { mfaMethods } : {}),
    };
  }

  /**
   * Creates a new user account
   */
  async signup(signupDto: SignupDto): Promise<SignupResponseDto> {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: {
        email:
          signupDto.name.toLowerCase().replace(/\s+/g, '') + '@example.com',
      },
    });

    if (existingUser) {
      throw new ConflictException('User already exists');
    }

    // Generate unique RM code
    const rmCode = await this.generateUniqueRmCode();

    // Hash password
    const hashedPassword = await bcrypt.hash(signupDto.password, 10);

    // Get default role and branch (you may want to adjust this logic)
    const defaultRole = await this.prisma.role.findFirst();
    const defaultBranch = await this.prisma.branch.findFirst();

    if (!defaultRole || !defaultBranch) {
      throw new BadRequestException('Default role or branch not found');
    }

    // Create user
    const user = await this.prisma.user.create({
      data: {
        name: signupDto.name,
        email:
          signupDto.name.toLowerCase().replace(/\s+/g, '') + '@example.com',
        password: hashedPassword,
        rm_code: rmCode,
        role_id: defaultRole.id,
        branch_id: defaultBranch.id,
      },
      include: {
        role: true,
        branch: true,
      },
    });

    // Get user permissions
    const permissions = await this.permissionsService.getUserPermissions(user.id);

    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        rm_code: user.rm_code,
        role: {
          id: user.role.id,
          name: user.role.name,
        },
        permissions,
      },
      message: 'User created successfully',
    };
  }

  /**
   * Refreshes access token using refresh token
   */
  async refreshToken(
    refreshTokenDto: RefreshTokenDto,
  ): Promise<TokenResponseDto> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshTokenDto.refreshToken, {
        secret:
          this.configService.get<string>('JWT_REFRESH_SECRET') ||
          'fallback-refresh-secret-key',
      });

      // Check if refresh token exists in database
      const storedToken = await this.prisma.refreshToken.findUnique({
        where: { token: refreshTokenDto.refreshToken },
        include: { user: true },
      });

      if (!storedToken || storedToken.expires_at < new Date()) {
        throw new UnauthorizedException('Invalid or expired refresh token');
      }

      // Generate new access token
      const accessToken = this.jwtService.sign(
        { sub: storedToken.user.id, email: storedToken.user.email },
        {
          secret:
            this.configService.get<string>('JWT_SECRET') ||
            'fallback-secret-key',
          expiresIn: '15m',
        },
      );

      return {
        accessToken,
        expiresIn: 900, // 15 minutes
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Logs out a user by invalidating refresh token
   */
  async logout(refreshToken: string): Promise<void> {
    await this.prisma.refreshToken.deleteMany({
      where: { token: refreshToken },
    });
  }

  /**
   * Generates access and refresh tokens
   */
  private async generateTokens(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        { sub: user.id, email: user.email },
        {
          secret:
            this.configService.get<string>('JWT_SECRET') ||
            'fallback-secret-key',
          expiresIn: '15m',
        },
      ),
      this.jwtService.signAsync(
        { sub: user.id, email: user.email },
        {
          secret:
            this.configService.get<string>('JWT_REFRESH_SECRET') ||
            'fallback-refresh-secret-key',
          expiresIn: '7d',
        },
      ),
    ]);

    // Store refresh token in database
    await this.prisma.refreshToken.create({
      data: {
        token: refreshToken,
        user_id: user.id,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      },
    });

    return { accessToken, refreshToken };
  }

  /**
   * Generates a unique RM code
   */
  private async generateUniqueRmCode(): Promise<string> {
    let rmCode = '';
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 100;

    while (!isUnique && attempts < maxAttempts) {
      // Generate RM code with format RM####
      const randomNumber = Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, '0');
      rmCode = `RM${randomNumber}`;

      // Check if code already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { rm_code: rmCode },
      });

      if (!existingUser) {
        isUnique = true;
      }

      attempts++;
    }

    if (!isUnique) {
      throw new Error('Unable to generate unique RM code');
    }

    return rmCode;
  }

  /**
   * Initiates password reset process by sending OTP to user's email
   */
  async forgotPassword(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<{ message: string }> {
    const { email } = forgotPasswordDto;

    // Find user by email
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User with this email does not exist');
    }

    // Generate OTP
    const otpCode = generateOtp();
    const expiresAt = generateOtpExpiry(5); // 5 minutes

    // Save OTP to database
    await this.prisma.otp.create({
      data: {
        code: otpCode,
        purpose: 'reset_password',
        expires_at: expiresAt,
        user_id: user.id,
      },
    });

    // Send OTP email
    await this.emailService.sendOtpEmail(user.email, {
      name: user.name,
      otp: otpCode,
      otp_validity_minutes: 5,
      timestamp: formatTimestamp(),
      app_name: this.configService.get<string>('APP_NAME') || 'KB Tracker',
    });

    return {
      message: 'OTP has been sent to your email address',
    };
  }

  /**
   * Verifies OTP for password reset
   */
  async resetVerification(
    resetVerificationDto: ResetVerificationDto,
  ): Promise<{ message: string; valid: boolean }> {
    const { email, otp } = resetVerificationDto;

    // Find user by email
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User with this email does not exist');
    }

    // Find valid OTP
    const otpRecord = await this.prisma.otp.findFirst({
      where: {
        user_id: user.id,
        code: otp,
        purpose: 'reset_password',
        used: false,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    if (!otpRecord) {
      throw new BadRequestException({
        message: 'Invalid OTP code',
        valid: false,
      });
    }

    // Check if OTP has expired
    if (isOtpExpired(otpRecord.expires_at)) {
      throw new BadRequestException({
        message: 'OTP has expired',
        valid: false,
      });
    }

    return {
      message: 'OTP verified successfully',
      valid: true,
    };
  }

  /**
   * Resets user password using verified OTP
   */
  async getMfaStatus(userId: string): Promise<{
    email: string;
    mfaMethods: Array<{
      id: string;
      method: 'EMAIL' | 'SMS';
      contact: string | null;
      verified: boolean;
      enabled: boolean;
      added_at: Date | null;
      last_used_at: Date | null;
      active: boolean;
    }>;
  }> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get all MFA methods
    const allMfaMethods = await this.prisma.mfaMethod.findMany({
      where: { active: true },
    });

    // Get user's MFA methods
    const userMfaMethods = await this.prisma.userMfaMethod.findMany({
      where: { user_id: userId },
      include: { method: true },
    });

    // Map MFA methods to response format
    const mfaMethods = allMfaMethods.map((method) => {
      const userMethod = userMfaMethods.find(
        (um) => um.method_id === method.id,
      );
      return {
        id: method.id,
        method: method.method,
        description: method.description,
        contact: userMethod?.contact || null,
        verified: userMethod?.verified || false,
        enabled: userMethod?.enabled || false,
        added_at: userMethod?.added_at || null,
        last_used_at: userMethod?.last_used_at || null,
        active: method.active,
      };
    });

    return {
      email: user.email,
      mfaMethods,
    };
  }

  async getPasswordStatus(
    userId: string,
  ): Promise<{ lastPasswordUpdate: Date | null }> {
    // Ensure userId is provided
    if (!userId) {
      throw new UnauthorizedException('User not authenticated');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        last_password_update: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      lastPasswordUpdate: user.last_password_update,
    };
  }

  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
  ): Promise<{ message: string }> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.password) {
      throw new UnauthorizedException('User has no password set');
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(
      currentPassword,
      user.password,
    );
    if (!isPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password and last_password_update
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        last_password_update: new Date(),
      },
    });

    return { message: 'Password changed successfully' };
  }

  async sendMfaCode(methodId: string): Promise<{ message: string }> {
    const userMfaMethod = await this.prisma.userMfaMethod.findFirst({
      where: {
        id: methodId,
        enabled: true,
        verified: true,
      },
      include: {
        method: true,
        user: true,
      },
    });

    if (!userMfaMethod) {
      throw new NotFoundException('MFA method not found or not enabled');
    }

    // Generate OTP
    const otpCode = generateOtp();
    const expiresAt = generateOtpExpiry(5); // 5 minutes

    // Save OTP to database
    await this.prisma.otp.create({
      data: {
        code: otpCode,
        purpose: 'mfa_verification',
        expires_at: expiresAt,
        user_mfa_method_id: userMfaMethod.id,
      },
    });

    // If method is EMAIL, send OTP via email
    if (userMfaMethod.method.method === 'EMAIL') {
      await this.emailService.sendOtpEmail(
        userMfaMethod.contact || userMfaMethod.user.email,
        {
          name: userMfaMethod.user.name,
          otp: otpCode,
          otp_validity_minutes: 5,
          timestamp: formatTimestamp(),
          app_name: this.configService.get<string>('APP_NAME') || 'KB Tracker',
        },
      );
    }

    // For SMS method, you would implement SMS sending here
    // else if (userMfaMethod.method.method === 'SMS') { ... }

    return {
      message: 'Verification code has been sent',
    };
  }

  async verifyMfaCode(
    methodId: string,
    code: string,
  ): Promise<{ message: string; verified: boolean }> {
    const userMfaMethod = await this.prisma.userMfaMethod.findFirst({
      where: {
        id: methodId,
        enabled: true,
      },
    });

    if (!userMfaMethod) {
      throw new NotFoundException('MFA method not found or not enabled');
    }

    // Find valid OTP
    const otpRecord = await this.prisma.otp.findFirst({
      where: {
        user_mfa_method_id: methodId,
        code: code,
        purpose: 'mfa_verification',
        used: false,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    if (!otpRecord) {
      throw new BadRequestException('Invalid verification code');
    }

    // Check if OTP has expired
    if (isOtpExpired(otpRecord.expires_at)) {
      throw new BadRequestException('Verification code has expired');
    }

    // Mark OTP as used and update method status
    await this.prisma.$transaction([
      this.prisma.otp.update({
        where: { id: otpRecord.id },
        data: { used: true },
      }),
      this.prisma.userMfaMethod.update({
        where: { id: methodId },
        data: {
          verified: true,
          enabled: true,
          last_used_at: new Date(),
        },
      }),
    ]);

    return {
      message: 'Verification successful',
      verified: true,
    };
  }

  async enableMfa(
    userId: string,
    mfaMethodId: string,
    enabled: boolean,
  ): Promise<{ message: string }> {
    // Find the MFA method
    const mfaMethod = await this.prisma.mfaMethod.findUnique({
      where: { id: mfaMethodId },
    });

    if (!mfaMethod) {
      throw new NotFoundException('MFA method not found');
    }

    // Find existing user MFA method
    const existingUserMethod = await this.prisma.userMfaMethod.findFirst({
      where: {
        user_id: userId,
        method_id: mfaMethodId,
      },
    });

    if (existingUserMethod) {
      // Update existing record
      await this.prisma.userMfaMethod.update({
        where: { id: existingUserMethod.id },
        data: { enabled },
      });
    } else {
      // Get user details
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Create new record
      const contact =
        mfaMethod.method === 'SMS' ? user.phone_number : user.email;

      // Validate that contact is not null
      if (!contact) {
        throw new BadRequestException(
          mfaMethod.method === 'SMS'
            ? 'User phone number is required for SMS MFA'
            : 'User email is required for EMAIL MFA',
        );
      }
      await this.prisma.userMfaMethod.create({
        data: {
          user_id: userId,
          method_id: mfaMethodId,
          contact,
          enabled: true,
          verified: false,
        },
      });
    }

    return {
      message: enabled ? 'MFA method enabled' : 'MFA method disabled',
    };
  }

  async updatePhone(
    userId: string,
    phoneNumber: string,
    methodId: string,
  ): Promise<{ message: string }> {
    // Check if method is SMS type
    const mfaMethod = await this.prisma.mfaMethod.findFirst({
      where: {
        id: methodId,
        method: 'SMS',
      },
    });

    if (!mfaMethod) {
      throw new NotFoundException('SMS MFA method not found');
    }

    // Check for existing SMS method
    const existingMethod = await this.prisma.userMfaMethod.findFirst({
      where: {
        user_id: userId,
        method: {
          method: 'SMS',
        },
      },
    });

    if (existingMethod) {
      throw new BadRequestException(
        "Only used for users who don't have an existing SMS MFA method",
      );
    }

    // Create new user MFA method
    const userMfaMethod = await this.prisma.userMfaMethod.create({
      data: {
        user_id: userId,
        method_id: methodId,
        contact: phoneNumber,
        enabled: false,
        verified: false,
      },
    });

    // Generate OTP
    const otpCode = generateOtp();
    const expiresAt = generateOtpExpiry(5); // 5 minutes

    // Save OTP
    await this.prisma.otp.create({
      data: {
        code: otpCode,
        purpose: 'mfa_verification',
        expires_at: expiresAt,
        user_mfa_method_id: userMfaMethod.id,
      },
    });

    // Log OTP (in production, this would be sent via SMS)
    console.log('Phone number OTP:', otpCode);

    return {
      message: 'Verification code sent to phone number',
    };
  }

  async verifyPhone(
    userId: string,
    code: string,
  ): Promise<{ message: string; verified: boolean }> {
    // Find user's SMS MFA method
    const userMfaMethod = await this.prisma.userMfaMethod.findFirst({
      where: {
        user_id: userId,
        method: {
          method: 'SMS',
        },
      },
    });

    if (!userMfaMethod) {
      throw new NotFoundException('SMS MFA method not found');
    }

    // Find valid OTP
    const otpRecord = await this.prisma.otp.findFirst({
      where: {
        user_mfa_method_id: userMfaMethod.id,
        code,
        purpose: 'mfa_verification',
        used: false,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    if (!otpRecord) {
      throw new BadRequestException('Invalid verification code');
    }

    if (isOtpExpired(otpRecord.expires_at)) {
      throw new BadRequestException('Verification code has expired');
    }

    // Mark OTP as used and update method status
    await this.prisma.$transaction([
      this.prisma.otp.update({
        where: { id: otpRecord.id },
        data: { used: true },
      }),
      this.prisma.userMfaMethod.update({
        where: { id: userMfaMethod.id },
        data: {
          verified: true,
          enabled: true,
        },
      }),
    ]);

    return {
      message: 'Phone number verified successfully',
      verified: true,
    };
  }

  async resendPhoneCode(userId: string): Promise<{ message: string }> {
    // Find user's SMS MFA method
    const userMfaMethod = await this.prisma.userMfaMethod.findFirst({
      where: {
        user_id: userId,
        method: {
          method: 'SMS',
        },
      },
    });

    if (!userMfaMethod) {
      return {
        message: 'No SMS MFA method found for user',
      };
    }

    // Generate new OTP
    const otpCode = generateOtp();
    const expiresAt = generateOtpExpiry(5); // 5 minutes

    // Save OTP
    await this.prisma.otp.create({
      data: {
        code: otpCode,
        purpose: 'mfa_verification',
        expires_at: expiresAt,
        user_mfa_method_id: userMfaMethod.id,
      },
    });

    // Log OTP (in production, this would be sent via SMS)
    console.log('Phone number OTP:', otpCode);

    return {
      message: 'Verification code resent',
    };
  }

  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
  ): Promise<{ message: string }> {
    const { email, token, password } = resetPasswordDto;

    // Find user by email
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User with this email does not exist');
    }
    console.log('User', user);
    console.log('token', token);
    // Find valid OTP
    const otpRecord = await this.prisma.otp.findFirst({
      where: {
        user_id: user.id,
        code: token,
        purpose: 'reset_password',
        used: false,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    if (!otpRecord) {
      throw new BadRequestException('Invalid or expired token');
    }

    // Check if OTP has expired
    if (isOtpExpired(otpRecord.expires_at)) {
      throw new BadRequestException('Token has expired');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update user password and mark OTP as used
    await this.prisma.$transaction([
      this.prisma.user.update({
        where: { id: user.id },
        data: { password: hashedPassword },
      }),
      this.prisma.otp.update({
        where: { id: otpRecord.id },
        data: { used: true },
      }),
    ]);

    return {
      message: 'Password reset successfully',
    };
  }
}
