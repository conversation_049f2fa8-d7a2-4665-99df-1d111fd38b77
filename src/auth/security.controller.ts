import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthService } from './auth.service';
import { UpdatePhoneDto, VerifyPhoneDto } from './dto/enable-mfa.dto';

@ApiTags('Security')
@Controller('security')
export class SecurityController {
  constructor(private readonly authService: AuthService) {}

  @Post('mfa/phone')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update phone number for SMS MFA',
    description: 'Updates phone number and sends verification code',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Verification code sent',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Verification code sent to phone number',
        },
      },
    },
  })
  async updatePhone(
    @Request() req: any,
    @Body() updatePhoneDto: UpdatePhoneDto,
  ): Promise<{ message: string }> {
    return this.authService.updatePhone(
      req.user.id,
      updatePhoneDto.phoneNumber,
      updatePhoneDto.methodId,
    );
  }

  @Post('mfa/phone/verify')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify phone number for SMS MFA',
    description: 'Verifies the SMS verification code',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Phone number verified successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Phone number verified successfully',
        },
        verified: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid or expired verification code',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Invalid verification code',
        },
        error: {
          type: 'string',
          example: 'Bad Request',
        },
        statusCode: {
          type: 'number',
          example: 400,
        },
      },
    },
  })
  async verifyPhone(
    @Request() req: any,
    @Body() verifyPhoneDto: VerifyPhoneDto,
  ): Promise<{ message: string; verified: boolean }> {
    return this.authService.verifyPhone(req.user.id, verifyPhoneDto.code);
  }

  @Post('mfa/phone/resend')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Resend SMS verification code',
    description: 'Resends the SMS verification code',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Code resent',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Verification code resent',
        },
      },
    },
  })
  async resendPhoneCode(@Request() req: any): Promise<{ message: string }> {
    return this.authService.resendPhoneCode(req.user.id);
  }
}

