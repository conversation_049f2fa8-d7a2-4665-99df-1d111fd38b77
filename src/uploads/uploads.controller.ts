import {
  Controller,
  Get,
  Param,
  Res,
  NotFoundException,
  StreamableFile,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { FileStorageService } from '../common/services/file-storage.service';
import * as fs from 'fs';
import * as path from 'path';

@ApiTags('uploads')
@Controller('uploads')
export class UploadsController {
  constructor(private readonly fileStorageService: FileStorageService) {}

  /**
   * Serves uploaded files from nested directories
   * GET /uploads/{subDirectory}/{nestedDir}/{filename}
   */
  @Get(':subDirectory/:nestedDir/:filename')
  @ApiOperation({
    summary: 'Serve uploaded file from nested directory',
    description: 'Serves a file from a nested subdirectory in uploads',
  })
  @ApiParam({
    name: 'subDirectory',
    description: 'Main subdirectory (e.g., activities)',
    example: 'activities',
  })
  @ApiParam({
    name: 'nestedDir',
    description: 'Nested directory (e.g., activity ID)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'filename',
    description: 'Name of the file to serve',
    example: 'document.pdf',
  })
  @ApiResponse({
    status: 200,
    description: 'File served successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async serveNestedFile(
    @Param('subDirectory') subDirectory: string,
    @Param('nestedDir') nestedDir: string,
    @Param('filename') filename: string,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      // Construct file path
      const uploadDir = this.fileStorageService.getUploadDir();
      const filePath = path.join(uploadDir, subDirectory, nestedDir, filename);

      // Check if file exists
      const fileExists = await this.fileStorageService.fileExists(filePath);
      if (!fileExists) {
        throw new NotFoundException('File not found');
      }

      // Get file info
      const fileInfo = await this.fileStorageService.getFileInfo(filePath);
      if (!fileInfo) {
        throw new NotFoundException('File not found');
      }

      // Create file stream
      const file = fs.createReadStream(filePath);

      // Set appropriate headers
      const ext = path.extname(filename).toLowerCase();
      const mimeTypes: { [key: string]: string } = {
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.txt': 'text/plain',
        '.csv': 'text/csv',
      };

      const mimeType = mimeTypes[ext] || 'application/octet-stream';

      res.set({
        'Content-Type': mimeType,
        'Content-Length': fileInfo.size.toString(),
        'Content-Disposition': `inline; filename="${filename}"`,
      });

      return new StreamableFile(file);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('File not found');
    }
  }

  /**
   * Serves uploaded files from single directory
   * GET /uploads/{subDirectory}/{filename}
   */
  @Get(':subDirectory/:filename')
  @ApiOperation({
    summary: 'Serve uploaded file',
    description: 'Serves a file from the uploads directory',
  })
  @ApiParam({
    name: 'subDirectory',
    description: 'Subdirectory where the file is stored',
    example: 'general',
  })
  @ApiParam({
    name: 'filename',
    description: 'Name of the file to serve',
    example: 'document.pdf',
  })
  @ApiResponse({
    status: 200,
    description: 'File served successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async serveFile(
    @Param('subDirectory') subDirectory: string,
    @Param('filename') filename: string,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      // Construct file path
      const uploadDir = this.fileStorageService.getUploadDir();
      const filePath = path.join(uploadDir, subDirectory, filename);

      // Check if file exists
      const fileExists = await this.fileStorageService.fileExists(filePath);
      if (!fileExists) {
        throw new NotFoundException('File not found');
      }

      // Get file info
      const fileInfo = await this.fileStorageService.getFileInfo(filePath);
      if (!fileInfo) {
        throw new NotFoundException('File not found');
      }

      // Create file stream
      const file = fs.createReadStream(filePath);

      // Set appropriate headers
      const ext = path.extname(filename).toLowerCase();
      const mimeTypes: { [key: string]: string } = {
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.txt': 'text/plain',
        '.csv': 'text/csv',
      };

      const mimeType = mimeTypes[ext] || 'application/octet-stream';

      res.set({
        'Content-Type': mimeType,
        'Content-Length': fileInfo.size.toString(),
        'Content-Disposition': `inline; filename="${filename}"`,
      });

      return new StreamableFile(file);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('File not found');
    }
  }

}
