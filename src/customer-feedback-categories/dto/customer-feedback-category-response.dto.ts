import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for customer feedback category response
 * Defines the structure of customer feedback category data returned by the API
 */
export class CustomerFeedbackCategoryResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the customer feedback category',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the customer feedback category',
    example: 'Product Inquiry',
  })
  name: string;

  @ApiProperty({
    description: 'Username of the user who added this category',
    example: 'admin',
  })
  added_by: string;

  @ApiProperty({
    description: 'Date when the category was added',
    example: '2025-08-20T09:15:00.000Z',
  })
  added_on: string;
}
