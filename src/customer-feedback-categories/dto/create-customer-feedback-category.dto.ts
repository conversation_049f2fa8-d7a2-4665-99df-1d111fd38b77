import { IsNotEmpty, IsS<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new customer feedback category
 * Validates input data and provides API documentation
 */
export class CreateCustomerFeedbackCategoryDto {
  @ApiProperty({
    description: 'The name of the customer feedback category',
    example: 'Product Inquiry',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Customer feedback category name is required' })
  @IsString({ message: 'Customer feedback category name must be a string' })
  @MaxLength(255, { message: 'Customer feedback category name cannot exceed 255 characters' })
  name: string;
}
