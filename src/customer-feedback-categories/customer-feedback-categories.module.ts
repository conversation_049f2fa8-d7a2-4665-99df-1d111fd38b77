import { Module } from '@nestjs/common';
import { CustomerFeedbackCategoriesService } from './customer-feedback-categories.service';
import { CustomerFeedbackCategoriesController } from './customer-feedback-categories.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for Customer Feedback Categories functionality
 * Provides CRUD operations for customer feedback category management
 */
@Module({
  imports: [PrismaModule],
  providers: [CustomerFeedbackCategoriesService],
  controllers: [CustomerFeedbackCategoriesController],
  exports: [CustomerFeedbackCategoriesService], // Export service for use in other modules
})
export class CustomerFeedbackCategoriesModule {}
