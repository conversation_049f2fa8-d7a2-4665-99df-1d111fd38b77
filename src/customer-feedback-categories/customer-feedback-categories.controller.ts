import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CustomerFeedbackCategoriesService } from './customer-feedback-categories.service';
import { CreateCustomerFeedbackCategoryDto } from './dto/create-customer-feedback-category.dto';
import { UpdateCustomerFeedbackCategoryDto } from './dto/update-customer-feedback-category.dto';
import { CustomerFeedbackCategoryResponseDto } from './dto/customer-feedback-category-response.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';

/**
 * Controller handling all customer feedback category-related HTTP endpoints
 * Provides RESTful API for customer feedback category management
 */
@ApiTags('Customer Feedback Categories')
@Controller('customer-feedback-categories')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CustomerFeedbackCategoriesController {
  constructor(private readonly customerFeedbackCategoriesService: CustomerFeedbackCategoriesService) {}

  /**
   * Creates a new customer feedback category
   * POST /customer-feedback-categories
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new customer feedback category',
    description: 'Creates a new customer feedback category with the provided name. Category names must be unique (case-insensitive). The added_by field is automatically set to the logged-in user.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Customer feedback category created successfully',
    type: ApiResponseDto<CustomerFeedbackCategoryResponseDto>,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Customer feedback category name already exists' })
  async create(
    @Body(ValidationPipe) createDto: CreateCustomerFeedbackCategoryDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<CustomerFeedbackCategoryResponseDto>> {
    return this.customerFeedbackCategoriesService.create(createDto, req.user.id);
  }

  /**
   * Retrieves all customer feedback categories
   * GET /customer-feedback-categories
   */
  @Get()
  @ApiOperation({
    summary: 'Get all customer feedback categories',
    description: 'Retrieves all customer feedback categories with their details including who added them and when.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer feedback categories fetched successfully',
    type: ApiResponseDto<CustomerFeedbackCategoryResponseDto[]>,
  })
  async findAll(): Promise<ApiResponseDto<CustomerFeedbackCategoryResponseDto[]>> {
    return this.customerFeedbackCategoriesService.findAll();
  }

  /**
   * Updates a customer feedback category
   * PATCH /customer-feedback-categories/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update customer feedback category',
    description: 'Updates a customer feedback category by ID. Only the name can be updated.',
  })
  @ApiParam({ name: 'id', description: 'Customer feedback category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer feedback category updated successfully',
    type: ApiResponseDto<CustomerFeedbackCategoryResponseDto>,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Customer feedback category not found' })
  @ApiConflictResponse({ description: 'Customer feedback category name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateCustomerFeedbackCategoryDto,
  ): Promise<ApiResponseDto<CustomerFeedbackCategoryResponseDto>> {
    return this.customerFeedbackCategoriesService.update(id, updateDto);
  }

  /**
   * Deletes a customer feedback category
   * DELETE /customer-feedback-categories/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete customer feedback category',
    description: 'Deletes a customer feedback category by ID. Cannot delete categories that have associated activities.',
  })
  @ApiParam({ name: 'id', description: 'Customer feedback category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer feedback category deleted successfully',
    type: ApiResponseDto<null>,
  })
  @ApiNotFoundResponse({ description: 'Customer feedback category not found' })
  @ApiConflictResponse({ description: 'Cannot delete category with associated activities' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponseDto<null>> {
    return this.customerFeedbackCategoriesService.remove(id);
  }
}
