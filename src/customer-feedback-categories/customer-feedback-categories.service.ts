import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerFeedbackCategoryDto } from './dto/create-customer-feedback-category.dto';
import { UpdateCustomerFeedbackCategoryDto } from './dto/update-customer-feedback-category.dto';
import { CustomerFeedbackCategoryResponseDto } from './dto/customer-feedback-category-response.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';

@Injectable()
export class CustomerFeedbackCategoriesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new customer feedback category
   */
  async create(
    createDto: CreateCustomerFeedbackCategoryDto,
    userId: string,
  ): Promise<ApiResponseDto<CustomerFeedbackCategoryResponseDto>> {
    try {
      // Check if category with the same name already exists (case-insensitive)
      const existingCategory = await this.prisma.customerFeedbackCategory.findFirst({
        where: {
          name: {
            equals: createDto.name.trim(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      });

      if (existingCategory) {
        throw new ConflictException(
          `Customer feedback category with name "${createDto.name}" already exists`
        );
      }

      // Create the new customer feedback category
      const category = await this.prisma.customerFeedbackCategory.create({
        data: {
          name: createDto.name.trim(),
          added_by: userId,
        },
        include: {
          user: {
            select: { name: true },
          },
        },
      });

      const responseData: CustomerFeedbackCategoryResponseDto = {
        id: category.id,
        name: category.name,
        added_by: category.user.name,
        added_on: category.created_at.toISOString(),
      };

      return ApiResponseDto.success('Customer feedback category created successfully', responseData);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create customer feedback category');
    }
  }

  /**
   * Retrieves all customer feedback categories
   */
  async findAll(): Promise<ApiResponseDto<CustomerFeedbackCategoryResponseDto[]>> {
    try {
      const categories = await this.prisma.customerFeedbackCategory.findMany({
        include: {
          user: {
            select: { name: true },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });

      const data = categories.map((category) => ({
        id: category.id,
        name: category.name,
        added_by: category.user.name,
        added_on: category.created_at.toISOString(),
      }));

      return ApiResponseDto.success('Customer feedback categories fetched successfully', data);
    } catch (error) {
      throw new BadRequestException('Failed to fetch customer feedback categories');
    }
  }

  /**
   * Updates a customer feedback category
   */
  async update(
    id: string,
    updateDto: UpdateCustomerFeedbackCategoryDto,
  ): Promise<ApiResponseDto<CustomerFeedbackCategoryResponseDto>> {
    try {
      // Check if category exists
      const existingCategory = await this.prisma.customerFeedbackCategory.findUnique({
        where: { id },
        include: {
          user: {
            select: { name: true },
          },
        },
      });

      if (!existingCategory) {
        throw new NotFoundException('Customer feedback category not found');
      }

      // Check if another category with the same name exists (case-insensitive)
      if (updateDto.name) {
        const duplicateCategory = await this.prisma.customerFeedbackCategory.findFirst({
          where: {
            name: {
              equals: updateDto.name.trim(),
              mode: 'insensitive',
            },
            id: {
              not: id,
            },
          },
          select: { id: true, name: true },
        });

        if (duplicateCategory) {
          throw new ConflictException(
            `Customer feedback category with name "${updateDto.name}" already exists`
          );
        }
      }

      // Update the category
      const updatedCategory = await this.prisma.customerFeedbackCategory.update({
        where: { id },
        data: {
          name: updateDto.name?.trim(),
        },
        include: {
          user: {
            select: { name: true },
          },
        },
      });

      const responseData: CustomerFeedbackCategoryResponseDto = {
        id: updatedCategory.id,
        name: updatedCategory.name,
        added_by: updatedCategory.user.name,
        added_on: updatedCategory.created_at.toISOString(),
      };

      return ApiResponseDto.success('Customer feedback category updated successfully', responseData);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to update customer feedback category');
    }
  }

  /**
   * Deletes a customer feedback category
   */
  async remove(id: string): Promise<ApiResponseDto<null>> {
    try {
      // Check if category exists
      const existingCategory = await this.prisma.customerFeedbackCategory.findUnique({
        where: { id },
        include: {
          _count: {
            select: { activities: true },
          },
        },
      });

      if (!existingCategory) {
        throw new NotFoundException('Customer feedback category not found');
      }

      // Check if category has associated activities
      if (existingCategory._count.activities > 0) {
        throw new ConflictException(
          'Cannot delete customer feedback category that has associated activities'
        );
      }

      // Delete the category
      await this.prisma.customerFeedbackCategory.delete({
        where: { id },
      });

      return ApiResponseDto.success('Customer feedback category deleted successfully', null);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete customer feedback category');
    }
  }
}
