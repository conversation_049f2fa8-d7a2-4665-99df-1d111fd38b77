import { Module } from '@nestjs/common';
import { BankerDashboardController } from './banker-dashboard.controller';
import { BankerDashboardService } from './banker-dashboard.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [BankerDashboardController],
  providers: [BankerDashboardService],
  exports: [BankerDashboardService],
})
export class BankerDashboardModule {}