import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { BankerActivityStatusResponseDto } from './dto/banker-activity-status.dto';

@Injectable()
export class BankerDashboardService {
  constructor(private prisma: PrismaService) {}

  async getUpcomingAndOverdueActivities(
    userId: string,
    status?: string,
    interactionType?: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<BankerActivityStatusResponseDto> {
    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    // Build where conditions for filtering
    const whereConditions: any = {
      next_followup_date: {
        not: null,
      },
      lead: {
        isNot: null,
      },
      performed_by_user_id: userId, // Only activities for the current user
    };

    // Add status filter (upcoming or overdue)
    if (status === 'upcoming') {
      whereConditions.next_followup_date = {
        ...whereConditions.next_followup_date,
        gte: now,
        lte: threeDaysFromNow,
      };
    } else if (status === 'overdue') {
      whereConditions.next_followup_date = {
        ...whereConditions.next_followup_date,
        lt: now,
      };
    }

    // Add interaction type filter
    if (interactionType) {
      whereConditions.interaction_type = interactionType;
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get activities with all necessary relationships
    const [activities, totalCount] = await Promise.all([
      this.prisma.activity.findMany({
        where: whereConditions,
        include: {
          lead: true,
          performed_by: {
            include: {
              branch: {
                include: {
                  region: true,
                },
              },
            },
          },
        },
        orderBy: {
          next_followup_date: 'asc',
        },
        skip: offset,
        take: limit,
      }),
      this.prisma.activity.count({
        where: whereConditions,
      }),
    ]);

    // Filter activities based on status logic
    const filteredActivities = await this.filterActivitiesByStatus(activities, status);

    // Transform the data to match the DTO
    const transformedActivities = filteredActivities.map((activity) => {
      const isUpcoming = activity.next_followup_date && activity.next_followup_date >= now && activity.next_followup_date <= threeDaysFromNow;
      const activityStatus = isUpcoming ? 'upcoming' : 'overdue';

      return {
        id: activity.id,
        customer_name: activity.lead?.customer_name || 'Unknown Customer',
        staff_name: activity.performed_by?.name || 'Unknown Staff',
        region: activity.performed_by?.branch?.region?.name || 'Unknown Region',
        branch: activity.performed_by?.branch?.name || 'Unknown Branch',
        interaction_type: activity.interaction_type || 'unknown',
        status: activityStatus,
        contact_info: activity.performed_by?.phone_number || 'No contact info',
        next_followup_date: activity.next_followup_date?.toISOString() || '',
        created_at: activity.created_at.toISOString(),
        notes: activity.notes,
      };
    });

    const totalPages = Math.ceil(totalCount / limit);

    return {
      activities: transformedActivities,
      total: totalCount,
      page,
      limit,
      total_pages: totalPages,
    };
  }

  private async filterActivitiesByStatus(activities: any[], status?: string): Promise<any[]> {
    if (!status) {
      return activities;
    }

    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    const filteredActivities: any[] = [];

    for (const activity of activities) {
      if (!activity.next_followup_date) continue;

      const isUpcoming = activity.next_followup_date >= now && activity.next_followup_date <= threeDaysFromNow;
      const isOverdue = activity.next_followup_date < now;

      if (isOverdue && status === 'overdue') {
        // Check if there's a newer activity on the same lead after the next_followup_date
        const newerActivity = await this.prisma.activity.findFirst({
          where: {
            lead_id: activity.lead_id,
            created_at: {
              gt: activity.next_followup_date,
            },
          },
          orderBy: {
            created_at: 'desc',
          },
        });

        // Only include if no newer activity was found (meaning it's truly overdue)
        if (!newerActivity) {
          filteredActivities.push(activity);
        }
      } else if (isUpcoming && status === 'upcoming') {
        filteredActivities.push(activity);
      } else if (!status) {
        // If no status filter, include both upcoming and overdue
        if (isUpcoming || (isOverdue && !(await this.hasNewerActivity(activity)))) {
          filteredActivities.push(activity);
        }
      }
    }

    return filteredActivities;
  }

  private async hasNewerActivity(activity: any): Promise<boolean> {
    const newerActivity = await this.prisma.activity.findFirst({
      where: {
        lead_id: activity.lead_id,
        created_at: {
          gt: activity.next_followup_date,
        },
      },
    });

    return !!newerActivity;
  }
}