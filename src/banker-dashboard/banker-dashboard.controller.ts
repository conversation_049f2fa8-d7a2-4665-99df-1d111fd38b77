import { Controller, Get, Query, UseGuards, Request } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BankerDashboardService } from './banker-dashboard.service';
import { BankerActivityStatusResponseDto } from './dto/banker-activity-status.dto';

@ApiTags('Banker Dashboard')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('banker-dashboard')
export class BankerDashboardController {
  constructor(
    private readonly bankerDashboardService: BankerDashboardService,
  ) {}

  @Get('activity-status')
  @ApiOperation({
    summary: 'Get upcoming and overdue activities for the current user',
    description: 'Retrieves activities that are upcoming (due in less than 3 days) or overdue (past due date with no subsequent activity on the same lead) for the currently logged in user. Supports filtering by status and interaction type.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by activity status',
    enum: ['upcoming', 'overdue'],
    example: 'upcoming',
  })
  @ApiQuery({
    name: 'interaction_type',
    required: false,
    type: String,
    description: 'Filter by interaction type',
    enum: ['call', 'visit'],
    example: 'call',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 50, max: 100)',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: 'Activity status data retrieved successfully',
    type: BankerActivityStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getActivityStatus(
    @Request() req,
    @Query('status') status?: string,
    @Query('interaction_type') interactionType?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<BankerActivityStatusResponseDto> {
    const userId = req.user.id; // Get user ID from JWT token
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? Math.min(parseInt(limit, 10), 100) : 50;

    return this.bankerDashboardService.getUpcomingAndOverdueActivities(
      userId,
      status,
      interactionType,
      pageNumber,
      limitNumber,
    );
  }
}