import { NestFactory } from '@nestjs/core';
import { Logger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { SchedulerModule } from './scheduler.module';
import { SchedulerService } from './scheduler/scheduler.service';
import { MemoryMonitor } from './common/utils/memory-monitor';

async function bootstrap() {
  const app = await NestFactory.create(SchedulerModule, {
    bufferLogs: true,
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Use pino logger for all application logging
  app.useLogger(app.get(Logger));

  const configService = app.get(ConfigService);
  const logger = app.get(Logger);
  const schedulerService = app.get(SchedulerService);

  // Check if scheduler is enabled
  const schedulerEnabled = configService.get('SCHEDULER_ENABLED') === 'true';
  const timezone = configService.get('SCHEDULER_TIMEZONE') || 'UTC';

  if (!schedulerEnabled) {
    logger.warn(
      '⚠️  Scheduler is disabled. Set SCHEDULER_ENABLED=true to enable.',
    );
    process.exit(0);
  }

  logger.log('⏰ Starting Scheduler Service...');
  logger.log(`Scheduler Configuration:`);
  logger.log(`- Enabled: ${schedulerEnabled}`);
  logger.log(`- Timezone: ${timezone}`);

  // Initialize the application
  await app.init();

  // Start memory monitoring
  MemoryMonitor.startMonitoring(30000, 1500); // Monitor every 30 seconds, warn at 1.5GB

  // Start the scheduler (temporarily disabled for debugging)
  // await schedulerService.startScheduler();
  logger.log('⚠️  Scheduler startup temporarily disabled for debugging');

  logger.log('✅ Scheduler Service started successfully');

  // Handle graceful shutdown
  process.on('SIGTERM', async () => {
    logger.log('🛑 Received SIGTERM, shutting down gracefully...');
    await schedulerService.stopScheduler();
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🛑 Received SIGINT, shutting down gracefully...');
    await schedulerService.stopScheduler();
    await app.close();
    process.exit(0);
  });

  // Keep the process alive
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
}

bootstrap().catch((error) => {
  console.error('Failed to start scheduler service:', error);
  process.exit(1);
});
