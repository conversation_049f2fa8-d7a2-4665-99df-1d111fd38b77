import { ApiProperty } from '@nestjs/swagger';

export class TopAnchorDto {
  @ApiProperty({ 
    example: 'ABC Manufacturing Ltd',
    description: 'Name of the anchor'
  })
  name: string;

  @ApiProperty({ 
    example: 89,
    description: 'Number of leads associated with this anchor'
  })
  lead_count: number;
}

export class TopAnchorsResponseDto {
  @ApiProperty({
    type: [TopAnchorDto],
    description: 'Top 3 anchors with highest lead count'
  })
  topAnchors: TopAnchorDto[];
}

export class LeadStatisticsDto {
  @ApiProperty({
    example: 1247,
    description: 'Total number of leads (hitlist size)'
  })
  hitlist_size: number;

  @ApiProperty({
    example: 156,
    description: 'Number of leads generated month-to-date'
  })
  leads_generated_mtd: number;

  @ApiProperty({
    example: 89,
    description: 'Number of converted leads month-to-date (leads with account numbers)'
  })
  converted_leads_mtd: number;

  @ApiProperty({
    example: 57.05,
    description: 'Conversion rate as percentage (converted leads / total leads * 100)'
  })
  conversion_rate: number;
}

export class LeadStatisticsResponseDto {
  @ApiProperty({
    type: LeadStatisticsDto,
    description: 'Lead statistics including hitlist size, MTD data, and conversion rate'
  })
  statistics: LeadStatisticsDto;
}

export class LeadsByCategoryDto {
  @ApiProperty({
    example: 'Employed',
    description: 'Name of the customer category'
  })
  category_name: string;

  @ApiProperty({
    example: 456,
    description: 'Number of leads in this category'
  })
  lead_count: number;
}

export class LeadsByStatusDto {
  @ApiProperty({
    example: 'Hot',
    description: 'Lead status'
  })
  status: string;

  @ApiProperty({
    example: 234,
    description: 'Number of leads with this status'
  })
  lead_count: number;
}

export class LeadsGroupedResponseDto {
  @ApiProperty({
    type: [LeadsByCategoryDto],
    description: 'Leads grouped by customer category'
  })
  leads_by_category: LeadsByCategoryDto[];

  @ApiProperty({
    type: [LeadsByStatusDto],
    description: 'Leads grouped by lead status'
  })
  leads_by_status: LeadsByStatusDto[];
}

export class MonthlyConversionRateDto {
  @ApiProperty({
    example: 'January',
    description: 'Month name'
  })
  month: string;

  @ApiProperty({
    example: 1,
    description: 'Month number (1-12)'
  })
  month_number: number;

  @ApiProperty({
    example: 156,
    description: 'Total number of leads created in this month'
  })
  leads_created: number;

  @ApiProperty({
    example: 89,
    description: 'Number of leads converted (account number assigned) in this month'
  })
  leads_converted: number;

  @ApiProperty({
    example: 57.05,
    description: 'Conversion rate as percentage for this month'
  })
  conversion_rate: number;
}

export class MonthlyConversionRatesResponseDto {
  @ApiProperty({
    example: 2025,
    description: 'Year for which the data is provided'
  })
  year: number;

  @ApiProperty({
    type: [MonthlyConversionRateDto],
    description: 'Monthly conversion rates for the specified year'
  })
  monthly_conversion_rates: MonthlyConversionRateDto[];
}

export class LeadsByRegionDto {
  @ApiProperty({
    example: 'Central',
    description: 'Region name'
  })
  region_name: string;

  @ApiProperty({
    example: 456,
    description: 'Total number of leads in this region'
  })
  total_leads: number;

  @ApiProperty({
    example: 234,
    description: 'Number of converted leads (with account number) in this region'
  })
  converted_leads: number;

  @ApiProperty({
    example: 51.32,
    description: 'Conversion rate as percentage for this region'
  })
  conversion_rate: number;
}

export class LeadsByRegionResponseDto {
  @ApiProperty({
    type: [LeadsByRegionDto],
    description: 'Leads grouped by region with conversion data'
  })
  leads_by_region: LeadsByRegionDto[];
}

export class LeadsByBranchDto {
  @ApiProperty({
    example: 'Nairobi Main',
    description: 'Branch name'
  })
  branch_name: string;

  @ApiProperty({
    example: 156,
    description: 'Total number of leads in this branch'
  })
  total_leads: number;

  @ApiProperty({
    example: 89,
    description: 'Number of converted leads (with account number) in this branch'
  })
  converted_leads: number;

  @ApiProperty({
    example: 57.05,
    description: 'Conversion rate as percentage for this branch'
  })
  conversion_rate: number;
}

export class LeadsByBranchResponseDto {
  @ApiProperty({
    example: 'Nairobi Region',
    description: 'Region name for which branch data is provided'
  })
  region_name: string;

  @ApiProperty({
    type: [LeadsByBranchDto],
    description: 'Leads grouped by branch within the specified region'
  })
  leads_by_branch: LeadsByBranchDto[];
}

export class CallVisitStatisticsDto {
  @ApiProperty({
    example: 245,
    description: 'Total number of calls made month-to-date'
  })
  total_calls_mtd: number;

  @ApiProperty({
    example: 300,
    description: 'Total call targets month-to-date (sum of all active call targets)'
  })
  total_call_targets_mtd: number;

  @ApiProperty({
    example: 156,
    description: 'Total number of visits made month-to-date'
  })
  total_visits_mtd: number;

  @ApiProperty({
    example: 200,
    description: 'Total visit targets month-to-date (sum of all active visit targets)'
  })
  total_visit_targets_mtd: number;

  @ApiProperty({
    example: 81.67,
    description: 'Call achievement rate as percentage (calls made / call targets * 100)'
  })
  call_achievement_rate: number;

  @ApiProperty({
    example: 78.00,
    description: 'Visit achievement rate as percentage (visits made / visit targets * 100)'
  })
  visit_achievement_rate: number;
}

export class CallVisitStatisticsResponseDto {
  @ApiProperty({
    type: CallVisitStatisticsDto,
    description: 'Call and visit statistics including MTD data and achievement rates'
  })
  statistics: CallVisitStatisticsDto;
}
