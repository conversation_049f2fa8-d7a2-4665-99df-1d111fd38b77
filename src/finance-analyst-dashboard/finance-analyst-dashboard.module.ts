import { Module } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { FinanceAnalystDashboardController } from './finance-analyst-dashboard.controller';
import { FinanceAnalystDashboardService } from './finance-analyst-dashboard.service';

@Module({
  imports: [PrismaModule],
  controllers: [FinanceAnalystDashboardController],
  providers: [FinanceAnalystDashboardService],
  exports: [FinanceAnalystDashboardService],
})
export class FinanceAnalystDashboardModule {}
