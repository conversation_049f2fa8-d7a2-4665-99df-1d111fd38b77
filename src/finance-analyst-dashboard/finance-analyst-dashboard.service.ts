import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TopAnchorsResponseDto, LeadStatisticsResponseDto, LeadsGroupedResponseDto, MonthlyConversionRatesResponseDto, LeadsByRegionResponseDto, LeadsByBranchResponseDto, CallVisitStatisticsResponseDto } from './dto/top-anchors.dto';

@Injectable()
export class FinanceAnalystDashboardService {
  constructor(private readonly prisma: PrismaService) {}

  async getTopAnchors(): Promise<TopAnchorsResponseDto> {
    // Get anchors with their lead counts, ordered by lead count descending
    const anchorsWithLeadCount = await this.prisma.anchor.findMany({
      select: {
        name: true,
        _count: {
          select: {
            leads: true,
          },
        },
      },
      orderBy: {
        leads: {
          _count: 'desc',
        },
      },
      take: 3, // Get top 3 anchors
    });

    // Transform the data to match the expected response format
    const topAnchors = anchorsWithLeadCount.map((anchor) => ({
      name: anchor.name,
      lead_count: anchor._count.leads,
    }));

    return {
      topAnchors,
    };
  }

  async getLeadStatistics(): Promise<LeadStatisticsResponseDto> {
    // Calculate MTD (Month-to-Date) range
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // Execute all queries in parallel for optimal performance
    const [
      totalLeads,
      leadsGeneratedMTD,
      convertedLeadsMTD,
    ] = await Promise.all([
      // Total number of leads (hitlist size)
      this.prisma.lead.count(),

      // Number of leads generated MTD (using created_at field)
      this.prisma.lead.count({
        where: {
          created_at: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      }),

      // Number of converted leads MTD (using account_number_assigned_at field)
      this.prisma.lead.count({
        where: {
          account_number_assigned_at: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
          account_number: {
            not: null,
          },
        },
      }),
    ]);

    // Calculate conversion rate (converted leads / total leads * 100)
    const conversionRate = totalLeads > 0
      ? Number(((convertedLeadsMTD / totalLeads) * 100).toFixed(2))
      : 0;

    return {
      statistics: {
        hitlist_size: totalLeads,
        leads_generated_mtd: leadsGeneratedMTD,
        converted_leads_mtd: convertedLeadsMTD,
        conversion_rate: conversionRate,
      },
    };
  }

  async getLeadsGrouped(): Promise<LeadsGroupedResponseDto> {
    // Execute both queries in parallel for optimal performance
    const [leadsByCategory, leadsByStatus] = await Promise.all([
      // Get leads grouped by customer category
      this.prisma.lead.groupBy({
        by: ['customer_category_id'],
        _count: {
          id: true,
        },
        where: {
          customer_category_id: {
            not: null,
          },
        },
      }),

      // Get leads grouped by lead status
      this.prisma.lead.groupBy({
        by: ['lead_status'],
        _count: {
          id: true,
        },
        where: {
          lead_status: {
            not: null,
          },
        },
      }),
    ]);

    // Get customer category details for the grouped results
    const categoryIds = leadsByCategory.map(item => item.customer_category_id).filter(Boolean);
    const customerCategories = categoryIds.length > 0 ? await this.prisma.customerCategory.findMany({
      where: {
        id: {
          in: categoryIds as string[],
        },
      },
      select: {
        id: true,
        name: true,
      },
    }) : [];

    // Transform leads by category data
    const transformedLeadsByCategory = leadsByCategory.map(item => {
      const category = customerCategories.find(cat => cat.id === item.customer_category_id);
      return {
        category_name: category?.name || 'Unknown Category',
        lead_count: item._count.id,
      };
    });

    // Transform leads by status data
    const transformedLeadsByStatus = leadsByStatus.map(item => ({
      status: item.lead_status || 'Unknown Status',
      lead_count: item._count.id,
    }));

    return {
      leads_by_category: transformedLeadsByCategory,
      leads_by_status: transformedLeadsByStatus,
    };
  }

  async getMonthlyConversionRates(year?: number): Promise<MonthlyConversionRatesResponseDto> {
    // Use current year if no year is provided
    const targetYear = year || new Date().getFullYear();
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11, we need 1-12

    // Determine how many months to process
    // If it's the current year, only process up to current month
    // If it's a past year, process all 12 months
    const monthsToProcess = targetYear === currentYear ? currentMonth : 12;

    // Month names for display
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Create array to store monthly data
    const monthlyData: Array<{
      month: string;
      month_number: number;
      leads_created: number;
      leads_converted: number;
      conversion_rate: number;
    }> = [];

    // Process each month
    for (let month = 1; month <= monthsToProcess; month++) {
      // Calculate start and end dates for the month
      const startOfMonth = new Date(targetYear, month - 1, 1);
      const endOfMonth = new Date(targetYear, month, 0, 23, 59, 59, 999);

      // Execute queries in parallel for each month
      const [leadsCreated, leadsConverted] = await Promise.all([
        // Count leads created in this month
        this.prisma.lead.count({
          where: {
            created_at: {
              gte: startOfMonth,
              lte: endOfMonth,
            },
          },
        }),

        // Count leads converted in this month (account number assigned)
        this.prisma.lead.count({
          where: {
            account_number_assigned_at: {
              gte: startOfMonth,
              lte: endOfMonth,
            },
            account_number: {
              not: null,
            },
          },
        }),
      ]);

      // Calculate conversion rate for this month
      const conversionRate = leadsCreated > 0
        ? Number(((leadsConverted / leadsCreated) * 100).toFixed(2))
        : 0;

      monthlyData.push({
        month: monthNames[month - 1],
        month_number: month,
        leads_created: leadsCreated,
        leads_converted: leadsConverted,
        conversion_rate: conversionRate,
      });
    }

    return {
      year: targetYear,
      monthly_conversion_rates: monthlyData,
    };
  }

  async getLeadsByRegion(): Promise<LeadsByRegionResponseDto> {
    // Get all regions with their leads data
    const regions = await this.prisma.region.findMany({
      include: {
        branches: {
          include: {
            leads: {
              select: {
                id: true,
                account_number: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform data to calculate totals per region
    const leadsByRegion = regions.map(region => {
      // Flatten all leads from all branches in this region
      const allLeadsInRegion = region.branches.flatMap(branch => branch.leads);

      const totalLeads = allLeadsInRegion.length;
      const convertedLeads = allLeadsInRegion.filter(lead => lead.account_number !== null).length;
      const conversionRate = totalLeads > 0
        ? Number(((convertedLeads / totalLeads) * 100).toFixed(2))
        : 0;

      return {
        region_name: region.name,
        total_leads: totalLeads,
        converted_leads: convertedLeads,
        conversion_rate: conversionRate,
      };
    });

    return {
      leads_by_region: leadsByRegion,
    };
  }

  async getLeadsByBranch(regionName?: string): Promise<LeadsByBranchResponseDto> {
    // Use 'Nairobi Region' as default region if none provided
    const targetRegion = regionName || 'Nairobi Region';

    // Get the specific region with its branches and leads
    const region = await this.prisma.region.findFirst({
      where: {
        name: {
          equals: targetRegion,
          mode: 'insensitive',
        },
      },
      include: {
        branches: {
          include: {
            leads: {
              select: {
                id: true,
                account_number: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
        },
      },
    });

    if (!region) {
      // If region not found, return empty data
      return {
        region_name: targetRegion,
        leads_by_branch: [],
      };
    }

    // Transform data to calculate totals per branch
    const leadsByBranch = region.branches.map(branch => {
      const totalLeads = branch.leads.length;
      const convertedLeads = branch.leads.filter(lead => lead.account_number !== null).length;
      const conversionRate = totalLeads > 0
        ? Number(((convertedLeads / totalLeads) * 100).toFixed(2))
        : 0;

      return {
        branch_name: branch.name,
        total_leads: totalLeads,
        converted_leads: convertedLeads,
        conversion_rate: conversionRate,
      };
    });

    return {
      region_name: region.name,
      leads_by_branch: leadsByBranch,
    };
  }

  async getCallVisitStatistics(): Promise<CallVisitStatisticsResponseDto> {
    // Get current month start and end dates
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // Execute all queries in parallel for optimal performance
    const [
      totalCallsMTD,
      totalVisitsMTD,
      activeCallTargets,
      activeVisitTargets,
    ] = await Promise.all([
      // Count total calls made MTD
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          created_at: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      }),

      // Count total visits made MTD
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          created_at: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      }),

      // Get active call targets (targets that started before or during current month)
      this.prisma.target.findMany({
        where: {
          metric_type: 'Call',
          start_date: {
            lte: endOfMonth, // Target started before or during current month
          },
          end_date: {
            gte: startOfMonth, // Target ends after or during current month
          },
        },
        include: {
          role: {
            include: {
              users: true,
            },
          },
          exempted_target_users: true,
        },
      }),

      // Get active visit targets (targets that started before or during current month)
      this.prisma.target.findMany({
        where: {
          metric_type: 'Visit',
          start_date: {
            lte: endOfMonth, // Target started before or during current month
          },
          end_date: {
            gte: startOfMonth, // Target ends after or during current month
          },
        },
        include: {
          role: {
            include: {
              users: true,
            },
          },
          exempted_target_users: true,
        },
      }),
    ]);

    // Calculate total call targets MTD
    const totalCallTargetsMTD = activeCallTargets.reduce((total, target) => {
      if (target.role_id) {
        // For role targets: count users in role minus exempted users, then multiply by target value
        const totalUsersInRole = target.role?.users?.length || 0;
        const exemptedUsersCount = target.exempted_target_users.length;
        const activeUsers = Math.max(0, totalUsersInRole - exemptedUsersCount);
        return total + (activeUsers * target.target_value);
      } else {
        // For individual targets: add the target value directly
        return total + target.target_value;
      }
    }, 0);

    // Calculate total visit targets MTD
    const totalVisitTargetsMTD = activeVisitTargets.reduce((total, target) => {
      if (target.role_id) {
        // For role targets: count users in role minus exempted users, then multiply by target value
        const totalUsersInRole = target.role?.users?.length || 0;
        const exemptedUsersCount = target.exempted_target_users.length;
        const activeUsers = Math.max(0, totalUsersInRole - exemptedUsersCount);
        return total + (activeUsers * target.target_value);
      } else {
        // For individual targets: add the target value directly
        return total + target.target_value;
      }
    }, 0);

    // Calculate achievement rates
    const callAchievementRate = totalCallTargetsMTD > 0
      ? Number(((totalCallsMTD / totalCallTargetsMTD) * 100).toFixed(2))
      : 0;

    const visitAchievementRate = totalVisitTargetsMTD > 0
      ? Number(((totalVisitsMTD / totalVisitTargetsMTD) * 100).toFixed(2))
      : 0;

    return {
      statistics: {
        total_calls_mtd: totalCallsMTD,
        total_call_targets_mtd: totalCallTargetsMTD,
        total_visits_mtd: totalVisitsMTD,
        total_visit_targets_mtd: totalVisitTargetsMTD,
        call_achievement_rate: callAchievementRate,
        visit_achievement_rate: visitAchievementRate,
      },
    };
  }
}
