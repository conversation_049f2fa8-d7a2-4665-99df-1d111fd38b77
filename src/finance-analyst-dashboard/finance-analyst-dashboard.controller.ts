import {
  Controller,
  Get,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { FinanceAnalystDashboardService } from './finance-analyst-dashboard.service';
import { TopAnchorsResponseDto, LeadStatisticsResponseDto, LeadsGroupedResponseDto, MonthlyConversionRatesResponseDto, LeadsByRegionResponseDto, LeadsByBranchResponseDto, CallVisitStatisticsResponseDto } from './dto/top-anchors.dto';

@ApiTags('Finance Analyst Dashboard')
@Controller('finance-analyst-dashboard')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FinanceAnalystDashboardController {
  constructor(
    private readonly financeAnalystDashboardService: FinanceAnalystDashboardService,
  ) {}

  @Get('top-anchors')
  @ApiOperation({
    summary: 'Get top 3 anchors with highest lead count',
    description: 'Retrieves the top 3 anchors ordered by the number of leads associated with each anchor.',
  })
  @ApiResponse({
    status: 200,
    description: 'Top anchors retrieved successfully',
    type: TopAnchorsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getTopAnchors(): Promise<TopAnchorsResponseDto> {
    return this.financeAnalystDashboardService.getTopAnchors();
  }

  @Get('lead-statistics')
  @ApiOperation({
    summary: 'Get lead statistics',
    description: 'Retrieves comprehensive lead statistics including total leads (hitlist size), MTD leads generated, MTD converted leads, and conversion rate.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lead statistics retrieved successfully',
    type: LeadStatisticsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getLeadStatistics(): Promise<LeadStatisticsResponseDto> {
    return this.financeAnalystDashboardService.getLeadStatistics();
  }

  @Get('leads-grouped')
  @ApiOperation({
    summary: 'Get leads grouped by category and status',
    description: 'Retrieves leads grouped by customer category and lead status with counts for each group.',
  })
  @ApiResponse({
    status: 200,
    description: 'Grouped leads data retrieved successfully',
    type: LeadsGroupedResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getLeadsGrouped(): Promise<LeadsGroupedResponseDto> {
    return this.financeAnalystDashboardService.getLeadsGrouped();
  }

  @Get('monthly-conversion-rates')
  @ApiOperation({
    summary: 'Get monthly lead conversion rates',
    description: 'Retrieves monthly lead conversion rates for a specified year. Shows conversion rate for each month (leads converted / leads created * 100%). For current year, shows data up to current month. For past years, shows all 12 months.',
  })
  @ApiQuery({
    name: 'year',
    required: false,
    type: Number,
    description: 'Year in YYYY format (e.g., 2025). Defaults to current year if not provided.',
    example: 2025,
  })
  @ApiResponse({
    status: 200,
    description: 'Monthly conversion rates retrieved successfully',
    type: MonthlyConversionRatesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getMonthlyConversionRates(
    @Query('year') year?: string,
  ): Promise<MonthlyConversionRatesResponseDto> {
    const yearNumber = year ? parseInt(year, 10) : undefined;
    return this.financeAnalystDashboardService.getMonthlyConversionRates(yearNumber);
  }

  @Get('leads-by-region')
  @ApiOperation({
    summary: 'Get leads grouped by region',
    description: 'Retrieves total leads and converted leads grouped by region. Uses branch-to-region relationship to aggregate data.',
  })
  @ApiResponse({
    status: 200,
    description: 'Leads by region data retrieved successfully',
    type: LeadsByRegionResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getLeadsByRegion(): Promise<LeadsByRegionResponseDto> {
    return this.financeAnalystDashboardService.getLeadsByRegion();
  }

  @Get('leads-by-branch')
  @ApiOperation({
    summary: 'Get leads grouped by branch within a region',
    description: 'Retrieves total leads and converted leads grouped by branch for a specific region. Defaults to Nairobi Region if no region is specified.',
  })
  @ApiQuery({
    name: 'region',
    required: false,
    type: String,
    description: 'Region name to filter branches. Defaults to "Nairobi Region" if not provided.',
    example: 'Nairobi Region',
  })
  @ApiResponse({
    status: 200,
    description: 'Leads by branch data retrieved successfully',
    type: LeadsByBranchResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getLeadsByBranch(
    @Query('region') region?: string,
  ): Promise<LeadsByBranchResponseDto> {
    return this.financeAnalystDashboardService.getLeadsByBranch(region);
  }

  @Get('call-visit-statistics')
  @ApiOperation({
    summary: 'Get call and visit statistics MTD',
    description: 'Retrieves month-to-date statistics for calls and visits made, along with their respective targets and achievement rates.',
  })
  @ApiResponse({
    status: 200,
    description: 'Call and visit statistics retrieved successfully',
    type: CallVisitStatisticsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getCallVisitStatistics(): Promise<CallVisitStatisticsResponseDto> {
    return this.financeAnalystDashboardService.getCallVisitStatistics();
  }
}
