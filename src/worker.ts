import { NestFactory } from '@nestjs/core';
import { Logger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { WorkerModule } from './worker.module';
import { MemoryMonitor } from './common/utils/memory-monitor';

async function bootstrap() {
  const app = await NestFactory.create(WorkerModule, {
    bufferLogs: true,
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Use pino logger for all application logging
  app.useLogger(app.get(Logger));

  const configService = app.get(ConfigService);
  const logger = app.get(Logger);

  // Get worker configuration
  const concurrency = configService.get('WORKER_CONCURRENCY') || 5;
  const maxStalledCount = configService.get('WORKER_MAX_STALLED_COUNT') || 3;
  const stalledInterval = configService.get('WORKER_STALLED_INTERVAL') || 30000;

  logger.log('🔧 Starting BullMQ Worker Service...');
  logger.log(`Worker Configuration:`);
  logger.log(`- Concurrency: ${concurrency}`);
  logger.log(`- Max Stalled Count: ${maxStalledCount}`);
  logger.log(`- Stalled Interval: ${stalledInterval}ms`);

  // Initialize the application (this will start the processors)
  await app.init();

  // Start memory monitoring
  MemoryMonitor.startMonitoring(30000, 1500); // Monitor every 30 seconds, warn at 1.5GB

  logger.log('✅ BullMQ Worker Service started successfully');

  // Handle graceful shutdown
  process.on('SIGTERM', async () => {
    logger.log('🛑 Received SIGTERM, shutting down gracefully...');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🛑 Received SIGINT, shutting down gracefully...');
    await app.close();
    process.exit(0);
  });

  // Keep the process alive
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
}

bootstrap().catch((error) => {
  console.error('Failed to start worker service:', error);
  process.exit(1);
});
