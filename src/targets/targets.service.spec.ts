import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { TargetsService } from './targets.service';
import { PrismaService } from '../prisma/prisma.service';
import { DailyTargetHandler } from './handlers/daily-target.handler';
import { TargetProgressService } from './services/target-progress.service';
import { CreateTargetDto } from './dto/create-target.dto';

describe('TargetsService', () => {
  let service: TargetsService;
  let prismaService: PrismaService;
  let dailyTargetHandler: DailyTargetHandler;

  const mockPrismaService = {
    target: {
      create: jest.fn(),
      createMany: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    role: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    user: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    activity: {
      count: jest.fn(),
    },
    exemptedTargetUser: {
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  const mockDailyTargetHandler = {
    handleDailyTargets: jest.fn(),
    validateRoles: jest.fn(),
    validateUsers: jest.fn(),
  };

  const mockTargetProgressService = {
    createDailyRoleTargetProgress: jest.fn(),
    createDailyRoleTargetProgressSingleDay: jest.fn(),
    createDailyRoleTargetProgressToday: jest.fn(),
    getUsersByRoles: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TargetsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: DailyTargetHandler,
          useValue: mockDailyTargetHandler,
        },
        {
          provide: TargetProgressService,
          useValue: mockTargetProgressService,
        },
      ],
    }).compile();

    service = module.get<TargetsService>(TargetsService);
    prismaService = module.get<PrismaService>(PrismaService);
    dailyTargetHandler = module.get<DailyTargetHandler>(DailyTargetHandler);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTargets', () => {
    const mockCreateTargetDto: CreateTargetDto = {
      metricType: 'Call',
      targetValue: 5,
      frequency: 'daily',
      startDate: '2025-08-01T02:00:00.000Z',
      endDate: '2025-08-31T20:59:59.999Z',
      scope: 'role',
      assignTo: ['b24c9a7f-7b57-4788-9fbb-f04757055566'],
      ranges: null,
    };

    it('should create daily targets using daily target handler', async () => {
      // Arrange
      const mockTargetIds = ['target-id-1', 'target-id-2'];
      mockDailyTargetHandler.validateRoles.mockResolvedValue(undefined);
      mockDailyTargetHandler.handleDailyTargets.mockResolvedValue(
        mockTargetIds,
      );
      mockPrismaService.role.findFirst.mockResolvedValue({
        id: 'role-id',
        name: 'Test Role',
        users: [{ id: 'user-1' }, { id: 'user-2' }],
      });

      // Act
      const result = await service.createTargets(mockCreateTargetDto);

      // Assert
      expect(mockDailyTargetHandler.validateRoles).toHaveBeenCalledWith([
        'b24c9a7f-7b57-4788-9fbb-f04757055566',
      ]);
      expect(mockDailyTargetHandler.handleDailyTargets).toHaveBeenCalledWith(
        mockCreateTargetDto,
      );
      expect(result).toEqual(
        expect.objectContaining({
          id: mockTargetIds[0],
          metric: 'Call',
          frequency: 'daily',
          value: 5,
          scope: 'role',
        }),
      );
    });

    it('should create non-daily targets using handleNonDailyTargets', async () => {
      // Arrange
      const weeklyTargetDto = { ...mockCreateTargetDto, frequency: 'weekly' };
      mockDailyTargetHandler.validateRoles.mockResolvedValue(undefined);
      mockPrismaService.target.create.mockResolvedValue({
        id: 'weekly-target-id',
        metric_type: 'Call',
        target_value: 5,
        frequency: 'weekly',
      });
      mockPrismaService.role.findFirst.mockResolvedValue({
        id: 'role-id',
        name: 'Test Role',
        users: [{ id: 'user-1' }],
      });

      // Act
      const result = await service.createTargets(weeklyTargetDto);

      // Assert
      expect(mockDailyTargetHandler.handleDailyTargets).not.toHaveBeenCalled();
      expect(result).toEqual(
        expect.objectContaining({
          metric: 'Call',
          frequency: 'weekly',
          value: 5,
          scope: 'role',
        }),
      );
    });

    it('should validate roles for role scope', async () => {
      // Arrange
      mockDailyTargetHandler.validateRoles.mockResolvedValue(undefined);
      mockDailyTargetHandler.handleDailyTargets.mockResolvedValue([
        'target-id',
      ]);
      mockPrismaService.role.findFirst.mockResolvedValue({
        id: 'role-id',
        name: 'Test Role',
        users: [],
      });

      // Act
      await service.createTargets(mockCreateTargetDto);

      // Assert
      expect(mockDailyTargetHandler.validateRoles).toHaveBeenCalledWith([
        'b24c9a7f-7b57-4788-9fbb-f04757055566',
      ]);
    });

    it('should validate users for individual scope', async () => {
      // Arrange
      const individualTargetDto = {
        ...mockCreateTargetDto,
        scope: 'individual',
      };
      mockDailyTargetHandler.validateUsers.mockResolvedValue(undefined);
      mockDailyTargetHandler.handleDailyTargets.mockResolvedValue([
        'target-id',
      ]);
      mockPrismaService.user.findFirst.mockResolvedValue({
        id: 'user-id',
        name: 'Test User',
        role: { name: 'Test Role' },
      });

      // Act
      await service.createTargets(individualTargetDto);

      // Assert
      expect(mockDailyTargetHandler.validateUsers).toHaveBeenCalledWith([
        'b24c9a7f-7b57-4788-9fbb-f04757055566',
      ]);
    });

    it('should handle daily targets with null endDate', async () => {
      // Arrange
      const nullEndDateDto = { ...mockCreateTargetDto, endDate: null };
      mockDailyTargetHandler.validateRoles.mockResolvedValue(undefined);
      mockDailyTargetHandler.handleDailyTargets.mockResolvedValue([
        'target-id',
      ]);
      mockPrismaService.role.findFirst.mockResolvedValue({
        id: 'role-id',
        name: 'Test Role',
        users: [],
      });

      // Act
      const result = await service.createTargets(nullEndDateDto);

      // Assert
      expect(mockDailyTargetHandler.handleDailyTargets).toHaveBeenCalledWith(
        nullEndDateDto,
      );
      expect(result.end_date).toBe(nullEndDateDto.startDate); // Should default to startDate
    });
  });
});
