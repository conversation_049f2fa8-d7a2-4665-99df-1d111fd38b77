import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for target response
 */
export class TargetResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the target',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Type of metric being tracked',
    example: 'Call',
    enum: ['Call', 'Visit'],
  })
  metric: string;

  @ApiProperty({
    description: 'User or role assigned to this target',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: '<PERSON>',
      role: 'Relationship Officer',
    },
  })
  assigned_to:
    | {
        id: string;
        name: string;
        role?: string;
      }
    | string;

  @ApiProperty({
    description: 'Frequency of the target',
    example: 'weekly',
    enum: ['daily', 'weekly', 'custom'],
  })
  frequency: string;

  @ApiProperty({
    description: 'Type of activity for the target',
    example: 'LEADS_HITLIST',
    enum: [
      'LEADS_HITLIST',
      'CUSTOMER_RELATIONSHIP',
      'TWO_BY_TWO_BY_TWO_HITLIST',
      'DORMANCY_HITLIST',
      'LOAN_ACTIVITIES',
    ],
  })
  activity: string;

  @ApiProperty({
    description: 'Target value to achieve',
    example: 25,
  })
  value: number;

  @ApiProperty({
    description: 'Start date of the target period',
    example: '2025-08-01',
  })
  start_date: string;

  @ApiProperty({
    description: 'End date of the target period (can be null)',
    example: '2025-08-31',
    nullable: true,
  })
  end_date: string | null;

  @ApiProperty({
    description: 'Number of users using this target',
    example: 5,
  })
  users_count: number;

  @ApiProperty({
    description: 'Number of exempted users for this target',
    example: 2,
  })
  exempted_users: number;

  @ApiProperty({
    description: 'Scope of the target assignment',
    example: 'role',
    enum: ['role', 'individual'],
  })
  scope: string;

  @ApiProperty({
    description:
      'Number of activities completed within the target period (only for individual scope)',
    example: 15,
    required: false,
  })
  activity_count?: number;

  @ApiProperty({
    description: 'Total number of calls made within the target period',
    example: 45,
  })
  total_calls_made: number;

  @ApiProperty({
    description: 'Total number of visits made within the target period',
    example: 30,
  })
  total_visits_made: number;

  @ApiProperty({
    description:
      'Total target value (adjusted for exempted users in role targets)',
    example: 100,
  })
  total_target: number;
}
