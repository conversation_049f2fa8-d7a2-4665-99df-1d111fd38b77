import { ApiProperty } from '@nestjs/swagger';

export class MyTargetActivityDto {
  @ApiProperty({
    description: 'Target ID',
    example: 'e6dd1952-0f0e-4365-a28a-c88bf7fc6244',
  })
  id: string;

  @ApiProperty({
    description: 'Activity type',
    example: 'LEADS_HITLIST',
    enum: [
      'LEADS_HITLIST',
      'CUSTOMER_RELATIONSHIP',
      'TWO_BY_TWO_BY_TWO_HITLIST',
      'DORMANCY_HITLIST',
      'LOAN_ACTIVITIES',
    ],
  })
  activity: string;

  @ApiProperty({
    description: 'Target value for today',
    example: 5,
  })
  target_value: number;

  @ApiProperty({
    description: 'Number of activities completed today',
    example: 3,
  })
  done_count: number;

  @ApiProperty({
    description: 'Whether the target is applicable today (false on weekends/holidays)',
    example: true,
  })
  is_applicable: boolean;
}

export class MyTargetsResponseDto {
  @ApiProperty({
    description: 'List of target activities for today',
    type: [MyTargetActivityDto],
  })
  activities: MyTargetActivityDto[];
}
