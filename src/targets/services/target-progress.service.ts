import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import {
  isApplicable,
  createDateAt2AM,
  generateDateRange,
  isSameDay,
  getTodayAt2AM,
} from '../../common/utils/date.utils';

// Type for Prisma transaction client
type PrismaTransaction = Parameters<
  Parameters<PrismaService['$transaction']>[0]
>[0];

/**
 * Service for handling TargetProgress creation and management
 * Provides modular functions for different target progress scenarios
 */
@Injectable()
export class TargetProgressService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create TargetProgress records for daily targets with role scope
   * @param targetIds - Array of target IDs to link progress records to
   * @param userIds - Array of user IDs to create progress for
   * @param startDate - Start date for the target period
   * @param endDate - End date for the target period
   * @param tx - Optional transaction client
   * @returns Promise<void>
   */
  async createDailyRoleTargetProgress(
    targetIds: string[],
    userIds: string[],
    startDate: Date,
    endDate: Date,
    tx?: PrismaTransaction,
  ): Promise<void> {
    // Generate all dates in the range
    const dates = generateDateRange(startDate, endDate);

    // Create progress records for each user, each target, and each date
    const progressRecords: any[] = [];

    for (const userId of userIds) {
      for (const targetId of targetIds) {
        for (const date of dates) {
          const periodStart = createDateAt2AM(date);
          const periodEnd = createDateAt2AM(date);

          // Check if the date is applicable (not weekend or holiday)
          const applicable = await isApplicable(date, this.prisma);

          progressRecords.push({
            target_id: targetId,
            user_id: userId,
            period_start: periodStart,
            period_end: periodEnd,
            achieved_count: 0,
            is_achieved: null,
            is_applicable: applicable,
          });
        }
      }
    }

    // Batch create all progress records
    if (progressRecords.length > 0) {
      const client = tx || this.prisma;
      await client.targetProgress.createMany({
        data: progressRecords,
        skipDuplicates: true, // Skip if duplicate exists
      });
    }
  }

  /**
   * Create TargetProgress records for daily targets when start and end dates are the same
   * @param targetIds - Array of target IDs to link progress records to
   * @param userIds - Array of user IDs to create progress for
   * @param date - The specific date for the target
   * @param tx - Optional transaction client
   * @returns Promise<void>
   */
  async createDailyRoleTargetProgressSingleDay(
    targetIds: string[],
    userIds: string[],
    date: Date,
    tx?: PrismaTransaction,
  ): Promise<void> {
    const periodStart = createDateAt2AM(date);
    const periodEnd = createDateAt2AM(date);

    // Check if the date is applicable (not weekend or holiday)
    const applicable = await isApplicable(date, this.prisma);

    const progressRecords: any[] = [];

    for (const userId of userIds) {
      for (const targetId of targetIds) {
        progressRecords.push({
          target_id: targetId,
          user_id: userId,
          period_start: periodStart,
          period_end: periodEnd,
          achieved_count: 0,
          is_achieved: null,
          is_applicable: applicable,
        });
      }
    }

    // Batch create all progress records
    if (progressRecords.length > 0) {
      const client = tx || this.prisma;
      await client.targetProgress.createMany({
        data: progressRecords,
        skipDuplicates: true, // Skip if duplicate exists
      });
    }
  }

  /**
   * Create TargetProgress records for daily targets with today's date
   * @param targetIds - Array of target IDs to link progress records to
   * @param userIds - Array of user IDs to create progress for
   * @returns Promise<void>
   */
  async createDailyRoleTargetProgressToday(
    targetIds: string[],
    userIds: string[],
  ): Promise<void> {
    const today = new Date();
    const periodStart = getTodayAt2AM();
    const periodEnd = getTodayAt2AM();

    // Check if today is applicable (not weekend or holiday)
    const applicable = await isApplicable(today, this.prisma);

    const progressRecords: any[] = [];

    for (const userId of userIds) {
      for (const targetId of targetIds) {
        progressRecords.push({
          target_id: targetId,
          user_id: userId,
          period_start: periodStart,
          period_end: periodEnd,
          achieved_count: 0,
          is_achieved: null,
          is_applicable: applicable,
        });
      }
    }

    // Batch create all progress records
    if (progressRecords.length > 0) {
      await this.prisma.targetProgress.createMany({
        data: progressRecords,
        skipDuplicates: true, // Skip if duplicate exists
      });
    }
  }

  /**
   * Get all users belonging to specific roles
   * @param roleIds - Array of role IDs
   * @param branchId - Optional branch ID to filter users by branch
   * @returns Promise<string[]> - Array of user IDs
   */
  async getUsersByRoles(roleIds: string[], branchId?: string): Promise<string[]> {
    const whereClause: any = {
      role_id: {
        in: roleIds,
      },
    };

    if (branchId) {
      whereClause.branch_id = branchId;
    }

    const users = await this.prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
      },
    });

    return users.map((user) => user.id);
  }
}
