import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseUUIDPipe,
  ParseBoolPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { AnchorsService } from './anchors.service';
import { PaginationDto } from '../common/dto/pagination.dto';
import {
  CreateAnchorDto,
  UpdateAnchorDto,
  AnchorResponseDto,
  AnchorWithLeadsResponseDto,
  AnchorListResponseDto,
  AnchorStatsResponseDto,
} from './dto';

/**
 * AnchorsController
 * 
 * RESTful API controller for managing Anchor entities.
 * Provides comprehensive CRUD operations with proper HTTP status codes,
 * validation, error handling, and API documentation.
 * 
 * Base URL: /api/v1/anchors
 * 
 * Features:
 * - Full CRUD operations (Create, Read, Update, Delete)
 * - Pagination and search capabilities
 * - Usage statistics and analytics
 * - Advanced search functionality
 * - Comprehensive error handling
 * - Swagger API documentation
 * - Input validation and sanitization
 * - Optimized database queries
 */
@ApiTags('Anchors')
@Controller('anchors')
export class AnchorsController {
  constructor(private readonly anchorsService: AnchorsService) {}

  /**
   * Create a new Anchor
   * POST /anchors
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new anchor',
    description: 
      'Creates a new anchor with the provided name, email, and phone number. ' +
      'Email and phone number must be unique across all anchors. ' +
      'Returns the created anchor with usage statistics.',
  })
  @ApiCreatedResponse({
    description: 'Anchor created successfully',
    type: AnchorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or validation failed',
  })
  @ApiConflictResponse({
    description: 'Anchor with this email or phone number already exists',
  })
  async create(
    @Body(ValidationPipe) createAnchorDto: CreateAnchorDto,
  ): Promise<AnchorResponseDto> {
    return this.anchorsService.create(createAnchorDto);
  }

  /**
   * Get all anchors with pagination and search
   * GET /anchors
   */
  @Get()
  @ApiOperation({
    summary: 'Get all anchors with pagination',
    description:
      'Retrieves all anchors with pagination support and optional search filtering. ' +
      'Search is performed across name, email, and phone number fields (case-insensitive). ' +
      'Results include usage statistics for each anchor.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (1-based)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term to filter anchors by name, email, or phone',
    example: 'ABC Corp',
  })
  @ApiOkResponse({
    description: 'Anchors retrieved successfully',
    type: AnchorListResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters',
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<AnchorListResponseDto> {
    return this.anchorsService.findAll(paginationDto);
  }

  /**
   * Get usage statistics for all anchors
   * GET /anchors/statistics
   */
  @Get('statistics')
  @ApiOperation({
    summary: 'Get usage statistics for anchors',
    description:
      'Provides comprehensive analytics about anchor usage across the system. ' +
      'Includes total counts, most active anchors, and unused anchors list.',
  })
  @ApiOkResponse({
    description: 'Usage statistics retrieved successfully',
    type: AnchorStatsResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Failed to retrieve statistics',
  })
  async getStatistics(): Promise<AnchorStatsResponseDto> {
    return this.anchorsService.getUsageStatistics();
  }

  /**
   * Search anchors with advanced options
   * GET /anchors/search
   */
  @Get('search')
  @ApiOperation({
    summary: 'Advanced search for anchors',
    description:
      'Performs advanced search across anchor fields with customizable search criteria. ' +
      'Allows searching in specific fields (name, email, phone) for more targeted results.',
  })
  @ApiQuery({
    name: 'q',
    required: true,
    type: String,
    description: 'Search term',
    example: 'ABC',
  })
  @ApiQuery({
    name: 'fields',
    required: false,
    type: String,
    description: 'Comma-separated list of fields to search (name,email,phone)',
    example: 'name,email',
  })
  @ApiOkResponse({
    description: 'Search completed successfully',
    type: [AnchorResponseDto],
  })
  @ApiBadRequestResponse({
    description: 'Invalid search parameters',
  })
  async searchAnchors(
    @Query('q') searchTerm: string,
    @Query('fields') fields?: string,
  ): Promise<AnchorResponseDto[]> {
    const validFields = ['name', 'email', 'phone'] as const;
    const searchFields: ('name' | 'email' | 'phone')[] = fields
      ? fields.split(',').filter(f => validFields.includes(f as any)).map(f => f as 'name' | 'email' | 'phone')
      : ['name', 'email', 'phone'];

    return this.anchorsService.searchAnchors(searchTerm, searchFields);
  }

  /**
   * Get a specific anchor by ID
   * GET /anchors/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get an anchor by ID',
    description:
      'Retrieves a specific anchor by its UUID. ' +
      'Optionally includes detailed lead information associated with the anchor.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the anchor to retrieve',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'includeLeads',
    required: false,
    type: Boolean,
    description: 'Whether to include detailed lead information',
    example: false,
  })
  @ApiOkResponse({
    description: 'Anchor retrieved successfully',
    type: AnchorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Anchor not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('includeLeads', new ParseBoolPipe({ optional: true })) includeLeads?: boolean,
  ): Promise<AnchorResponseDto | AnchorWithLeadsResponseDto> {
    return this.anchorsService.findOne(id, includeLeads || false);
  }

  /**
   * Update an anchor
   * PATCH /anchors/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update an anchor',
    description:
      'Updates an existing anchor with the provided data. ' +
      'Supports partial updates - only provided fields will be updated. ' +
      'Email and phone number uniqueness is validated (excluding the current record).',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the anchor to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Anchor updated successfully',
    type: AnchorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Anchor not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or UUID format',
  })
  @ApiConflictResponse({
    description: 'Anchor with this email or phone number already exists',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateAnchorDto: UpdateAnchorDto,
  ): Promise<AnchorResponseDto> {
    return this.anchorsService.update(id, updateAnchorDto);
  }

  /**
   * Delete an anchor
   * DELETE /anchors/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete an anchor',
    description:
      'Deletes an anchor by its UUID. ' +
      'Deletion is prevented if the anchor is currently in use by any leads ' +
      'to maintain data integrity. Returns confirmation message on success.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the anchor to delete',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Anchor deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Anchor "ABC Corporation" deleted successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Anchor not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format',
  })
  @ApiConflictResponse({
    description: 'Cannot delete anchor as it is currently in use by leads',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<{ message: string }> {
    return this.anchorsService.remove(id);
  }
}
