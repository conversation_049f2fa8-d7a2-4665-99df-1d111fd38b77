import { PartialType } from '@nestjs/swagger';
import { CreateAnchorDto } from './create-anchor.dto';

/**
 * UpdateAnchorDto
 * 
 * Data Transfer Object for updating an existing Anchor.
 * Extends CreateAnchorDto but makes all fields optional,
 * allowing for partial updates of the entity.
 * 
 * This approach ensures consistency between create and update operations
 * while providing flexibility for partial updates.
 * 
 * All validation rules from CreateAnchorDto apply when fields are provided.
 */
export class UpdateAnchorDto extends PartialType(CreateAnchorDto) {
  // All fields from CreateAnchorDto are now optional
  // This allows for partial updates where only specific fields are modified
  // 
  // Available fields for update:
  // - name?: string (2-100 characters)
  // - email?: string (valid email format)
  // - phone_number?: string (valid Kenyan phone format)
}
