import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEmail, IsNotEmpty, <PERSON><PERSON>ength, MinLength, Matches, IsOptional } from 'class-validator';

/**
 * CreateAnchorDto
 * 
 * Data Transfer Object for creating a new Anchor.
 * Validates input data to ensure data integrity and provides
 * clear API documentation through Swagger decorators.
 */
export class CreateAnchorDto {
  /**
   * Name of the anchor organization or entity
   * 
   * This field represents the primary identifier/title for the anchor.
   * Examples: "ABC Corporation", "XYZ Holdings", "Main Street Partners"
   */
  @ApiProperty({
    description: 'Name of the anchor organization or entity',
    example: 'ABC Corporation',
    minLength: 2,
    maxLength: 100,
  })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name: string;

  /**
   * Email address of the anchor
   * 
   * Primary contact email for the anchor organization.
   * Must be a valid email format and will be used for communications.
   */
  @ApiProperty({
    description: 'Email address of the anchor organization',
    example: '<EMAIL>',
    format: 'email',
  })
  @IsString({ message: 'Email must be a string' })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @MaxLength(255, { message: 'Email must not exceed 255 characters' })
  email: string;

  /**
   * Phone number of the anchor
   * 
   * Primary contact phone number for the anchor organization.
   * Supports Kenyan phone number formats (07XXXXXXXX, 01XXXXXXXX, +254XXXXXXXXX).
   */
  @ApiProperty({
    description: 'Phone number of the anchor organization',
    example: '**********',
    pattern: '^(\\+254|0)[17]\\d{8}$',
  })
  @IsString({ message: 'Phone number must be a string' })
  @IsNotEmpty({ message: 'Phone number is required' })
  @Matches(
    /^(\+254|0)[17]\d{8}$/,
    { 
      message: 'Phone number must be a valid Kenyan format (07XXXXXXXX, 01XXXXXXXX, or +254XXXXXXXXX)' 
    }
  )
  phone_number: string;

  /**
   * Account ID of the anchor (optional)
   *
   * Unique account identifier for the anchor organization.
   * This field is optional and can be used to link the anchor to external systems.
   */
  @ApiPropertyOptional({
    description: 'Account ID of the anchor organization (optional)',
    example: 'ACC-ANCHOR-001',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Account ID must be a string' })
  @MaxLength(50, { message: 'Account ID must not exceed 50 characters' })
  account_id?: string;
}
