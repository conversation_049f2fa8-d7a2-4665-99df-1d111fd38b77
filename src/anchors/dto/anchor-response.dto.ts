import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * AnchorResponseDto
 * 
 * Data Transfer Object for Anchor responses.
 * Defines the structure of data returned by the API endpoints.
 * Includes metadata about related entities for better API usability.
 */
export class AnchorResponseDto {
  /**
   * Unique identifier for the anchor
   */
  @ApiProperty({
    description: 'Unique identifier for the anchor',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Name of the anchor organization
   */
  @ApiProperty({
    description: 'Name of the anchor organization',
    example: 'ABC Corporation',
  })
  name: string;

  /**
   * Email address of the anchor
   */
  @ApiProperty({
    description: 'Email address of the anchor organization',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Phone number of the anchor
   */
  @ApiProperty({
    description: 'Phone number of the anchor organization',
    example: '**********',
  })
  phone_number: string;

  /**
   * Account ID of the anchor (optional)
   */
  @ApiPropertyOptional({
    description: 'Account ID of the anchor organization',
    example: 'ACC-ANCHOR-001',
    nullable: true,
  })
  account_id: string | null;

  /**
   * Count of leads associated with this anchor
   */
  @ApiProperty({
    description: 'Number of leads associated with this anchor',
    example: 15,
  })
  leads_count: number;

  /**
   * Indicates if this anchor is currently in use
   */
  @ApiProperty({
    description: 'Whether this anchor is currently being used by any leads',
    example: true,
  })
  is_in_use: boolean;

  /**
   * Timestamp when the anchor was created
   */
  @ApiProperty({
    description: 'Timestamp when the anchor was created',
    example: '2025-08-01T10:30:00.000Z',
  })
  created_at: string;
}

/**
 * AnchorWithLeadsResponseDto
 * 
 * Extended response DTO that includes lead details
 */
export class AnchorWithLeadsResponseDto extends AnchorResponseDto {
  /**
   * Array of leads associated with this anchor
   */
  @ApiProperty({
    description: 'Array of leads associated with this anchor',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440001' },
        customer_name: { type: 'string', example: 'John Doe' },
        phone_number: { type: 'string', example: '0723456789' },
        lead_status: { type: 'string', example: 'Active' },
        type_of_lead: { type: 'string', example: 'New Business' },
      },
    },
  })
  leads: Array<{
    id: string;
    customer_name: string | null;
    phone_number: string | null;
    lead_status: string | null;
    type_of_lead: string | null;
  }>;
}

/**
 * AnchorListResponseDto
 * 
 * Response DTO for paginated list of anchors with metadata
 */
export class AnchorListResponseDto {
  /**
   * Array of anchor objects
   */
  @ApiProperty({
    description: 'Array of anchor objects',
    type: [AnchorResponseDto],
  })
  data: AnchorResponseDto[];

  /**
   * Pagination metadata
   */
  @ApiProperty({
    description: 'Pagination metadata',
    type: 'object',
    properties: {
      total: { type: 'number', example: 156, description: 'Total number of anchors' },
      page: { type: 'number', example: 2, description: 'Current page number' },
      limit: { type: 'number', example: 20, description: 'Items per page' },
      totalPages: { type: 'number', example: 8, description: 'Total number of pages' },
      hasNextPage: { type: 'boolean', example: true, description: 'Whether there is a next page' },
      hasPreviousPage: { type: 'boolean', example: true, description: 'Whether there is a previous page' },
    },
  })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * AnchorStatsResponseDto
 * 
 * Response DTO for anchor usage statistics
 */
export class AnchorStatsResponseDto {
  /**
   * Total number of anchors
   */
  @ApiProperty({
    description: 'Total number of anchors in the system',
    example: 25,
  })
  total_anchors: number;

  /**
   * Number of anchors currently in use
   */
  @ApiProperty({
    description: 'Number of anchors currently being used by leads',
    example: 18,
  })
  used_anchors: number;

  /**
   * Number of unused anchors
   */
  @ApiProperty({
    description: 'Number of anchors not currently being used',
    example: 7,
  })
  unused_anchors: number;

  /**
   * Total number of leads across all anchors
   */
  @ApiProperty({
    description: 'Total number of leads associated with anchors',
    example: 142,
  })
  total_leads: number;

  /**
   * Most active anchors by lead count
   */
  @ApiProperty({
    description: 'Top 5 anchors with the most leads',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        leads_count: { type: 'number' },
      },
    },
  })
  most_active_anchors: Array<{
    id: string;
    name: string;
    leads_count: number;
  }>;

  /**
   * List of unused anchors
   */
  @ApiProperty({
    description: 'Anchors that are not currently being used',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string' },
        created_at: { type: 'string' },
      },
    },
  })
  unused_anchor_list: Array<{
    id: string;
    name: string;
    email: string;
    created_at: string;
  }>;
}
