import { Modu<PERSON> } from '@nestjs/common';
import { AnchorsController } from './anchors.controller';
import { AnchorsService } from './anchors.service';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * AnchorsModule
 * 
 * This module handles all operations related to Anchor entities.
 * Anchors are business entities that can be referenced by leads,
 * providing a way to organize and track lead sources.
 * 
 * Features:
 * - Create new anchors with validation
 * - Read/List anchors with pagination and search
 * - Update existing anchors
 * - Delete anchors (with relationship validation)
 * - Optimized database queries with selective field inclusion
 * - Relationship management with Lead entities
 * - Usage statistics and analytics
 */
@Module({
  imports: [PrismaModule],
  controllers: [AnchorsController],
  providers: [AnchorsService],
  exports: [AnchorsService], // Export service for use in other modules
})
export class AnchorsModule {}
