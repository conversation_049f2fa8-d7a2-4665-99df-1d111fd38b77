import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { PaginationDto } from '../common/dto/pagination.dto';
import { 
  CreateAnchorDto, 
  UpdateAnchorDto,
  AnchorResponseDto,
  AnchorWithLeadsResponseDto,
  AnchorListResponseDto,
  AnchorStatsResponseDto
} from './dto';

/**
 * AnchorsService
 * 
 * Service class handling all business logic for Anchor operations.
 * Implements optimized database queries, proper error handling, and data validation.
 * 
 * Key Features:
 * - Optimized queries with selective field inclusion
 * - Relationship counting for usage statistics
 * - Duplicate email/phone prevention
 * - Cascade delete protection
 * - Comprehensive error handling
 * - Pagination support with search
 * - Performance optimizations with parallel queries
 */
@Injectable()
export class AnchorsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new Anchor
   * 
   * Validates that email and phone are unique before creation to prevent duplicates.
   * Returns the created entity with usage statistics.
   * 
   * @param createDto - Data for creating the anchor
   * @returns Promise<AnchorResponseDto> - Created anchor with metadata
   * @throws ConflictException - If email or phone already exists
   * @throws BadRequestException - If validation fails
   */
  async create(createDto: CreateAnchorDto): Promise<AnchorResponseDto> {
    try {
      // Check for duplicate email, phone, and account_id in parallel for better performance
      const [existingEmail, existingPhone, existingAccountId] = await Promise.all([
        this.prisma.anchor.findFirst({
          where: {
            email: {
              equals: createDto.email.toLowerCase().trim(),
              mode: 'insensitive',
            },
          },
          select: { id: true, email: true },
        }),
        this.prisma.anchor.findFirst({
          where: {
            phone_number: createDto.phone_number.trim(),
          },
          select: { id: true, phone_number: true },
        }),
        createDto.account_id
          ? this.prisma.anchor.findFirst({
              where: {
                account_id: createDto.account_id.trim(),
              },
              select: { id: true, account_id: true },
            })
          : null,
      ]);

      if (existingEmail) {
        throw new ConflictException(
          `Anchor with email "${createDto.email}" already exists`
        );
      }

      if (existingPhone) {
        throw new ConflictException(
          `Anchor with phone number "${createDto.phone_number}" already exists`
        );
      }

      if (existingAccountId) {
        throw new ConflictException(
          `Anchor with account ID "${createDto.account_id}" already exists`
        );
      }

      // Create the new anchor
      const anchor = await this.prisma.anchor.create({
        data: {
          name: createDto.name.trim(),
          email: createDto.email.toLowerCase().trim(),
          phone_number: createDto.phone_number.trim(),
          account_id: createDto.account_id ? createDto.account_id.trim() : null,
        },
        // Include relationship counts for immediate response
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
      });

      return this.formatAnchorResponse(anchor);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create anchor: ${error.message}`
      );
    }
  }

  /**
   * Retrieves all anchors with pagination and optional search
   * 
   * Implements efficient querying with relationship counting.
   * Supports case-insensitive search across name, email, and phone fields.
   * Uses parallel queries for optimal performance.
   * 
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<AnchorListResponseDto> - Paginated list of anchors with metadata
   */
  async findAll(paginationDto: PaginationDto): Promise<AnchorListResponseDto> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build search conditions if search term provided
      const whereClause = search
        ? {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
              {
                email: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
              {
                phone_number: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            ],
          }
        : {};

      // Execute queries in parallel for better performance
      const [anchors, total] = await Promise.all([
        this.prisma.anchor.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            _count: {
              select: {
                leads: true,
              },
            },
          },
          orderBy: [
            { name: 'asc' }, // Primary sort by name
            { created_at: 'desc' }, // Secondary sort by creation date
          ],
        }),
        this.prisma.anchor.count({
          where: whereClause,
        }),
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);

      // Format response data
      const formattedAnchors = anchors.map(anchor => this.formatAnchorResponse(anchor));

      return {
        data: formattedAnchors,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve anchors: ${error.message}`
      );
    }
  }

  /**
   * Retrieves a single anchor by ID with optional lead details
   * 
   * Includes comprehensive relationship data and usage statistics.
   * Optionally includes detailed lead information.
   * 
   * @param id - UUID of the anchor to retrieve
   * @param includeLeads - Whether to include detailed lead information
   * @returns Promise<AnchorResponseDto | AnchorWithLeadsResponseDto> - Anchor with metadata
   * @throws NotFoundException - If anchor not found
   */
  async findOne(id: string, includeLeads: boolean = false): Promise<AnchorResponseDto | AnchorWithLeadsResponseDto> {
    try {
      const anchor = await this.prisma.anchor.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
          ...(includeLeads && {
            leads: {
              select: {
                id: true,
                customer_name: true,
                account_number: true,
                account_number_assigned_at: true,
                phone_number: true,
                lead_status: true,
                type_of_lead: true,
              },
              orderBy: {
                customer_name: 'asc',
              },
            },
          }),
        },
      });

      if (!anchor) {
        throw new NotFoundException(`Anchor with ID "${id}" not found`);
      }

      if (includeLeads) {
        return this.formatAnchorWithLeadsResponse(anchor);
      }

      return this.formatAnchorResponse(anchor);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve anchor: ${error.message}`
      );
    }
  }

  /**
   * Updates an existing anchor
   *
   * Validates email and phone uniqueness (excluding current record) and handles partial updates.
   * Only updates fields that are provided in the DTO.
   *
   * @param id - UUID of the anchor to update
   * @param updateDto - Data for updating the anchor
   * @returns Promise<AnchorResponseDto> - Updated anchor with metadata
   * @throws NotFoundException - If anchor not found
   * @throws ConflictException - If email or phone conflicts with another anchor
   */
  async update(id: string, updateDto: UpdateAnchorDto): Promise<AnchorResponseDto> {
    try {
      // First, verify the anchor exists
      const existingAnchor = await this.prisma.anchor.findUnique({
        where: { id },
        select: { id: true, name: true, email: true, phone_number: true, account_id: true },
      });

      if (!existingAnchor) {
        throw new NotFoundException(`Anchor with ID "${id}" not found`);
      }

      // Check for email and phone conflicts if they are being updated
      const emailCheck = updateDto.email && updateDto.email.toLowerCase().trim() !== existingAnchor.email.toLowerCase()
        ? this.prisma.anchor.findFirst({
            where: {
              email: {
                equals: updateDto.email.toLowerCase().trim(),
                mode: 'insensitive',
              },
              NOT: { id }, // Exclude current record
            },
            select: { id: true, email: true },
          })
        : Promise.resolve(null);

      const phoneCheck = updateDto.phone_number && updateDto.phone_number.trim() !== existingAnchor.phone_number
        ? this.prisma.anchor.findFirst({
            where: {
              phone_number: updateDto.phone_number.trim(),
              NOT: { id }, // Exclude current record
            },
            select: { id: true, phone_number: true },
          })
        : Promise.resolve(null);

      const accountIdCheck = updateDto.account_id && updateDto.account_id.trim() !== existingAnchor.account_id
        ? this.prisma.anchor.findFirst({
            where: {
              account_id: updateDto.account_id.trim(),
              NOT: { id }, // Exclude current record
            },
            select: { id: true, account_id: true },
          })
        : Promise.resolve(null);

      const [conflictingEmail, conflictingPhone, conflictingAccountId] = await Promise.all([emailCheck, phoneCheck, accountIdCheck]);

      if (conflictingEmail) {
        throw new ConflictException(
          `Anchor with email "${updateDto.email}" already exists`
        );
      }

      if (conflictingPhone) {
        throw new ConflictException(
          `Anchor with phone number "${updateDto.phone_number}" already exists`
        );
      }

      if (conflictingAccountId) {
        throw new ConflictException(
          `Anchor with account ID "${updateDto.account_id}" already exists`
        );
      }

      // Build update data object with only provided fields
      const updateData: any = {};
      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name.trim();
      }
      if (updateDto.email !== undefined) {
        updateData.email = updateDto.email.toLowerCase().trim();
      }
      if (updateDto.phone_number !== undefined) {
        updateData.phone_number = updateDto.phone_number.trim();
      }
      if (updateDto.account_id !== undefined) {
        updateData.account_id = updateDto.account_id ? updateDto.account_id.trim() : null;
      }

      // Perform the update
      const updatedAnchor = await this.prisma.anchor.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
      });

      return this.formatAnchorResponse(updatedAnchor);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update anchor: ${error.message}`
      );
    }
  }

  /**
   * Deletes an anchor
   *
   * Implements cascade protection by checking for related leads before deletion.
   * Prevents deletion if the anchor is currently in use to maintain data integrity.
   *
   * @param id - UUID of the anchor to delete
   * @returns Promise<{ message: string }> - Success confirmation
   * @throws NotFoundException - If anchor not found
   * @throws ConflictException - If anchor is in use by leads
   */
  async remove(id: string): Promise<{ message: string }> {
    try {
      // First, verify the anchor exists and get usage counts
      const anchor = await this.prisma.anchor.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
      });

      if (!anchor) {
        throw new NotFoundException(`Anchor with ID "${id}" not found`);
      }

      // Check if anchor is in use
      const leadsCount = anchor._count.leads;
      if (leadsCount > 0) {
        throw new ConflictException(
          `Cannot delete anchor "${anchor.name}" as it is currently used by ${leadsCount} leads. ` +
          `Please reassign or remove these leads first.`
        );
      }

      // Safe to delete
      await this.prisma.anchor.delete({
        where: { id },
      });

      return {
        message: `Anchor "${anchor.name}" deleted successfully`,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete anchor: ${error.message}`
      );
    }
  }

  /**
   * Gets usage statistics for all anchors
   *
   * Provides comprehensive analytics about anchor usage across the system.
   * Useful for reporting and identifying unused anchors.
   * Uses optimized queries with aggregations for performance.
   *
   * @returns Promise<AnchorStatsResponseDto> - Usage statistics and analytics
   */
  async getUsageStatistics(): Promise<AnchorStatsResponseDto> {
    try {
      const [anchors, totalLeads] = await Promise.all([
        this.prisma.anchor.findMany({
          include: {
            _count: {
              select: {
                leads: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.lead.count({
          where: {
            anchor_id: {
              not: null,
            },
          },
        }),
      ]);

      const usedAnchors = anchors.filter((a: any) => a._count.leads > 0);
      const unusedAnchors = anchors.filter((a: any) => a._count.leads === 0);

      // Sort used anchors by lead count for most active list
      const sortedUsedAnchors = usedAnchors.sort((a: any, b: any) => b._count.leads - a._count.leads);

      return {
        total_anchors: anchors.length,
        used_anchors: usedAnchors.length,
        unused_anchors: unusedAnchors.length,
        total_leads: totalLeads,
        most_active_anchors: sortedUsedAnchors.slice(0, 5).map((a: any) => ({
          id: a.id,
          name: a.name,
          leads_count: a._count.leads,
        })),
        unused_anchor_list: unusedAnchors.map((a: any) => ({
          id: a.id,
          name: a.name,
          email: a.email,
          created_at: a.created_at.toISOString(),
        })),
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve usage statistics: ${error.message}`
      );
    }
  }

  /**
   * Searches anchors by various criteria
   *
   * Advanced search functionality with multiple search options.
   * Optimized for performance with selective field inclusion.
   *
   * @param searchTerm - Term to search for
   * @param searchFields - Fields to search in (name, email, phone)
   * @returns Promise<AnchorResponseDto[]> - Matching anchors
   */
  async searchAnchors(
    searchTerm: string,
    searchFields: ('name' | 'email' | 'phone')[] = ['name', 'email', 'phone']
  ): Promise<AnchorResponseDto[]> {
    try {
      const searchConditions: any[] = [];

      if (searchFields.includes('name')) {
        searchConditions.push({
          name: {
            contains: searchTerm,
            mode: 'insensitive' as const,
          },
        });
      }

      if (searchFields.includes('email')) {
        searchConditions.push({
          email: {
            contains: searchTerm,
            mode: 'insensitive' as const,
          },
        });
      }

      if (searchFields.includes('phone')) {
        searchConditions.push({
          phone_number: {
            contains: searchTerm,
            mode: 'insensitive' as const,
          },
        });
      }

      const anchors = await this.prisma.anchor.findMany({
        where: {
          OR: searchConditions,
        },
        include: {
          _count: {
            select: {
              leads: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });

      return anchors.map(anchor => this.formatAnchorResponse(anchor));
    } catch (error) {
      throw new BadRequestException(
        `Failed to search anchors: ${error.message}`
      );
    }
  }

  /**
   * Formats an anchor entity for API response
   *
   * Private helper method that transforms database entity into response DTO format.
   * Calculates derived fields and ensures consistent response structure.
   *
   * @param anchor - Raw anchor entity from database
   * @returns AnchorResponseDto - Formatted response object
   */
  private formatAnchorResponse(anchor: any): AnchorResponseDto {
    const leadsCount = anchor._count?.leads || 0;

    return {
      id: anchor.id,
      name: anchor.name,
      email: anchor.email,
      phone_number: anchor.phone_number,
      account_id: anchor.account_id,
      leads_count: leadsCount,
      is_in_use: leadsCount > 0,
      created_at: anchor.created_at.toISOString(),
    };
  }

  /**
   * Formats an anchor entity with leads for API response
   *
   * Private helper method for detailed anchor responses including lead information.
   *
   * @param anchor - Raw anchor entity with leads from database
   * @returns AnchorWithLeadsResponseDto - Formatted response object with leads
   */
  private formatAnchorWithLeadsResponse(anchor: any): AnchorWithLeadsResponseDto {
    const baseResponse = this.formatAnchorResponse(anchor);

    return {
      ...baseResponse,
      leads: anchor.leads?.map((lead: any) => ({
        id: lead.id,
        customer_name: lead.customer_name,
        phone_number: lead.phone_number,
        lead_status: lead.lead_status,
        type_of_lead: lead.type_of_lead,
      })) || [],
    };
  }
}
