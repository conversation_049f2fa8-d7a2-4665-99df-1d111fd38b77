import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bullmq';
import { ScheduledTaskService } from '../../scheduled-tasks/scheduled-task.service';
import { TaskHandlerRegistry } from '../../scheduled-tasks/task-handlers/task-handler.registry';

export interface ScheduledTaskJobData {
  taskId: string;
  type: string;
  payload: any;
  attemptNumber: number;
}

@Processor('scheduled-tasks')
export class ScheduledTaskProcessor extends WorkerHost {
  private readonly logger = new Logger(ScheduledTaskProcessor.name);
  private readonly workerId: string;

  constructor(
    @Inject(forwardRef(() => ScheduledTaskService))
    private readonly scheduledTaskService: ScheduledTaskService,
    @Inject(forwardRef(() => TaskHandlerRegistry))
    private readonly taskHandlerRegistry: TaskHandlerRegistry,
  ) {
    super();
    this.workerId = `worker-${process.pid}-${Date.now()}`;
  }

  async process(job: Job<ScheduledTaskJobData>) {
    const { taskId, type, payload, attemptNumber } = job.data;
    const startTime = Date.now();

    this.logger.log(`Processing scheduled task ${taskId} of type ${type} (attempt ${attemptNumber})`);

    try {
      // Mark task as running and create execution record
      const { task, execution } = await this.scheduledTaskService.markAsRunning(taskId, this.workerId);
      
      // Update job progress
      await job.updateProgress(10);

      // Process the task using the task handler registry
      const result = await this.taskHandlerRegistry.executeTask(type, payload, job);

      await job.updateProgress(90);

      // Calculate execution duration
      const durationMs = Date.now() - startTime;

      // Mark task as completed
      await this.scheduledTaskService.markAsCompleted(taskId, execution.id, result, durationMs);

      await job.updateProgress(100);

      this.logger.log(`Completed scheduled task ${taskId} in ${durationMs}ms`);
      return result;

    } catch (error) {
      const durationMs = Date.now() - startTime;
      const errorMessage = error.message || 'Unknown error occurred';

      this.logger.error(`Failed to process scheduled task ${taskId}: ${errorMessage}`, error.stack);

      // Get the execution record to update it
      const task = await this.scheduledTaskService.findOne(taskId);
      const execution = task.executions[0]; // Most recent execution

      // Mark task as failed
      await this.scheduledTaskService.markAsFailed(taskId, execution.id, errorMessage, durationMs);

      throw error;
    }
  }


}
