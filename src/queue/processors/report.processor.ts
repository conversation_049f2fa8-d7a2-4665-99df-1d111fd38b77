import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { ReportJobData } from '../queue.service';
import { PrismaService } from '../../prisma/prisma.service';
import { FileStorageService } from '../../common/services/file-storage.service';
import * as ExcelJS from 'exceljs';
import { format } from 'date-fns';
import { promises as fs } from 'fs';
import * as path from 'path';

@Processor('reports')
export class ReportProcessor extends WorkerHost {
  private readonly logger = new Logger(ReportProcessor.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly fileStorage: FileStorageService,
  ) {
    super();
  }

  async process(job: Job<ReportJobData>) {
    this.logger.log(`Processing report job ${job.id} for user ${job.data.userId}`);

    try {
      const { userId, reportType, filters, format: outputFormat } = job.data;

      // Update job progress
      await job.updateProgress(10);

      // Generate report data based on type
      let reportData: any[];
      let reportTitle: string;

      switch (reportType) {
        case 'leads':
          reportData = await this.generateLeadsReport(filters);
          reportTitle = 'Leads Report';
          break;
        case 'activities':
          reportData = await this.generateActivitiesReport(filters);
          reportTitle = 'Activities Report';
          break;
        case 'targets':
          reportData = await this.generateTargetsReport(filters);
          reportTitle = 'Targets Report';
          break;
        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }

      await job.updateProgress(50);

      // Generate file based on format
      let filePath: string;
      const timestamp = format(new Date(), 'yyyy-MM-dd-HH-mm-ss');
      const fileName = `${reportType}-report-${timestamp}`;

      switch (outputFormat) {
        case 'excel':
          filePath = await this.generateExcelReport(reportData, reportTitle, fileName);
          break;
        case 'csv':
          filePath = await this.generateCsvReport(reportData, fileName);
          break;
        default:
          throw new Error(`Unsupported format: ${outputFormat}`);
      }

      await job.updateProgress(90);

      this.logger.log(`Report generated successfully: ${filePath}`);

      await job.updateProgress(100);

      return {
        success: true,
        filePath,
        fileName: `${fileName}.${outputFormat}`,
        recordCount: reportData.length,
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate report: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async generateLeadsReport(filters: any) {
    const where: any = {};

    if (filters.branchId) {
      where.branch_id = filters.branchId;
    }

    if (filters.dateFrom || filters.dateTo) {
      where.created_at = {};
      if (filters.dateFrom) {
        where.created_at.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.created_at.lte = new Date(filters.dateTo);
      }
    }

    return this.prisma.lead.findMany({
      where,
      include: {
        branch: true,
        rm_user: true,
        customer_category: true,
        isic_sector: true,
      },
      orderBy: { created_at: 'desc' },
    });
  }

  private async generateActivitiesReport(filters: any) {
    const where: any = {};

    if (filters.userId) {
      where.performed_by_user_id = filters.userId;
    }

    if (filters.dateFrom || filters.dateTo) {
      where.created_at = {};
      if (filters.dateFrom) {
        where.created_at.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.created_at.lte = new Date(filters.dateTo);
      }
    }

    return this.prisma.activity.findMany({
      where,
      include: {
        performed_by: true,
        lead: true,
        purpose: true,
      },
      orderBy: { created_at: 'desc' },
    });
  }

  private async generateTargetsReport(filters: any) {
    const where: any = {};

    if (filters.branchId) {
      where.branch_id = filters.branchId;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    return this.prisma.target.findMany({
      where,
      include: {
        user: true,
        role: true,
        branch: true,
        target_progress: true,
      },
      orderBy: { created_at: 'desc' },
    });
  }

  private async generateExcelReport(data: any[], title: string, fileName: string): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(title);

    if (data.length > 0) {
      // Add headers
      const headers = Object.keys(data[0]);
      worksheet.addRow(headers);

      // Style headers
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      // Add data
      data.forEach(row => {
        worksheet.addRow(Object.values(row));
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    const filePath = `reports/${fileName}.xlsx`;
    await workbook.xlsx.writeFile(filePath);
    return filePath;
  }

  private async generateCsvReport(data: any[], fileName: string): Promise<string> {
    if (data.length === 0) {
      return '';
    }

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        Object.values(row).map(value =>
          typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
        ).join(',')
      )
    ].join('\n');

    const filePath = `reports/${fileName}.csv`;

    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, csvContent);

    return filePath;
  }
}
