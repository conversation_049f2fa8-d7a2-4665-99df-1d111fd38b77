import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { EmailService } from '../../common/services/email.service';
import { EmailJobData } from '../queue.service';

@Processor('email')
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);

  constructor(private readonly emailService: EmailService) {
    super();
  }

  async process(job: Job<EmailJobData>) {
    this.logger.log(`Processing email job ${job.id} for ${job.data.to}`);

    try {
      await this.emailService.sendEmail(
        job.data.to,
        job.data.subject,
        job.data.template,
        job.data.context,
      );

      this.logger.log(`Email sent successfully to ${job.data.to}`);
      return { success: true, recipient: job.data.to };
    } catch (error) {
      this.logger.error(
        `Failed to send email to ${job.data.to}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
