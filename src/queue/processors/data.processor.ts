import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { DataProcessingJobData } from '../queue.service';
import { PrismaService } from '../../prisma/prisma.service';
import * as ExcelJS from 'exceljs';
import { readFile } from 'fs/promises';

@Processor('data-processing')
export class DataProcessor extends WorkerHost {
  private readonly logger = new Logger(DataProcessor.name);

  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async process(job: Job<DataProcessingJobData>) {
    this.logger.log(`Processing data job ${job.id} of type ${job.data.type}`);

    try {
      const { type, fileUrl, data, userId, metadata } = job.data;

      await job.updateProgress(10);

      let result: any;

      switch (type) {
        case 'excel-import':
          result = await this.handleExcelImport(fileUrl!, userId, metadata, job);
          break;
        case 'bulk-update':
          result = await this.handleBulkUpdate(data!, userId, metadata, job);
          break;
        case 'data-migration':
          result = await this.handleDataMigration(data!, userId, metadata, job);
          break;
        default:
          throw new Error(`Unknown data processing type: ${type}`);
      }

      await job.updateProgress(100);

      this.logger.log(`Data processing completed successfully`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to process data: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async handleExcelImport(
    fileUrl: string,
    userId: string,
    metadata: any,
    job: Job,
  ) {
    await job.updateProgress(20);

    // Read Excel file
    const fileBuffer = await readFile(fileUrl);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileBuffer);

    await job.updateProgress(30);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error('No worksheet found in Excel file');
    }

    const rows: any[] = [];
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // Skip header row
        const rowData: any = {};
        row.eachCell((cell, colNumber) => {
          rowData[`col_${colNumber}`] = cell.value;
        });
        rows.push(rowData);
      }
    });

    await job.updateProgress(50);

    // Process based on import type
    const importType = metadata?.importType;
    let processedCount = 0;
    let errors: any[] = [];

    switch (importType) {
      case 'leads':
        ({ processedCount, errors } = await this.processLeadsImport(rows, userId, job));
        break;
      case 'activities':
        ({ processedCount, errors } = await this.processActivitiesImport(rows, userId, job));
        break;
      default:
        throw new Error(`Unknown import type: ${importType}`);
    }

    return {
      success: true,
      totalRows: rows.length,
      processedCount,
      errorCount: errors.length,
      errors,
    };
  }

  private async processLeadsImport(rows: any[], userId: string, job: Job) {
    let processedCount = 0;
    const errors: any[] = [];
    const batchSize = 50; // Reduced batch size for better memory management

    for (let i = 0; i < rows.length; i += batchSize) {
      const batch = rows.slice(i, i + batchSize);
      
      for (const row of batch) {
        try {
          // Map Excel columns to lead fields
          const leadData = {
            customer_name: row.col_1,
            phone_number: row.col_2,
            type_of_lead: row.col_3,
            rm_user_id: userId,
            created_at: new Date(),
            updated_at: new Date(),
          };

          await this.prisma.lead.create({
            data: leadData,
          });

          processedCount++;
        } catch (error) {
          errors.push({
            row: i + processedCount + 1,
            error: error.message,
            data: row,
          });
        }
      }

      // Update progress
      const progress = 50 + ((i + batchSize) / rows.length) * 40;
      await job.updateProgress(Math.min(progress, 90));
    }

    return { processedCount, errors };
  }

  private async processActivitiesImport(rows: any[], userId: string, job: Job) {
    let processedCount = 0;
    const errors: any[] = [];
    const batchSize = 100;

    for (let i = 0; i < rows.length; i += batchSize) {
      const batch = rows.slice(i, i + batchSize);

      for (const row of batch) {
        try {
          // Map Excel columns to activity fields
          const activityData = {
            activity_type: row.col_1,
            interaction_type: row.col_2,
            notes: row.col_3,
            performed_by_user_id: userId,
            created_at: new Date(),
            updated_at: new Date(),
          };

          await this.prisma.activity.create({
            data: activityData,
          });

          processedCount++;
        } catch (error) {
          errors.push({
            row: i + processedCount + 1,
            error: error.message,
            data: row,
          });
        }
      }

      // Force garbage collection after each batch to free memory
      if (global.gc) {
        global.gc();
      }

      // Update progress
      const progress = 50 + ((i + batchSize) / rows.length) * 40;
      await job.updateProgress(Math.min(progress, 90));

      // Add small delay to prevent overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    return { processedCount, errors };
  }

  private async handleBulkUpdate(
    data: any[],
    userId: string,
    metadata: any,
    job: Job,
  ) {
    await job.updateProgress(30);

    const updateType = metadata?.updateType;
    let updatedCount = 0;
    const errors: any[] = [];

    for (let i = 0; i < data.length; i++) {
      try {
        const item = data[i];
        
        switch (updateType) {
          case 'leads':
            await this.prisma.lead.update({
              where: { id: item.id },
              data: item.updates,
            });
            break;
          case 'activities':
            await this.prisma.activity.update({
              where: { id: item.id },
              data: item.updates,
            });
            break;
          default:
            throw new Error(`Unknown update type: ${updateType}`);
        }

        updatedCount++;
      } catch (error) {
        errors.push({
          index: i,
          error: error.message,
          data: data[i],
        });
      }

      // Update progress
      const progress = 30 + ((i + 1) / data.length) * 60;
      await job.updateProgress(Math.min(progress, 90));
    }

    return {
      success: true,
      totalItems: data.length,
      updatedCount,
      errorCount: errors.length,
      errors,
    };
  }

  private async handleDataMigration(
    data: any[],
    userId: string,
    metadata: any,
    job: Job,
  ) {
    await job.updateProgress(30);

    const migrationType = metadata?.migrationType;
    let migratedCount = 0;
    const errors: any[] = [];

    // Implement specific migration logic based on type
    this.logger.log(`Starting data migration of type: ${migrationType}`);

    for (let i = 0; i < data.length; i++) {
      try {
        // Implement migration logic here
        // This is a placeholder for actual migration code
        migratedCount++;
      } catch (error) {
        errors.push({
          index: i,
          error: error.message,
          data: data[i],
        });
      }

      // Update progress
      const progress = 30 + ((i + 1) / data.length) * 60;
      await job.updateProgress(Math.min(progress, 90));
    }

    return {
      success: true,
      totalItems: data.length,
      migratedCount,
      errorCount: errors.length,
      errors,
    };
  }
}
