import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ConfigService } from '@nestjs/config';
import { MemoryMonitor } from '../common/utils/memory-monitor';

@Injectable()
export class QueueCleanupService {
  private readonly logger = new Logger(QueueCleanupService.name);

  constructor(
    @InjectQueue('email') private emailQueue: Queue,
    @InjectQueue('reports') private reportsQueue: Queue,
    @InjectQueue('data-processing') private dataProcessingQueue: Queue,
    @InjectQueue('scheduled-tasks') private scheduledTasksQueue: Queue,
    private configService: ConfigService,
  ) {}

  /**
   * Clean up completed and failed jobs from all queues
   */
  async cleanupAllQueues() {
    this.logger.log('Starting queue cleanup...');

    const queues = [
      { name: 'email', queue: this.emailQueue },
      { name: 'reports', queue: this.reportsQueue },
      { name: 'data-processing', queue: this.dataProcessingQueue },
      { name: 'scheduled-tasks', queue: this.scheduledTasksQueue },
    ];

    let totalCleaned = 0;

    for (const { name, queue } of queues) {
      try {
        const cleaned = await this.cleanupQueue(queue, name);
        totalCleaned += cleaned;
      } catch (error) {
        this.logger.error(`Failed to cleanup queue ${name}:`, error);
      }
    }

    this.logger.log(`Queue cleanup completed. Total jobs cleaned: ${totalCleaned}`);

    // Force garbage collection after cleanup
    MemoryMonitor.forceGC();

    return totalCleaned;
  }

  /**
   * Clean up a specific queue
   */
  private async cleanupQueue(queue: Queue, queueName: string): Promise<number> {
    const maxAge = this.getMaxAge();
    const maxJobs = this.getMaxJobs();

    this.logger.debug(`Cleaning up queue: ${queueName} (maxAge: ${maxAge}ms, maxJobs: ${maxJobs})`);

    // Clean completed jobs older than maxAge
    const completedCleaned = await queue.clean(maxAge, maxJobs, 'completed');

    // Clean failed jobs older than maxAge (keep fewer failed jobs)
    const failedCleaned = await queue.clean(maxAge, Math.floor(maxJobs / 2), 'failed');

    const totalCleaned = completedCleaned.length + failedCleaned.length;

    if (totalCleaned > 0) {
      this.logger.log(
        `Cleaned ${totalCleaned} jobs from ${queueName} queue ` +
        `(completed: ${completedCleaned.length}, failed: ${failedCleaned.length})`
      );
    }

    return totalCleaned;
  }

  /**
   * Get queue statistics for monitoring
   */
  async getQueueStats() {
    const queues = [
      { name: 'email', queue: this.emailQueue },
      { name: 'reports', queue: this.reportsQueue },
      { name: 'data-processing', queue: this.dataProcessingQueue },
      { name: 'scheduled-tasks', queue: this.scheduledTasksQueue },
    ];

    const stats = {};

    for (const { name, queue } of queues) {
      try {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
          queue.getWaiting(),
          queue.getActive(),
          queue.getCompleted(),
          queue.getFailed(),
          queue.getDelayed(),
        ]);

        stats[name] = {
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length,
          delayed: delayed.length,
          total: waiting.length + active.length + completed.length + failed.length + delayed.length,
        };
      } catch (error) {
        this.logger.error(`Failed to get stats for queue ${name}:`, error);
        stats[name] = { error: error.message };
      }
    }

    return stats;
  }

  /**
   * Emergency cleanup - removes more jobs when memory is critical
   */
  async emergencyCleanup() {
    this.logger.warn('🚨 Performing emergency queue cleanup due to high memory usage');

    const queues = [
      { name: 'email', queue: this.emailQueue },
      { name: 'reports', queue: this.reportsQueue },
      { name: 'data-processing', queue: this.dataProcessingQueue },
      { name: 'scheduled-tasks', queue: this.scheduledTasksQueue },
    ];

    let totalCleaned = 0;

    for (const { name, queue } of queues) {
      try {
        // More aggressive cleanup - shorter max age and fewer max jobs
        const maxAge = 1000 * 60 * 60 * 6; // 6 hours instead of default
        const maxJobs = 50; // Much fewer jobs

        const completedCleaned = await queue.clean(maxAge, maxJobs, 'completed');
        const failedCleaned = await queue.clean(maxAge, Math.floor(maxJobs / 4), 'failed');

        const cleaned = completedCleaned.length + failedCleaned.length;
        totalCleaned += cleaned;

        if (cleaned > 0) {
          this.logger.warn(`Emergency cleanup: removed ${cleaned} jobs from ${name} queue`);
        }
      } catch (error) {
        this.logger.error(`Failed emergency cleanup for queue ${name}:`, error);
      }
    }

    // Force multiple garbage collections
    for (let i = 0; i < 3; i++) {
      MemoryMonitor.forceGC();
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.logger.warn(`Emergency cleanup completed. Total jobs removed: ${totalCleaned}`);
    return totalCleaned;
  }

  /**
   * Get max age for job cleanup from config (default: 24 hours)
   */
  private getMaxAge(): number {
    const hours = this.configService.get<number>('QUEUE_CLEANUP_MAX_AGE_HOURS') || 24;
    return hours * 60 * 60 * 1000; // Convert to milliseconds
  }

  /**
   * Get max number of jobs to keep from config (default: 100)
   */
  private getMaxJobs(): number {
    return this.configService.get<number>('QUEUE_CLEANUP_MAX_JOBS') || 100;
  }
}
