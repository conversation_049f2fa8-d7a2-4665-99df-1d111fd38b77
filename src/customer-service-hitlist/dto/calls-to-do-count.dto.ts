import { ApiProperty } from '@nestjs/swagger';

export class CallsToDoCountDto {
  @ApiProperty({
    description: 'Number of pending calls',
    example: 5,
  })
  pending_count: number;

  @ApiProperty({
    description: 'Number of completed calls',
    example: 10,
  })
  completed_count: number;

  @ApiProperty({
    description: 'Number of upcoming calls',
    example: 3,
  })
  upcoming_count: number;

  @ApiProperty({
    description: 'Number of overdue calls',
    example: 2,
  })
  overdue_count: number;
}
