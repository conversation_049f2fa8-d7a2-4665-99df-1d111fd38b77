import { ApiProperty } from '@nestjs/swagger';
import { HitlistRecordDto } from './hitlist-record.dto';

export class UploadResponseDto {
  @ApiProperty({
    description: 'The type of hitlist that was uploaded',
    example: '2by2by2',
  })
  type: string;

  @ApiProperty({
    description: 'The ID of the created hitlist',
    example: 'uuid-string',
  })
  hitlistId: string;

  @ApiProperty({
    description: 'The unique code of the hitlist',
    example: 'HIT-JULY-001',
  })
  hitlistCode: string;

  @ApiProperty({
    description: 'The name of the user who uploaded the file',
    example: '<PERSON>',
  })
  uploadedBy: string;

  @ApiProperty({
    description: 'The date when the file was uploaded',
    example: '2025-08-05T10:30:00.000Z',
  })
  uploadDate: Date;

  @ApiProperty({
    description: 'The number of records processed from the uploaded file',
    example: 25,
  })
  numberOfRecords: number;
} 