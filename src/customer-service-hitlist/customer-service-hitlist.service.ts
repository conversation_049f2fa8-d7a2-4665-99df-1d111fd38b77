import { Injectable, BadRequestException } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { HitlistRecordDto } from './dto/hitlist-record.dto';
import { PrismaService } from '../prisma/prisma.service';
import { HitlistRecordListResponseDto } from './dto/hitlist-record-list-response.dto';
import { TargetUpdateService } from '../targets/services/target-update.service';
import { CallsToDoCountDto } from './dto/calls-to-do-count.dto';

@Injectable()
export class CustomerServiceHitlistService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly targetUpdateService: TargetUpdateService,
  ) {}

  /**
   * Get all hitlist records with assigned agents
   */
  async getAllHitlistRecords(): Promise<HitlistRecordListResponseDto[]> {
    const records = await this.prisma.customerServiceHitlistRecord.findMany({
      include: {
        customer_service_hitlist: true,
        two_by_two_phases: {
          include: {
            assigned_to: {
              select: { name: true },
            },
            activities: true,
          },
        },
        activities: true,
      },
    });

    const userMap = new Map<string, string>();
    // Get all unique rm_codes for Dormancy records
    const rmCodes = records
      .filter((record) => record.customer_service_hitlist.type === 'Dormancy')
      .map((record) => record.rm_code);
    if (rmCodes.length > 0) {
      const users = await this.prisma.user.findMany({
        where: { rm_code: { in: rmCodes } },
        select: { rm_code: true, name: true },
      });
      users.forEach((user) => userMap.set(user.rm_code, user.name));
    }

    return records.map((record) => {
      let assigned_agents: string[] = [];
      const parent_hitlist_type = record.customer_service_hitlist.type;
      let call_status: string | undefined = undefined;
      let phase_status: { phase: string; value: string }[] | undefined =
        undefined;

      if (parent_hitlist_type === 'Dormancy') {
        const userName = userMap.get(record.rm_code);
        if (userName) {
          assigned_agents = [userName];
        }
        // Call status: Completed if any call activity exists, else Not started
        const hasCall =
          record.activities &&
          record.activities.some(
            (a) => a.activity_type?.toLowerCase() === 'call',
          );
        call_status = hasCall ? 'Completed' : 'Not started';
      } else if (parent_hitlist_type === '2by2by2') {
        assigned_agents = record.two_by_two_phases
          .map((phase) => phase.assigned_to.name)
          .filter(Boolean);
        // For each phase, check if any call activity exists for that phase
        phase_status = record.two_by_two_phases.map((phase) => {
          const hasCall =
            phase.activities &&
            phase.activities.some(
              (a) => a.activity_type?.toLowerCase() === 'call',
            );
          return {
            phase: phase.type,
            value: hasCall ? 'Completed' : 'Not started',
          };
        });
      }

      return {
        id: record.id,
        parent_hitlist_code: record.customer_service_hitlist.code,
        parent_hitlist_type,
        account_number: record.account_number,
        customer_name: record.customer_name,
        phone_number: record.phone_number,
        assigned_agents,
        ...(call_status ? { call_status } : {}),
        ...(phase_status ? { phase_status } : {}),
      };
    });
  }

  /**
   * Generates an Excel template for customer service hitlist
   * @returns Buffer containing the Excel file
   */
  async generateTemplate(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Customer Service Hitlist');

    // Define the headers
    const headers = [
      'Customer Name',
      'Account Number',
      'Phone Number',
      'RM Code 1',
      'RM Code 2',
    ];

    // Add headers to the worksheet
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // Set column widths
    worksheet.getColumn(1).width = 25; // Customer Name
    worksheet.getColumn(2).width = 15; // Account Number
    worksheet.getColumn(3).width = 15; // Phone Number
    worksheet.getColumn(4).width = 12; // RM Code 1
    worksheet.getColumn(5).width = 12; // RM Code 2

    // Add some sample data
    worksheet.addRow([
      'Sample Customer',
      '**********',
      '+**********',
      'RM001',
      'RM002',
    ]);

    // Generate the buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Processes uploaded Excel file and creates database records
   * @param fileBuffer - The uploaded file buffer
   * @param type - The type of hitlist
   * @param uploadedByUserId - The ID of the user uploading the file
   * @returns Object containing hitlist information and record count
   */
  async processUploadedFile(
    fileBuffer: Buffer,
    type: string,
    uploadedByUserId: string,
  ): Promise<{
    type: string;
    hitlistId: string;
    hitlistCode: string;
    uploadedBy: string;
    uploadDate: Date;
    numberOfRecords: number;
  }> {
    // Validate type
    if (!['Dormancy', '2by2by2'].includes(type)) {
      throw new BadRequestException(
        'Type must be either "Dormancy" or "2by2by2"',
      );
    }

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileBuffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error('No worksheet found in the uploaded file');
    }

    const records: HitlistRecordDto[] = [];

    // Skip the header row and process data rows
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        // Skip header row
        const values = row.values as unknown[];

        // Extract values (ExcelJS uses 1-based indexing, so we need to adjust)
        const record: HitlistRecordDto = {
          customerName: this.safeStringConversion(values[1]),
          accountNumber: this.safeStringConversion(values[2]),
          phoneNumber: this.safeStringConversion(values[3]),
          rmCode1: this.safeStringConversion(values[4]),
          rmCode2: this.safeStringConversion(values[5]),
        };

        // Only add records that have at least a customer name
        if (record.customerName.trim()) {
          records.push(record);
        }
      }
    });

    // Generate hitlist code
    const hitlistCode = await this.generateHitlistCode();

    // Get user information
    const user = await this.prisma.user.findUnique({
      where: { id: uploadedByUserId },
      select: { name: true },
    });

    const uploadDate = new Date();

    // Create CustomerServiceHitlist record
    const hitlist = await this.prisma.customerServiceHitlist.create({
      data: {
        code: hitlistCode,
        type: type,
        uploaded_by: uploadedByUserId,
        uploaded_date: uploadDate,
      },
    });

    // Create CustomerServiceHitlistRecord records
    const hitlistRecords = await Promise.all(
      records.map(async (record) => {
        return this.prisma.customerServiceHitlistRecord.create({
          data: {
            customer_service_hitlist_id: hitlist.id,
            customer_name: record.customerName,
            account_number: record.accountNumber,
            phone_number: record.phoneNumber,
            rm_code: record.rmCode1,
            branch_code: '', // You might want to get this from user's branch
          },
        });
      }),
    );

    // If type is 2by2by2, create TwoByTwoPhase records
    if (type === '2by2by2') {
      await this.createTwoByTwoPhaseRecords(hitlistRecords, uploadedByUserId);
    }

    return {
      type,
      hitlistId: hitlist.id,
      hitlistCode: hitlist.code,
      uploadedBy: user?.name || 'Unknown User',
      uploadDate: hitlist.uploaded_date,
      numberOfRecords: records.length,
    };
  }

  /**
   * Safely converts a value to string, handling null, undefined, and objects
   * @param value - The value to convert
   * @returns String representation of the value
   */
  private safeStringConversion(value: unknown): string {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    }
    return '';
  }

  /**
   * Generates a unique hitlist code with format HIT-MONTH-XXX
   * @returns Unique hitlist code
   */
  private async generateHitlistCode(): Promise<string> {
    const currentDate = new Date();
    const month = currentDate
      .toLocaleString('en-US', { month: 'short' })
      .toUpperCase();
    const year = currentDate.getFullYear();

    // Get count of hitlists for this month
    const startOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1,
    );
    const endOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      0,
    );

    const hitlistsThisMonth = await this.prisma.customerServiceHitlist.count({
      where: {
        uploaded_date: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    const sequenceNumber = (hitlistsThisMonth + 1).toString().padStart(3, '0');
    return `HIT-${month}-${sequenceNumber}`;
  }

  /**
   * Creates TwoByTwoPhase records for 2by2by2 type hitlists
   * @param hitlistRecords - The hitlist records to create phases for
   * @param loggedInUserId - The ID of the logged in user
   */
  private async createTwoByTwoPhaseRecords(
    hitlistRecords: any[],
    loggedInUserId: string,
  ): Promise<void> {
    const importDate = new Date();

    for (const record of hitlistRecords) {
      // Get user for rmCode1 (first2 and third2)
      const user1 = await this.prisma.user.findFirst({
        where: { rm_code: record.rm_code },
      });

      // Get user for rmCode2 (second2) - fallback to logged in user if not found
      let user2 = await this.prisma.user.findUnique({
        where: { id: loggedInUserId },
      });
      if (record.rm_code) {
        const userByRmCode = await this.prisma.user.findFirst({
          where: { rm_code: record.rm_code },
        });
        if (userByRmCode) {
          user2 = userByRmCode;
        }
      }

      // Create first2 phase
      await this.prisma.twoByTwoPhase.create({
        data: {
          type: 'first2',
          customer_service_hitlist_record_id: record.id,
          assigned_to_user_id: user1?.id || loggedInUserId,
          status: 'not_started',
          expected_completion_date: new Date(
            importDate.getTime() + 2 * 24 * 60 * 60 * 1000,
          ), // 2 days
          is_current: true,
          is_completed: false,
        },
      });

      // Create second2 phase
      await this.prisma.twoByTwoPhase.create({
        data: {
          type: 'second2',
          customer_service_hitlist_record_id: record.id,
          assigned_to_user_id: user2?.id || loggedInUserId,
          status: 'not_started',
          expected_completion_date: new Date(
            importDate.getTime() + 14 * 24 * 60 * 60 * 1000,
          ), // 2 weeks
          is_current: false,
          is_completed: false,
        },
      });

      // Create third2 phase
      await this.prisma.twoByTwoPhase.create({
        data: {
          type: 'third2',
          customer_service_hitlist_record_id: record.id,
          assigned_to_user_id: user1?.id || loggedInUserId,
          status: 'not_started',
          expected_completion_date: new Date(
            importDate.getTime() + 60 * 24 * 60 * 60 * 1000,
          ), // 2 months
          is_current: false,
          is_completed: false,
        },
      });
    }
  }

  /**
   * Get hitlist summary and records by hitlist code
   */
  async getHitlistByCode(hitlistCode: string) {
    const hitlist = await this.prisma.customerServiceHitlist.findFirst({
      where: { code: hitlistCode },
      include: {
        uploader: { select: { name: true } },
        records: {
          include: {
            activities: true,
            two_by_two_phases: {
              include: {
                assigned_to: { select: { name: true, rm_code: true } },
              },
            },
          },
        },
      },
    });
    if (!hitlist) {
      throw new BadRequestException('Hitlist not found');
    }
    const response: any = {
      id: hitlist.id,
      hitlistCode: hitlist.code,
      uploadedBy: hitlist.uploader?.name || 'Unknown',
      uploadDate: hitlist.uploaded_date,
      type: hitlist.type,
      numberOfRecords: hitlist.records.length,
      records: [],
    };
    if (hitlist.type === 'Dormancy') {
      // Get all unique rm_codes from records to fetch user information
      const rmCodes = [
        ...new Set(hitlist.records.map((rec) => rec.rm_code).filter(Boolean)),
      ];
      const users = await this.prisma.user.findMany({
        where: { rm_code: { in: rmCodes } },
        select: { rm_code: true, name: true },
      });

      // Create a map for quick lookup
      const userMap = new Map(users.map((user) => [user.rm_code, user.name]));

      response.records = hitlist.records.map((rec) => {
        // Find latest call activity
        const callActivities = rec.activities.filter(
          (a) => a.activity_type?.toLowerCase() === 'call',
        );
        let callStatus = 'Not Started';
        let callDate: Date | null = null;
        if (callActivities.length > 0) {
          callStatus = 'Completed';
          callDate = callActivities.sort(
            (a, b) => (b.created_at as any) - (a.created_at as any),
          )[0].created_at;
        }
        return {
          id: rec.id, // Add hitlist record id here
          customerName: rec.customer_name,
          accountNumber: rec.account_number,
          phoneNumber: rec.phone_number,
          assignedAgentName: rec.rm_code
            ? userMap.get(rec.rm_code) || 'Unknown Agent'
            : '',
          assignedAgentRMCode: rec.rm_code || '',
          callStatus,
          callDate: callDate ? new Date(callDate) : null,
        };
      });
    } else if (hitlist.type === '2by2by2') {
      // ... existing 2by2by2 code remains unchanged ...
      response.records = await Promise.all(
        hitlist.records.map(async (rec) => {
          // Get all phases for this record
          const phases = rec.two_by_two_phases;
          // Get assigned agent names and rm codes for first2 and second2
          const first2 = phases.find((p) => p.type === 'first2');
          const second2 = phases.find((p) => p.type === 'second2');
          const third2 = phases.find((p) => p.type === 'third2');
          const assignedAgentNames = [
            first2?.assigned_to?.name && first2?.assigned_to?.rm_code
              ? {
                  name: first2.assigned_to.name,
                  rmCode: first2.assigned_to.rm_code,
                }
              : null,
            second2?.assigned_to?.name && second2?.assigned_to?.rm_code
              ? {
                  name: second2.assigned_to.name,
                  rmCode: second2.assigned_to.rm_code,
                }
              : null,
          ].filter(Boolean);
          return {
            id: rec.id, // Add hitlist record id here
            customerName: rec.customer_name,
            accountNumber: rec.account_number,
            phoneNumber: rec.phone_number,
            assignedAgentNames,
            first2: first2
              ? {
                  is_completed: first2.is_completed,
                  expected_completion_date: first2.expected_completion_date,
                  actual_completion_date: first2.execution_date,
                }
              : null,
            second2: second2
              ? {
                  is_completed: second2.is_completed,
                  expected_completion_date: second2.expected_completion_date,
                  actual_completion_date: second2.execution_date,
                }
              : null,
            third2: third2
              ? {
                  is_completed: third2.is_completed,
                  expected_completion_date: third2.expected_completion_date,
                  actual_completion_date: third2.execution_date,
                }
              : null,
          };
        }),
      );
    }
    return response;
  }

  /**
   * Get calls to do for the logged-in user with filtering support
   */
  async getCallsToDo(userId: string, type: string = 'pending') {
    // Validate type parameter
    const validTypes = ['pending', 'completed', 'upcoming', 'overdue'];
    if (!validTypes.includes(type)) {
      throw new BadRequestException(
        `Invalid type. Must be one of: ${validTypes.join(', ')}`,
      );
    }

    // Get the user's rm_code
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) throw new BadRequestException('User not found');
    const rmCode = user.rm_code;

    // Get all hitlists and their records, activities, and phases
    const hitlists = await this.prisma.customerServiceHitlist.findMany({
      include: {
        records: {
          include: {
            activities: true,
            two_by_two_phases: {
              include: { assigned_to: true },
            },
            customer_service_hitlist: {
              select: { type: true, uploaded_date: true },
            },
          },
        },
      },
    });

    let results: any[] = [];

    switch (type) {
      case 'pending':
        results = await this.getPendingTasks(hitlists, userId, rmCode);
        break;
      case 'completed':
        results = await this.getCompletedTasks(hitlists, userId, rmCode);
        break;
      case 'upcoming':
        results = await this.getUpcomingTasks(hitlists, userId);
        break;
      case 'overdue':
        results = await this.getOverdueTasks(hitlists, userId);
        break;
    }

    return results;
  }

  /**
   * Get counts of calls to do for the logged-in user by status
   */
  async getCallsToDoCount(userId: string): Promise<CallsToDoCountDto> {
    // Get the user's rm_code
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) throw new BadRequestException('User not found');
    const rmCode = user.rm_code;

    // Get all hitlists and their records, activities, and phases
    const hitlists = await this.prisma.customerServiceHitlist.findMany({
      include: {
        records: {
          include: {
            activities: true,
            two_by_two_phases: {
              include: { assigned_to: true },
            },
            customer_service_hitlist: {
              select: { type: true, uploaded_date: true },
            },
          },
        },
      },
    });

    // Get counts for each type
    const [pending, completed, upcoming, overdue] = await Promise.all([
      this.getPendingTasks(hitlists, userId, rmCode),
      this.getCompletedTasks(hitlists, userId, rmCode),
      this.getUpcomingTasks(hitlists, userId),
      this.getOverdueTasks(hitlists, userId),
    ]);

    return {
      pending_count: pending.length,
      completed_count: completed.length,
      upcoming_count: upcoming.length,
      overdue_count: overdue.length,
    };
  }

  /**
   * Helper method to calculate days and hours difference
   */
  private calculateTimeDifference(
    date1: Date,
    date2: Date,
  ): { days: number; hours: number } {
    const diffMs = Math.abs(date2.getTime() - date1.getTime());
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
    );
    return { days, hours };
  }

  /**
   * Helper method to check if a date is today
   */
  private isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  /**
   * Helper method to check if a date is before today
   */
  private isOverdue(date: Date): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const checkDate = new Date(date);
    checkDate.setHours(0, 0, 0, 0);
    return checkDate < today;
  }

  /**
   * Get pending tasks for the user
   */
  private async getPendingTasks(
    hitlists: any[],
    userId: string,
    rmCode: string,
  ): Promise<any[]> {
    const results: any[] = [];
    const today = new Date();

    // First, collect all 2by2by2 tasks for today (high priority)
    const todayTasks: any[] = [];
    // Then, collect overdue 2by2by2 tasks
    const overdueTasks: any[] = [];
    // Finally, collect dormancy tasks
    const dormancyTasks: any[] = [];

    for (const hitlist of hitlists) {
      for (const rec of hitlist.records) {
        const hitlistType = rec.customer_service_hitlist.type;

        // Handle 2by2by2 tasks
        if (hitlistType === '2by2by2' && rec.two_by_two_phases?.length > 0) {
          for (const phase of rec.two_by_two_phases) {
            if (phase.assigned_to_user_id === userId && !phase.is_completed) {
              const expectedDate = new Date(phase.expected_completion_date);
              const timeDiff = this.calculateTimeDifference(
                today,
                expectedDate,
              );

              const baseTask = {
                id: rec.id,
                customerName: rec.customer_name,
                phoneNumber: rec.phone_number,
                accountNumber: rec.account_number,
                hitlistType,
                phase: phase.type,
                scheduled: phase.expected_completion_date,
                status: { value: 'pending', created_at: null },
                completed_at: null,
                upcoming_in: null,
              };

              if (this.isToday(expectedDate)) {
                todayTasks.push({
                  ...baseTask,
                  priority: 'High',
                  overdue_by: null,
                });
              } else if (this.isOverdue(expectedDate)) {
                overdueTasks.push({
                  ...baseTask,
                  priority: 'Overdue',
                  overdue_by: `${timeDiff.days} days, ${timeDiff.hours} hours`,
                });
              }
            }
          }
        }

        // Handle Dormancy tasks
        if (hitlistType === 'Dormancy' && rec.rm_code === rmCode) {
          const userCalls = rec.activities.filter(
            (a) =>
              a.activity_type?.toLowerCase() === 'call' &&
              a.performed_by_user_id === userId,
          );

          if (userCalls.length === 0) {
            // Only pending tasks
            const uploadDate = new Date(
              rec.customer_service_hitlist.uploaded_date,
            );
            const daysDiff = this.calculateTimeDifference(
              today,
              uploadDate,
            ).days;

            let priority = 'Low';
            if (daysDiff >= 3) {
              priority = 'High';
            } else if (daysDiff >= 1) {
              priority = 'Medium';
            }

            dormancyTasks.push({
              id: rec.id,
              customerName: rec.customer_name,
              phoneNumber: rec.phone_number,
              accountNumber: rec.account_number,
              hitlistType,
              phase: null,
              scheduled: null,
              status: { value: 'pending', created_at: null },
              priority,
              overdue_by: null,
              completed_at: null,
              upcoming_in: null,
            });
          }
        }
      }
    }

    // Sort dormancy tasks by upload date (ascending)
    // Note: uploaded_date is already available in each task's customer_service_hitlist object
    dormancyTasks.sort((a, b) => {
      // Find the records to get their upload dates
      let uploadDateA: Date | null = null;
      let uploadDateB: Date | null = null;

      for (const hitlist of hitlists) {
        for (const rec of hitlist.records) {
          if (rec.id === a.id) {
            uploadDateA = new Date(rec.customer_service_hitlist.uploaded_date);
          }
          if (rec.id === b.id) {
            uploadDateB = new Date(rec.customer_service_hitlist.uploaded_date);
          }
        }
      }

      if (!uploadDateA || !uploadDateB) return 0;
      return uploadDateA.getTime() - uploadDateB.getTime();
    });

    // Combine results: today tasks first, then overdue, then dormancy
    results.push(...todayTasks, ...overdueTasks, ...dormancyTasks);
    return results;
  }

  /**
   * Get completed tasks for the user
   */
  private async getCompletedTasks(
    hitlists: any[],
    userId: string,
    rmCode: string,
  ): Promise<any[]> {
    const results: any[] = [];

    for (const hitlist of hitlists) {
      for (const rec of hitlist.records) {
        const hitlistType = rec.customer_service_hitlist.type;

        // Handle 2by2by2 completed tasks
        if (hitlistType === '2by2by2' && rec.two_by_two_phases?.length > 0) {
          for (const phase of rec.two_by_two_phases) {
            if (phase.assigned_to_user_id === userId && phase.is_completed) {
              // Find latest call for this phase
              const phaseCalls = rec.activities.filter(
                (a) =>
                  a.activity_type?.toLowerCase() === 'call' &&
                  a.two_by_two_phase_id === phase.id,
              );
              const latestCall =
                phaseCalls.length > 0
                  ? phaseCalls.sort(
                      (a, b) =>
                        new Date(b.created_at).getTime() -
                        new Date(a.created_at).getTime(),
                    )[0]
                  : null;

              results.push({
                id: rec.id,
                customerName: rec.customer_name,
                phoneNumber: rec.phone_number,
                accountNumber: rec.account_number,
                hitlistType,
                phase: phase.type,
                scheduled: phase.expected_completion_date,
                status: {
                  value: 'completed',
                  created_at: phase.execution_date,
                },
                priority: 'Low', // Default for completed
                overdue_by: null,
                completed_at: latestCall
                  ? latestCall.created_at
                  : phase.execution_date,
                upcoming_in: null,
              });
            }
          }
        }

        // Handle Dormancy completed tasks
        if (hitlistType === 'Dormancy' && rec.rm_code === rmCode) {
          const userCalls = rec.activities.filter(
            (a) =>
              a.activity_type?.toLowerCase() === 'call' &&
              a.performed_by_user_id === userId,
          );

          if (userCalls.length > 0) {
            const latestCall = userCalls.sort(
              (a, b) =>
                new Date(b.created_at).getTime() -
                new Date(a.created_at).getTime(),
            )[0];

            results.push({
              id: rec.id,
              customerName: rec.customer_name,
              phoneNumber: rec.phone_number,
              accountNumber: rec.account_number,
              hitlistType,
              phase: null,
              scheduled: null,
              status: { value: 'completed', created_at: latestCall.created_at },
              priority: 'Low', // Default for completed
              overdue_by: null,
              completed_at: latestCall.created_at,
              upcoming_in: null,
            });
          }
        }
      }
    }

    return results;
  }

  /**
   * Get upcoming tasks for the user (2by2by2 only)
   */
  private async getUpcomingTasks(
    hitlists: any[],
    userId: string,
  ): Promise<any[]> {
    const results: any[] = [];
    const today = new Date();

    for (const hitlist of hitlists) {
      for (const rec of hitlist.records) {
        const hitlistType = rec.customer_service_hitlist.type;

        // Only 2by2by2 tasks have upcoming deadlines
        if (hitlistType === '2by2by2' && rec.two_by_two_phases?.length > 0) {
          for (const phase of rec.two_by_two_phases) {
            if (phase.assigned_to_user_id === userId && !phase.is_completed) {
              const expectedDate = new Date(phase.expected_completion_date);

              // Only include future dates (not today or overdue)
              if (expectedDate > today && !this.isToday(expectedDate)) {
                const timeDiff = this.calculateTimeDifference(
                  today,
                  expectedDate,
                );

                results.push({
                  id: rec.id,
                  customerName: rec.customer_name,
                  phoneNumber: rec.phone_number,
                  accountNumber: rec.account_number,
                  hitlistType,
                  phase: phase.type,
                  scheduled: phase.expected_completion_date,
                  status: { value: 'pending', created_at: null },
                  priority: 'Low',
                  overdue_by: null,
                  completed_at: null,
                  upcoming_in: `${timeDiff.days} days, ${timeDiff.hours} hours`,
                });
              }
            }
          }
        }
      }
    }

    // Sort by expected completion date (ascending)
    results.sort(
      (a, b) =>
        new Date(a.scheduled).getTime() - new Date(b.scheduled).getTime(),
    );
    return results;
  }

  /**
   * Get overdue tasks for the user (2by2by2 only)
   */
  private async getOverdueTasks(
    hitlists: any[],
    userId: string,
  ): Promise<any[]> {
    const results: any[] = [];
    const today = new Date();

    for (const hitlist of hitlists) {
      for (const rec of hitlist.records) {
        const hitlistType = rec.customer_service_hitlist.type;

        // Only 2by2by2 tasks can be overdue
        if (hitlistType === '2by2by2' && rec.two_by_two_phases?.length > 0) {
          for (const phase of rec.two_by_two_phases) {
            if (phase.assigned_to_user_id === userId && !phase.is_completed) {
              const expectedDate = new Date(phase.expected_completion_date);

              // Only include overdue dates (not today)
              if (this.isOverdue(expectedDate) && !this.isToday(expectedDate)) {
                const timeDiff = this.calculateTimeDifference(
                  today,
                  expectedDate,
                );

                results.push({
                  id: rec.id,
                  customerName: rec.customer_name,
                  phoneNumber: rec.phone_number,
                  accountNumber: rec.account_number,
                  hitlistType,
                  phase: phase.type,
                  scheduled: phase.expected_completion_date,
                  status: { value: 'pending', created_at: null },
                  priority: 'Overdue',
                  overdue_by: `${timeDiff.days} days, ${timeDiff.hours} hours`,
                  completed_at: null,
                  upcoming_in: `-${timeDiff.days} days, -${timeDiff.hours} hours`,
                });
              }
            }
          }
        }
      }
    }

    // Sort by expected completion date (ascending - most overdue first)
    results.sort(
      (a, b) =>
        new Date(a.scheduled).getTime() - new Date(b.scheduled).getTime(),
    );
    return results;
  }

  /**
   * Get all hitlists with summary information
   */
  async getAllHitlists() {
    const hitlists = await this.prisma.customerServiceHitlist.findMany({
      include: {
        uploader: { select: { name: true } },
        records: {
          include: {
            activities: true,
            two_by_two_phases: true,
          },
        },
      },
      orderBy: {
        uploaded_date: 'desc',
      },
    });

    return hitlists.map((hitlist) => {
      const response: any = {
        type: hitlist.type,
        hitlistId: hitlist.id,
        hitlistCode: hitlist.code,
        uploadedBy: hitlist.uploader?.name || 'Unknown',
        uploadDate: hitlist.uploaded_date,
        numberOfRecords: hitlist.records.length,
        completion: '',
      };

      // Calculate completion percentage
      if (hitlist.type === 'Dormancy') {
        const recordsWithActivities = hitlist.records.filter(
          (record) => record.activities.length > 0,
        ).length;
        response.completion = `${Math.round(
          (recordsWithActivities / hitlist.records.length) * 100,
        )}%`;
      } else if (hitlist.type === '2by2by2') {
        const totalPhases = hitlist.records.length * 3; // 3 phases per record
        const completedPhases = hitlist.records.reduce(
          (acc, record) =>
            acc +
            record.two_by_two_phases.filter((phase) => phase.is_completed)
              .length,
          0,
        );
        response.completion = `${Math.round(
          (completedPhases / totalPhases) * 100,
        )}%`;
      }

      return response;
    });
  }

  /**
   * Get all customer feedback categories
   */
  async getCustomerFeedbackCategories() {
    return this.prisma.customerFeedbackCategory.findMany({
      orderBy: {
        name: 'asc',
      },
    });
  }

  /**
   * Make a call for a hitlist record
   */
  async makeCall(
    hitlistRecordId: string,
    callData: {
      call_status: string;
      notes?: string;
      phase?: string;
      customer_feedback_id: string;
    },
    userId: string,
  ) {
    // Get the hitlist record with its hitlist and phases
    const hitlistRecord =
      await this.prisma.customerServiceHitlistRecord.findUnique({
        where: { id: hitlistRecordId },
        include: {
          customer_service_hitlist: true,
          two_by_two_phases: true,
        },
      });

    if (!hitlistRecord) {
      throw new BadRequestException('Hitlist record not found');
    }

    const hitlistType = hitlistRecord.customer_service_hitlist.type;
    let twoByTwoPhaseId: string | undefined;

    // For 2by2by2 hitlist type, get the phase ID
    if (hitlistType === '2by2by2') {
      if (!callData.phase) {
        throw new BadRequestException(
          'Phase is required for 2by2by2 hitlist type',
        );
      }

      const phase = hitlistRecord.two_by_two_phases.find(
        (p) => p.type === callData.phase,
      );

      if (!phase) {
        throw new BadRequestException(
          'Phase not found for this hitlist record',
        );
      }

      twoByTwoPhaseId = phase.id;

      // Update the phase as completed
      await this.prisma.twoByTwoPhase.update({
        where: { id: phase.id },
        data: {
          is_completed: true,
          execution_date: new Date(),
        },
      });
    }

    // Create the activity
    const activity = await this.prisma.activity.create({
      data: {
        activity_type: 'call',
        interaction_type: 'call',
        call_status: callData.call_status,
        notes: callData.notes,
        performed_by_user_id: userId,
        customer_service_hitlist_record_id: hitlistRecordId,
        two_by_two_phase_id: twoByTwoPhaseId,
        customer_feedback_id: callData.customer_feedback_id,
      },
    });

    // Update user targets based on the hitlist type
    let targetActivity: string;

    if (hitlistType === '2by2by2') {
      targetActivity = 'TWO_BY_TWO_BY_TWO_HITLIST';
    } else if (hitlistType === 'Dormancy') {
      targetActivity = 'DORMANCY_HITLIST';
    } else {
      // Log unknown hitlist type but don't fail
      console.log(`Unknown hitlist type: ${hitlistType}`);
      targetActivity = 'DORMANCY_HITLIST'; // Default fallback
    }

    console.log(
      `Updating targets for user ${userId} with activity ${targetActivity} and interaction_type call`,
    );

    // Call the target update function
    await this.targetUpdateService.updateUserTargets(
      targetActivity,
      'call',
      userId,
    );

    return {
      success: true,
      activity_id: activity.id,
      message: 'Call recorded successfully',
    };
  }

  /**
   * Get call history for a customer service hitlist record
   */
  async getHitlistRecordCallHistory(hitlistRecordId: string) {
    const activities = await this.prisma.activity.findMany({
      where: {
        customer_service_hitlist_record_id: hitlistRecordId,
        interaction_type: 'call',
      },
      include: {
        performed_by: {
          select: {
            name: true,
            rm_code: true,
          },
        },
        customer_feedback: {
          select: {
            name: true,
          },
        },
      },
      orderBy: { created_at: 'desc' },
    });

    return activities.map((activity) => ({
      interaction_type: activity.interaction_type,
      performed_by: {
        name: activity.performed_by?.name,
        rm_code: activity.performed_by?.rm_code,
      },
      performed_at: activity.created_at,
      notes: activity.notes,
      customer_feedback: activity.customer_feedback?.name,
      call_status: activity.call_status,
    }));
  }

  /**
   * Update a customer service hitlist record
   */
  async updateHitlistRecord(id: string, body: any) {
    const updateData: any = {};
    if (body.customerName !== undefined)
      updateData.customer_name = body.customerName;
    if (body.phoneNumber !== undefined)
      updateData.phone_number = body.phoneNumber;
    if (body.accountNumber !== undefined)
      updateData.account_number = body.accountNumber;
    const updated = await this.prisma.customerServiceHitlistRecord.update({
      where: { id },
      data: updateData,
    });
    return {
      id: updated.id,
      customerName: updated.customer_name,
      phoneNumber: updated.phone_number,
      accountNumber: updated.account_number,
      rmCode: updated.rm_code,
    };
  }

  /**
   * Delete a customer service hitlist record and its calls
   */
  async deleteHitlistRecord(id: string) {
    // Delete all activities (calls) associated with this record
    await this.prisma.activity.deleteMany({
      where: { customer_service_hitlist_record_id: id },
    });
    // Delete the hitlist record itself
    await this.prisma.customerServiceHitlistRecord.delete({ where: { id } });
    return { success: true };
  }

  /**
   * Reassign users/phases for a hitlist record
   */
  async reassignHitlistRecord(id: string, body: any) {
    const record = await this.prisma.customerServiceHitlistRecord.findUnique({
      where: { id },
      include: {
        customer_service_hitlist: true,
        two_by_two_phases: true,
      },
    });
    if (!record) throw new Error('Hitlist record not found');
    const hitlistType = record.customer_service_hitlist.type;
    if (hitlistType === 'Dormancy') {
      if (body.newUser) {
        const user = await this.prisma.user.findUnique({
          where: { id: body.newUser },
        });
        if (!user) throw new Error('User not found');
        await this.prisma.customerServiceHitlistRecord.update({
          where: { id },
          data: { rm_code: user.rm_code },
        });
      }
    } else if (hitlistType === '2by2by2') {
      const updates: Promise<any>[] = [];
      let newRmCode: string | undefined;
      if (body.newFirst2) {
        updates.push(
          this.prisma.twoByTwoPhase.updateMany({
            where: { customer_service_hitlist_record_id: id, type: 'first2' },
            data: { assigned_to_user_id: body.newFirst2 },
          }),
        );
        const user = await this.prisma.user.findUnique({
          where: { id: body.newFirst2 },
        });
        if (user) newRmCode = user.rm_code;
      }
      if (body.newSecond2) {
        updates.push(
          this.prisma.twoByTwoPhase.updateMany({
            where: { customer_service_hitlist_record_id: id, type: 'second2' },
            data: { assigned_to_user_id: body.newSecond2 },
          }),
        );
      }
      if (body.newThird2) {
        updates.push(
          this.prisma.twoByTwoPhase.updateMany({
            where: { customer_service_hitlist_record_id: id, type: 'third2' },
            data: { assigned_to_user_id: body.newThird2 },
          }),
        );
      }
      await Promise.all(updates);
      if (newRmCode) {
        await this.prisma.customerServiceHitlistRecord.update({
          where: { id },
          data: { rm_code: newRmCode },
        });
      }
    }
    // Return updated record
    const updated = await this.prisma.customerServiceHitlistRecord.findUnique({
      where: { id },
    });
    if (!updated) {
      throw new Error('Failed to retrieve updated hitlist record');
    }
    return {
      id: updated.id,
      customerName: updated.customer_name,
      phoneNumber: updated.phone_number,
      accountNumber: updated.account_number,
      rmCode: updated.rm_code,
    };
  }
}
