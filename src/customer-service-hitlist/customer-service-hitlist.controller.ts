import {
  Controller,
  Get,
  Post,
  Res,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  Body,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
  BadRequestException,
  UseGuards,
  Request,
  Query,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CustomerServiceHitlistService } from './customer-service-hitlist.service';
import { CallsToDoCountDto } from './dto/calls-to-do-count.dto';
import { UploadHitlistDto } from './dto/upload-hitlist.dto';
import { UploadResponseDto } from './dto/upload-response.dto';
import { MakeCallDto } from './dto/make-call.dto';
import { HitlistRecordListResponseDto } from './dto/hitlist-record-list-response.dto';

@ApiTags('Customer Service Hitlist')
@Controller('customer-service')
export class CustomerServiceHitlistController {
  constructor(
    private readonly customerServiceHitlistService: CustomerServiceHitlistService,
  ) {}

  /**
   * Generates and downloads an Excel template for customer service hitlist
   * GET /customer-service/hitlist-template
   */
  @Get('hitlist-template')
  @ApiOperation({
    summary: 'Download customer service hitlist template',
    description:
      'Generates and returns an Excel template with the required fields: Customer Name, Account Number, Phone Number, RM Code 1, RM Code 2',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Excel template generated successfully',
  })
  async downloadTemplate(@Res() res: Response): Promise<void> {
    const buffer = await this.customerServiceHitlistService.generateTemplate();

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition':
        'attachment; filename="customer-service-hitlist-template.xlsx"',
      'Content-Length': buffer.length,
    });

    res.send(buffer);
  }

  /**
   * Uploads and processes a customer service hitlist file
   * POST /customer-service/hitlist-template
   */
  @Post('hitlist-template')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Upload customer service hitlist file',
    description:
      'Uploads an Excel file containing customer service hitlist data and returns the extracted records along with the type. Requires authentication.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload hitlist file with type',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel file to upload',
        },
        type: {
          type: 'string',
          description: 'Type of hitlist',
          example: 'customer_service',
        },
      },
      required: ['file', 'type'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'File processed successfully',
    type: UploadResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing required fields',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
  })
  async uploadTemplate(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB
        ],
      }),
    )
    file: Express.Multer.File,
    @Body() uploadDto: UploadHitlistDto,
    @Request() req: any,
  ): Promise<UploadResponseDto> {
    // Custom validation for Excel files
    if (
      !file.originalname.endsWith('.xlsx') &&
      !file.originalname.endsWith('.xls')
    ) {
      throw new BadRequestException(
        'File must be an Excel file (.xlsx or .xls)',
      );
    }

    // Check MIME type as additional validation
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/octet-stream', // Some systems send this for Excel files
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Expected Excel file, got: ${file.mimetype}`,
      );
    }

    const result = await this.customerServiceHitlistService.processUploadedFile(
      file.buffer,
      uploadDto.type,
      req.user.id, // Pass the logged in user's ID
    );

    return result;
  }

  /**
   * Get hitlist summary and records by hitlist code
   * GET /customer-service/target/:hitlistCode
   */
  @Get('target/:hitlistCode')
  @ApiOperation({
    summary: 'Get hitlist summary and records by code',
    description:
      'Returns hitlist summary and records for Dormancy or 2by2by2 type',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Hitlist summary and records returned successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Hitlist not found',
  })
  async getHitlistByCode(@Param('hitlistCode') hitlistCode: string) {
    return this.customerServiceHitlistService.getHitlistByCode(hitlistCode);
  }

  /**
   * Get calls to do for the logged-in user
   * GET /customer-service/calls-to-do?type=pending|completed|upcoming|overdue
   */
  @Get('calls-to-do')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get calls to do for the logged-in user',
    description:
      'Returns customer service hitlist records assigned to the logged-in user (Dormancy and 2by2by2). Supports filtering by type: pending (default), completed, upcoming, overdue. Response includes priority, overdue_by, completed_at, and upcoming_in fields.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Calls to do returned successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: '123e4567-e89b-12d3-a456-************',
          },
          customerName: { type: 'string', example: 'John Doe' },
          phoneNumber: { type: 'string', example: '+**********' },
          accountNumber: { type: 'string', example: '**********' },
          hitlistType: {
            type: 'string',
            example: 'Dormancy',
            enum: ['Dormancy', '2by2by2'],
          },
          phase: { type: 'string', example: 'first2', nullable: true },
          scheduled: { type: 'string', format: 'date-time', nullable: true },
          status: {
            type: 'object',
            properties: {
              value: {
                type: 'string',
                example: 'pending',
                enum: ['pending', 'completed'],
              },
              created_at: {
                type: 'string',
                format: 'date-time',
                nullable: true,
              },
            },
          },
          priority: {
            type: 'string',
            example: 'High',
            enum: ['Low', 'Medium', 'High', 'Overdue'],
          },
          overdue_by: {
            type: 'string',
            example: '2 days, 3 hours',
            nullable: true,
          },
          completed_at: { type: 'string', format: 'date-time', nullable: true },
          upcoming_in: {
            type: 'string',
            example: '5 days, 2 hours',
            nullable: true,
          },
        },
      },
    },
  })
  async getCallsToDo(
    @Request() req: any,
    @Query('type') type: string = 'pending',
  ) {
    return this.customerServiceHitlistService.getCallsToDo(req.user.id, type);
  }

  /**
   * Get counts of calls to do for the logged-in user by status
   * GET /customer-service/calls-to-do/count
   */
  @Get('calls-to-do/count')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get counts of calls to do for the logged-in user by status',
    description:
      'Returns counts of customer service hitlist records assigned to the logged-in user by status (pending, completed, upcoming, overdue).',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Call counts returned successfully',
    type: CallsToDoCountDto,
  })
  async getCallsToDoCount(@Request() req: any): Promise<CallsToDoCountDto> {
    return this.customerServiceHitlistService.getCallsToDoCount(req.user.id);
  }

  /**
   * Get all hitlists with summary information
   * GET /hitlists
   */
  @Get('hitlists')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all hitlists',
    description:
      'Returns a list of all hitlists with summary information including completion status.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of hitlists retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          type: { type: 'string', example: 'Dormancy' },
          hitlistId: {
            type: 'string',
            example: '37ea5180-40b7-47bf-8a07-2798608ff122',
          },
          hitlistCode: { type: 'string', example: 'HIT-AUG-003' },
          uploadedBy: { type: 'string', example: 'John Doe' },
          uploadDate: {
            type: 'string',
            format: 'date-time',
            example: '2025-08-05T07:40:30.272Z',
          },
          numberOfRecords: { type: 'number', example: 1 },
          completion: { type: 'string', example: '75%' },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
  })
  async getAllHitlists() {
    return this.customerServiceHitlistService.getAllHitlists();
  }

  /**
   * Get all customer feedback categories
   * GET /customer-feedback-categories
   */
  @Get('customer-feedback-categories')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all customer feedback categories',
    description: 'Returns a list of all customer feedback categories.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer feedback categories retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: '123e4567-e89b-12d3-a456-************',
          },
          name: { type: 'string', example: 'Satisfied' },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
  })
  async getCustomerFeedbackCategories() {
    return this.customerServiceHitlistService.getCustomerFeedbackCategories();
  }

  /**
   * Make a call for a specific hitlist record
   * POST /customer-service/calls-to-do/:id/make-call
   */
  @Post('calls-to-do/:id/make-call')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Make a call for a hitlist record',
    description:
      'Records a call activity for a specific hitlist record. For 2by2by2 type, requires phase information.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Call recorded successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        activity_id: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-************',
        },
        message: { type: 'string', example: 'Call recorded successfully' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or hitlist record not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
  })
  async makeCall(
    @Param('id') hitlistRecordId: string,
    @Body() makeCallDto: MakeCallDto,
    @Request() req: any,
  ) {
    return this.customerServiceHitlistService.makeCall(
      hitlistRecordId,
      makeCallDto,
      req.user.id,
    );
  }

  /**
   * Get call history for a customer service hitlist record
   * GET /customer-service/hitlist-record/:id/call-history
   */
  @Get('hitlist-record/:id/call-history')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get call history for a hitlist record',
    description:
      'Returns all call activities for a given customer service hitlist record.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Call history retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          interaction_type: { type: 'string', example: 'call' },
          performed_by: {
            type: 'object',
            properties: {
              name: { type: 'string', example: 'John Doe' },
              rm_code: { type: 'string', example: 'RM123' },
            },
          },
          performed_at: {
            type: 'string',
            format: 'date-time',
            example: '2025-08-05T07:40:30.272Z',
          },
          notes: {
            type: 'string',
            example: 'Called customer, discussed product.',
          },
          customer_feedback: { type: 'string', example: 'Positive' },
          call_status: { type: 'string', example: 'Completed' },
        },
      },
    },
  })
  async getHitlistRecordCallHistory(@Param('id') id: string) {
    return this.customerServiceHitlistService.getHitlistRecordCallHistory(id);
  }

  /**
   * Update a customer service hitlist record
   * PATCH /customer-service/hitlist-record/:id
   */
  @Patch('hitlist-record/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update a hitlist record',
    description:
      'Updates customerName, phoneNumber, or accountNumber for a hitlist record.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Record updated successfully',
  })
  async updateHitlistRecord(@Param('id') id: string, @Body() body: any) {
    return this.customerServiceHitlistService.updateHitlistRecord(id, body);
  }

  /**
   * Delete a customer service hitlist record and its calls
   * DELETE /customer-service/hitlist-record/:id
   */
  @Delete('hitlist-record/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete a hitlist record',
    description: 'Deletes a hitlist record and all its associated calls.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Record deleted successfully',
  })
  async deleteHitlistRecord(@Param('id') id: string) {
    return this.customerServiceHitlistService.deleteHitlistRecord(id);
  }

  /**
   * Reassign users/phases for a hitlist record
   * PATCH /customer-service/hitlist-record/:id/reassign
   */
  @Patch('hitlist-record/:id/reassign')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Reassign users/phases for a hitlist record',
    description:
      'Reassigns users or phases for Dormancy or 2by2by2 hitlist records.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Record reassigned successfully',
  })
  async reassignHitlistRecord(@Param('id') id: string, @Body() body: any) {
    return this.customerServiceHitlistService.reassignHitlistRecord(id, body);
  }

  /**
   * Get all hitlist records with assigned agents
   * GET /customer-service/hitlist-records
   */
  @Get('hitlist-records')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all hitlist records',
    description:
      'Returns all hitlist records with their assigned agents based on hitlist type',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Records retrieved successfully',
    type: [HitlistRecordListResponseDto],
  })
  async getAllHitlistRecords(): Promise<HitlistRecordListResponseDto[]> {
    return this.customerServiceHitlistService.getAllHitlistRecords();
  }
}
