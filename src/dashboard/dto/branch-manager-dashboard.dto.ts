import { ApiProperty } from '@nestjs/swagger';

export class SummaryCardDto {
  @ApiProperty({
    description: 'Total count for all time',
    example: 150,
  })
  all: number;

  @ApiProperty({
    description: 'Count for this month',
    example: 25,
  })
  this_month: number;
}

export class SummaryCardsDto {
  @ApiProperty({
    description: 'Hitlist size metrics',
    type: SummaryCardDto,
  })
  hitlist_size: SummaryCardDto;

  @ApiProperty({
    description: 'Converted customers metrics',
    type: SummaryCardDto,
  })
  converted_customers: SummaryCardDto;

  @ApiProperty({
    description: 'Calls completed metrics',
    type: SummaryCardDto,
  })
  calls_completed: SummaryCardDto;

  @ApiProperty({
    description: 'Visits metrics',
    type: SummaryCardDto,
  })
  visits: SummaryCardDto;
}

export class CallsVisitsDetailDto {
  @ApiProperty({ description: 'Calls count', example: 150 })
  calls: number;

  @ApiProperty({ description: 'Visits count', example: 75 })
  visits: number;
}

export class CallsVisitsCategoryDto {
  @ApiProperty({ description: 'Leads metrics', type: CallsVisitsDetailDto })
  leads: CallsVisitsDetailDto;

  @ApiProperty({
    description: 'Customer service metrics',
    type: CallsVisitsDetailDto,
  })
  customer_service: CallsVisitsDetailDto;

  @ApiProperty({ description: 'Loans metrics', type: CallsVisitsDetailDto })
  loans: CallsVisitsDetailDto;
}

export class CallsVisitsDto {
  @ApiProperty({ description: 'Total metrics', type: CallsVisitsCategoryDto })
  total: CallsVisitsCategoryDto;

  @ApiProperty({
    description: 'This month metrics',
    type: CallsVisitsCategoryDto,
  })
  this_month: CallsVisitsCategoryDto;
}

export class DormantDetailDto {
  @ApiProperty({ description: 'Uncontacted records count', example: 25 })
  uncontacted: number;

  @ApiProperty({ description: 'Contacted records count', example: 15 })
  contacted: number;
}

export class DormantRecordsDto {
  @ApiProperty({ description: 'Total dormant records', type: DormantDetailDto })
  total: DormantDetailDto;

  @ApiProperty({
    description: 'This month dormant records',
    type: DormantDetailDto,
  })
  this_month: DormantDetailDto;
}

export class MonthlyHitlistProgressDto {
  @ApiProperty({
    description: 'Number of leads contacted this month',
    example: 45,
  })
  contacted: number;

  @ApiProperty({
    description: 'Number of leads remaining to be contacted this month',
    example: 25,
  })
  remaining: number;
}

export class CallsVisitsVsTargetDataDto {
  @ApiProperty({
    description: 'Name of the data series (Actual or Target)',
    example: 'Actual',
  })
  name: string;

  @ApiProperty({
    description: 'Array containing calls and visits data [calls, visits]',
    example: [120, 85],
    type: [Number],
  })
  data: number[];
}

export class CallsVisitsVsTargetDto {
  @ApiProperty({
    description: 'Actual calls and visits data',
    type: CallsVisitsVsTargetDataDto,
  })
  actual: CallsVisitsVsTargetDataDto;

  @ApiProperty({
    description: 'Target calls and visits data',
    type: CallsVisitsVsTargetDataDto,
  })
  target: CallsVisitsVsTargetDataDto;
}

export class CallsVsTargetsPerOfficerDto {
  @ApiProperty({
    description: 'Name of the user/officer',
    example: 'John Doe',
  })
  user_name: string;

  @ApiProperty({
    description: 'Number of calls made by the officer',
    example: 45,
  })
  calls_made: number;

  @ApiProperty({
    description: 'Call target for the officer',
    example: 50,
  })
  calls_target: number;
}

export class VisitsVsTargetsPerOfficerDto {
  @ApiProperty({
    description: 'Name of the user/officer',
    example: 'John Doe',
  })
  user_name: string;

  @ApiProperty({
    description: 'Number of visits made by the officer',
    example: 25,
  })
  visits_made: number;

  @ApiProperty({
    description: 'Visit target for the officer',
    example: 30,
  })
  visits_target: number;
}

export class BranchManagerDashboardDto {
  @ApiProperty({
    description: 'Summary cards with various metrics',
    type: SummaryCardsDto,
    required: false,
  })
  summary_cards?: SummaryCardsDto;

  @ApiProperty({
    description: 'Branch conversion trend by year and month',
    example: {
      '2023': [18, 21, 19, 24, 22, 25, 28, 26, 29, 32, 30, 33],
      '2024': [21, 24, 22, 27, 25, 28, 31, 29, 32, 35, 33, 36],
      '2025': [null, null, 25, 30, 28, 31, 34, 32, 35, 38, 36, 39],
    },
    required: false,
  })
  branch_conversion_trend?: Record<string, (number | null)[]>;

  @ApiProperty({
    description: 'Calls and visits metrics by category',
    type: CallsVisitsDto,
    required: false,
  })
  calls_and_visits?: CallsVisitsDto;

  @ApiProperty({
    description: 'Dormant records metrics',
    type: DormantRecordsDto,
    required: false,
  })
  dormant_records?: DormantRecordsDto;

  @ApiProperty({
    description: 'Monthly hitlist progress for contacted vs remaining leads',
    type: MonthlyHitlistProgressDto,
    required: false,
  })
  monthly_hitlist_progress?: MonthlyHitlistProgressDto;

  @ApiProperty({
    description: 'Calls and visits vs targets comparison',
    type: CallsVisitsVsTargetDto,
    required: false,
  })
  calls_visits_vs_target?: CallsVisitsVsTargetDto;

  @ApiProperty({
    description: 'Calls vs targets per officer breakdown',
    type: [CallsVsTargetsPerOfficerDto],
    required: false,
  })
  calls_vs_targets_per_officer?: CallsVsTargetsPerOfficerDto[];

  @ApiProperty({
    description: 'Visits vs targets per officer breakdown',
    type: [VisitsVsTargetsPerOfficerDto],
    required: false,
  })
  visits_vs_targets_per_officer?: VisitsVsTargetsPerOfficerDto[];
}
