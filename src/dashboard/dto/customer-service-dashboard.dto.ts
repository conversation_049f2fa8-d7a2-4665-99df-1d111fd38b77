import { ApiProperty } from '@nestjs/swagger';

export class SizeMetricDto {
  @ApiProperty({
    description: 'Total count',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Count for this month',
    example: 25,
  })
  this_month: number;
}

export class CustomerServiceSummaryCardsDto {
  @ApiProperty({
    description: 'Dormant size metrics',
    type: SizeMetricDto,
  })
  total_dormant_size: SizeMetricDto;

  @ApiProperty({
    description: '2by2by2 size metrics',
    type: SizeMetricDto,
  })
  total_2by2by2_size: SizeMetricDto;

  @ApiProperty({
    description: 'Calls made this month',
    example: 45,
  })
  calls_this_month: number;

  @ApiProperty({
    description: 'Overdue calls this month',
    example: 12,
  })
  overdue_calls_this_month: number;
}

export class UpcomingCallDto {
  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Expected completion date',
    example: '2025-08-20T10:00:00.000Z',
  })
  date: Date;
}

export class MonthlyCallsVsTargetsDto {
  @ApiProperty({
    description: 'Target for the month',
    example: 50,
  })
  target: number;

  @ApiProperty({
    description: 'Actual calls made in the month',
    example: 42,
  })
  calls: number;
}

export class PersonalMetricsDto {
  @ApiProperty({
    description: 'Calls scheduled for today',
    example: 8,
  })
  calls_today: number;

  @ApiProperty({
    description: 'Overdue calls count',
    example: 5,
  })
  overdue_calls: number;

  @ApiProperty({
    description: 'Upcoming calls list',
    type: [UpcomingCallDto],
  })
  upcoming_calls: UpcomingCallDto[];

  @ApiProperty({
    description: 'Total calls made this month by the user',
    example: 42,
  })
  my_calls_this_month: number;

  @ApiProperty({
    description: 'Monthly calls vs targets comparison',
    type: Object,
    example: {
      last_month: { target: 45, calls: 38 },
      this_month: { target: 50, calls: 42 },
    },
  })
  monthly_calls_vs_targets: {
    last_month: MonthlyCallsVsTargetsDto;
    this_month: MonthlyCallsVsTargetsDto;
  };
}

export class CompletionByPhaseDto {
  @ApiProperty({
    description: 'Phase name',
    example: 'First 2',
  })
  name: string;

  @ApiProperty({
    description: 'Total count for this phase',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'Completed count for this phase',
    example: 35,
  })
  completed: number;
}

export class TwoByTwoCompletionDto {
  @ApiProperty({
    description: 'Completed 2by2by2 records',
    example: 120,
  })
  completed: number;

  @ApiProperty({
    description: 'In progress 2by2by2 records',
    example: 80,
  })
  in_progress: number;
}

export class MonthlyFeedbackDto {
  @ApiProperty({
    description: 'Feedback category name',
    example: 'Satisfied',
  })
  name: string;

  @ApiProperty({
    description: 'Count of feedback',
    example: 25,
  })
  value: number;
}

export class MonthlyCallStatusDto {
  @ApiProperty({
    description: 'Call status name',
    example: 'Completed',
  })
  name: string;

  @ApiProperty({
    description: 'Count of calls with this status',
    example: 45,
  })
  value: number;
}

export class MonthlyDormantProgressDto {
  @ApiProperty({
    description: 'Completed dormant records',
    example: 75,
  })
  completed: number;

  @ApiProperty({
    description: 'Remaining dormant records',
    example: 25,
  })
  remaining: number;
}

export class OverdueCallsTrendDto {
  @ApiProperty({
    description: 'Month name',
    example: 'August 2025',
  })
  month: string;

  @ApiProperty({
    description: 'Number of overdue calls',
    example: 15,
    nullable: true,
  })
  value: number | null;
}

export class OverdueCallDto {
  @ApiProperty({
    description: 'Agent name',
    example: 'John Doe',
  })
  agent: string;

  @ApiProperty({
    description: 'Customer name',
    example: 'Jane Smith',
  })
  customer_name: string;

  @ApiProperty({
    description: 'Phase type',
    example: 'first2',
  })
  phase: string;

  @ApiProperty({
    description: 'Expected completion date',
    example: '2025-08-15T10:00:00.000Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Days overdue',
    example: 5,
  })
  overdue_by: number;

  @ApiProperty({
    description: 'Is the task completed',
    example: false,
  })
  is_done: boolean;
}

export class CustomerServiceDashboardDto {
  @ApiProperty({
    description: 'Summary cards with various metrics',
    type: CustomerServiceSummaryCardsDto,
  })
  summary_cards: CustomerServiceSummaryCardsDto;

  @ApiProperty({
    description: 'Personal metrics for the logged-in user',
    type: PersonalMetricsDto,
  })
  personal: PersonalMetricsDto;

  @ApiProperty({
    description: 'Completion statistics by phase',
    type: [CompletionByPhaseDto],
  })
  completion_by_phase: CompletionByPhaseDto[];

  @ApiProperty({
    description: '2by2by2 completion statistics',
    type: TwoByTwoCompletionDto,
  })
  '2by2by2_completion': TwoByTwoCompletionDto;

  @ApiProperty({
    description: 'Monthly customer feedback statistics',
    type: [MonthlyFeedbackDto],
  })
  monthly_customer_feedback: MonthlyFeedbackDto[];

  @ApiProperty({
    description: 'Monthly call status statistics',
    type: [MonthlyCallStatusDto],
  })
  monthly_call_status: MonthlyCallStatusDto[];

  @ApiProperty({
    description: 'Monthly dormant progress',
    type: MonthlyDormantProgressDto,
  })
  monthly_dormant_progress: MonthlyDormantProgressDto;

  @ApiProperty({
    description: 'Overdue calls trend for past 6 months',
    type: [OverdueCallsTrendDto],
  })
  overdue_calls_trend: OverdueCallsTrendDto[];

  @ApiProperty({
    description: 'List of overdue calls',
    type: [OverdueCallDto],
  })
  overdue_calls: OverdueCallDto[];
}
