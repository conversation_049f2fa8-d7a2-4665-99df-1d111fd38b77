import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DashboardService } from './dashboard.service';
import { BranchManagerDashboardDto } from './dto/branch-manager-dashboard.dto';

/**
 * Controller handling dashboard-related HTTP endpoints
 */
@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  /**
   * Gets branch manager dashboard data
   * GET /dashboard/branch-manager
   */
  @Get('branch-manager')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get branch manager dashboard',
    description: `Returns dashboard data for branch managers. Supports different sections:
    
    **Section=first (default):** Summary cards data, hitlist to conversion overview, branch conversion trend, Branch Calls & Visits, Branch Dormant Status
    
    **Section=second:** Hitlist Progress, Calls & Visits vs Targets this month, Monthly Calls vs Targets per officer, Monthly Visits vs Targets per officer
    
    **Section=third:** MONTHLY ACTIVITIES OVERVIEW, ACTIVITIES SCHEDULE`,
  })
  @ApiQuery({
    name: 'section',
    required: false,
    description:
      'Dashboard section to return (first, second, third). Defaults to "first"',
    example: 'first',
  })
  @ApiResponse({
    status: 200,
    description: 'Dashboard data retrieved successfully',
    type: BranchManagerDashboardDto,
  })
  async getBranchManagerDashboard(
    @Request() req: any,
    @Query('section') section?: string,
  ): Promise<BranchManagerDashboardDto> {
    return this.dashboardService.getBranchManagerDashboard(
      req.user.branch_id,
      section,
    );
  }

  /**
   * Gets customer service dashboard data
   * GET /dashboard/customer-service
   */
  @Get('customer-service')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get customer service dashboard',
    description: `Returns dashboard data for customer service officers. Supports different officer types:

    **Officer=cxo:** Customer Experience Officer dashboard with summary cards, personal metrics, and call targets`,
  })
  @ApiQuery({
    name: 'officer',
    required: false,
    description:
      'Officer type to return dashboard for (cxo). Defaults to "cxo"',
    example: 'cxo',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer service dashboard data retrieved successfully',
  })
  async getCustomerServiceDashboard(
    @Request() req: any,
    @Query('officer') officer?: string,
  ) {
    return this.dashboardService.getCustomerServiceDashboard(req.user, officer);
  }
}
