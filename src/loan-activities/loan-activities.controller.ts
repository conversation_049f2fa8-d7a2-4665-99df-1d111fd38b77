import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UseInterceptors,
  UploadedFiles,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  ParseIntPipe,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { LoanActivitiesService } from './loan-activities.service';
import { CreateLoanActivityDto } from './dto/create-loan-activity.dto';
import { CreateLoanActivityFormDto } from './dto/create-loan-activity-form.dto';
import { LoanActivityResponseDto } from './dto/loan-activity-response.dto';

/**
 * Controller handling all loan activity-related HTTP endpoints
 * Provides RESTful API for loan activity management
 */
@ApiTags('Loan Activities')
@Controller('loan-activities')
export class LoanActivitiesController {
  constructor(private readonly loanActivitiesService: LoanActivitiesService) {}

  /**
   * Creates a new loan activity with optional file attachments
   * POST /loan-activities
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Loan activity data with optional file attachments',
    schema: {
      type: 'object',
      properties: {
        loan_client_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'UUID of the loan client this activity is associated with',
        },
        loan_account_number: {
          type: 'string',
          example: '**********',
          description: 'Loan account number',
        },
        purpose_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'UUID of the purpose of this activity',
        },
        loan_balance: {
          type: 'string',
          example: '50000.00',
          description: 'Current loan balance',
        },
        arrears_days: {
          type: 'number',
          example: 30,
          description: 'Number of days in arrears',
        },
        comment: {
          type: 'string',
          example: 'Customer requested loan restructuring due to financial difficulties',
          description: 'Comments about the loan activity',
        },
        rm_user_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
          description: 'UUID of the RM user performing this activity',
        },
        via_api: {
          type: 'boolean',
          example: false,
          description: 'Whether this activity was created via API',
        },
                       api_call_reference: {
                 type: 'string',
                 example: 'api-call-12345',
                 description: 'API call reference for tracking',
               },
               interaction_type: {
                 type: 'string',
                 example: 'call',
                 description: 'Type of interaction (e.g., call, visit, email)',
               },
               call_status: {
                 type: 'string',
                 example: 'answered',
                 description: 'Status of the call (e.g., answered, missed, busy)',
               },
               visit_status: {
                 type: 'string',
                 example: 'completed',
                 description: 'Status of the visit (e.g., completed, scheduled, cancelled)',
               },
               next_followup_date: {
                 type: 'string',
                 format: 'date-time',
                 example: '2024-02-15T10:00:00.000Z',
                 description: 'Date for next follow-up',
               },
               followup_status: {
                 type: 'string',
                 example: 'pending',
                 description: 'Status of the follow-up (e.g., pending, completed, cancelled)',
               },
               call_duration_minutes: {
                 type: 'number',
                 example: 15,
                 description: 'Duration of the call in minutes',
               },
               attachments: {
                 type: 'array',
                 items: {
                   type: 'string',
                   format: 'binary',
                 },
                 description: 'Array of file attachments',
               },
      },
      required: ['rm_user_id'],
    },
  })
  @ApiOperation({
    summary: 'Create a new loan activity',
    description: 'Creates a new loan activity with optional file attachments. The activity can be associated with a loan client and include details about loan balance, arrears, and comments.',
  })
  @ApiResponse({
    status: 201,
    description: 'Loan activity created successfully',
    type: LoanActivityResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Bad request - validation failed or invalid data provided',
  })
  @ApiNotFoundResponse({
    description: 'Not found - RM user, loan client, or purpose not found',
  })
  async createLoanActivity(
    @Body(ValidationPipe) formData: CreateLoanActivityFormDto,
    @UploadedFiles() files: Express.Multer.File[],
  ): Promise<LoanActivityResponseDto> {
    // Transform form data to DTO format
    const createLoanActivityDto: CreateLoanActivityDto = {
      ...formData,
      attachments: files?.map((file) => ({
        name: file.originalname,
        size: file.size,
        type: file.mimetype,
        file,
      })) || [],
    };

    return this.loanActivitiesService.createLoanActivity(createLoanActivityDto);
  }

  /**
   * Retrieves all loan activities with filtering and pagination
   * GET /loan-activities
   */
  @Get()
  @ApiOperation({
    summary: 'Get all loan activities',
    description: 'Retrieves all loan activities with optional filtering and pagination support.',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number (default: 1)',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (default: 10)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'loan_client_id',
    description: 'Filter by loan client ID',
    required: false,
    type: String,
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'rm_user_id',
    description: 'Filter by RM user ID',
    required: false,
    type: String,
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'purpose_id',
    description: 'Filter by purpose ID',
    required: false,
    type: String,
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'interaction_type',
    description: 'Filter by interaction type (call, visit, email)',
    required: false,
    type: String,
    example: 'call',
  })
  @ApiQuery({
    name: 'followup_status',
    description: 'Filter by followup status (pending, completed, cancelled)',
    required: false,
    type: String,
    example: 'pending',
  })
  @ApiQuery({
    name: 'from_date',
    description: 'Filter activities from this date (ISO format)',
    required: false,
    type: String,
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'to_date',
    description: 'Filter activities to this date (ISO format)',
    required: false,
    type: String,
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Loan activities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LoanActivityResponseDto' },
        },
        total: { type: 'number', example: 100 },
        page: { type: 'number', example: 1 },
        limit: { type: 'number', example: 10 },
        totalPages: { type: 'number', example: 10 },
      },
    },
  })
  async getAllLoanActivities(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('loan_client_id') loanClientId?: string,
    @Query('rm_user_id') rmUserId?: string,
    @Query('purpose_id') purposeId?: string,
    @Query('interaction_type') interactionType?: string,
    @Query('followup_status') followupStatus?: string,
    @Query('from_date') fromDate?: string,
    @Query('to_date') toDate?: string,
  ) {
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    const filters = {
      loanClientId,
      rmUserId,
      purposeId,
      interactionType,
      followupStatus,
      fromDate,
      toDate,
    };

    return this.loanActivitiesService.getAllLoanActivities(
      pageNumber,
      limitNumber,
      filters,
    );
  }

  /**
   * Retrieves all follow-ups from loan activities with pagination
   * GET /loan-activities/followups
   */
  @Get('followups')
  @ApiOperation({
    summary: 'Get all follow-ups',
    description: 'Retrieves all loan activities that have follow-up dates with customer and officer details.',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number (default: 1)',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (default: 10)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by followup status (pending, completed, cancelled)',
    required: false,
    type: String,
    example: 'pending',
  })
  @ApiResponse({
    status: 200,
    description: 'Follow-ups retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
              customer_id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440001' },
              customer_name: { type: 'string', example: 'John Doe' },
              anchor_name: { type: 'string', example: 'ABC Company' },
              assigned_officer: { type: 'string', example: 'Jane Smith' },
              assigned_officer_id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440002' },
              followup_date: { type: 'string', example: '2024-02-15T10:00:00.000Z' },
              status: { type: 'string', example: 'pending' },
              created_date: { type: 'string', example: '2024-01-15T10:30:00.000Z' },
            },
          },
        },
        total: { type: 'number', example: 25 },
        page: { type: 'number', example: 1 },
        limit: { type: 'number', example: 10 },
        totalPages: { type: 'number', example: 3 },
      },
    },
  })
  async getFollowups(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('status') status?: string,
  ) {
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    return this.loanActivitiesService.getFollowups(
      pageNumber,
      limitNumber,
      status,
    );
  }

  /**
   * Retrieves a loan activity by ID
   * GET /loan-activities/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get loan activity by ID',
    description: 'Retrieves detailed information about a specific loan activity including related data like RM user, loan client, purpose, and attachments.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the loan activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Loan activity retrieved successfully',
    type: LoanActivityResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Not found - loan activity with the specified ID does not exist',
  })
  async getLoanActivityById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<LoanActivityResponseDto> {
    return this.loanActivitiesService.getLoanActivityById(id);
  }

  /**
   * Retrieves loan activities for a specific loan client with pagination
   * GET /loan-activities/client/:loanClientId
   */
  @Get('client/:loanClientId')
  @ApiOperation({
    summary: 'Get loan activities by client',
    description: 'Retrieves all loan activities for a specific loan client with pagination support.',
  })
  @ApiParam({
    name: 'loanClientId',
    description: 'UUID of the loan client',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number (default: 1)',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (default: 10)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Loan activities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LoanActivityResponseDto' },
        },
        total: { type: 'number', example: 25 },
        page: { type: 'number', example: 1 },
        limit: { type: 'number', example: 10 },
        totalPages: { type: 'number', example: 3 },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found - loan client with the specified ID does not exist',
  })
  async getLoanActivitiesByClient(
    @Param('loanClientId', ParseUUIDPipe) loanClientId: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    return this.loanActivitiesService.getLoanActivitiesByClient(
      loanClientId,
      pageNumber,
      limitNumber,
    );
  }

  /**
   * Retrieves loan activities for a specific RM user with pagination
   * GET /loan-activities/rm-user/:rmUserId
   */
  @Get('rm-user/:rmUserId')
  @ApiOperation({
    summary: 'Get loan activities by RM user',
    description: 'Retrieves all loan activities performed by a specific RM user with pagination support.',
  })
  @ApiParam({
    name: 'rmUserId',
    description: 'UUID of the RM user',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number (default: 1)',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (default: 10)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Loan activities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LoanActivityResponseDto' },
        },
        total: { type: 'number', example: 15 },
        page: { type: 'number', example: 1 },
        limit: { type: 'number', example: 10 },
        totalPages: { type: 'number', example: 2 },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found - RM user with the specified ID does not exist',
  })
  async getLoanActivitiesByRmUser(
    @Param('rmUserId', ParseUUIDPipe) rmUserId: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    return this.loanActivitiesService.getLoanActivitiesByRmUser(
      rmUserId,
      pageNumber,
      limitNumber,
    );
  }

  

  /**
   * Updates the followup status of a loan activity
   * PATCH /loan-activities/:id/followup-status
   */
  @Patch(':id/followup-status')
  @ApiOperation({
    summary: 'Update followup status',
    description: 'Updates the followup status of a specific loan activity.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the loan activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiBody({
    description: 'Followup status update data',
    schema: {
      type: 'object',
      properties: {
        followup_status: {
          type: 'string',
          enum: ['pending', 'completed', 'cancelled'],
          example: 'completed',
          description: 'New followup status',
        },
      },
      required: ['followup_status'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Followup status updated successfully',
    type: LoanActivityResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Not found - loan activity with the specified ID does not exist',
  })
  async updateFollowupStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateData: { followup_status: string },
  ): Promise<LoanActivityResponseDto> {
    return this.loanActivitiesService.updateFollowupStatus(id, updateData.followup_status);
  }
}
