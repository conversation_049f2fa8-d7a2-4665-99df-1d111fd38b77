import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  IsNumber,
  IsDecimal,
  IsInt,
  IsBoolean,
  MaxLength,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a loan activity via form data
 * Handles multipart form data with file uploads
 */
export class CreateLoanActivityFormDto {
  @ApiPropertyOptional({
    description: 'UUID of the loan client this activity is associated with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Loan client ID must be a valid UUID' })
  loan_client_id?: string;

  @ApiPropertyOptional({
    description: 'Loan account number',
    example: '**********',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Loan account number must be a string' })
  @MaxLength(50, { message: 'Loan account number cannot exceed 50 characters' })
  loan_account_number?: string;

  @ApiPropertyOptional({
    description: 'UUID of the purpose of this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Purpose ID must be a valid UUID' })
  purpose_id?: string;

  @ApiPropertyOptional({
    description: 'Current loan balance',
    example: '50000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Loan balance must be a valid decimal number' })
  loan_balance?: string;

  @ApiPropertyOptional({
    description: 'Number of days in arrears',
    example: 30,
    minimum: 0,
  })
  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value, 10) : undefined)
  @IsInt({ message: 'Arrears days must be an integer' })
  arrears_days?: number;

  @ApiPropertyOptional({
    description: 'Comments about the loan activity',
    example: 'Customer requested loan restructuring due to financial difficulties',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Comment must be a string' })
  @MaxLength(1000, { message: 'Comment cannot exceed 1000 characters' })
  comment?: string;

  @ApiProperty({
    description: 'UUID of the RM user performing this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID(4, { message: 'RM user ID must be a valid UUID' })
  rm_user_id: string;

  @ApiPropertyOptional({
    description: 'Whether this activity was created via API',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean({ message: 'Via API must be a boolean' })
  via_api?: boolean;

           @ApiPropertyOptional({
           description: 'API call reference for tracking',
           example: 'api-call-12345',
           maxLength: 255,
         })
         @IsOptional()
         @IsString({ message: 'API call reference must be a string' })
         @MaxLength(255, { message: 'API call reference cannot exceed 255 characters' })
         api_call_reference?: string;

         @ApiPropertyOptional({
           description: 'Type of interaction (e.g., call, visit, email)',
           example: 'call',
           maxLength: 50,
         })
         @IsOptional()
         @IsString({ message: 'Interaction type must be a string' })
         @MaxLength(50, { message: 'Interaction type cannot exceed 50 characters' })
         interaction_type?: string;

         @ApiPropertyOptional({
           description: 'Status of the call (e.g., answered, missed, busy)',
           example: 'answered',
           maxLength: 50,
         })
         @IsOptional()
         @IsString({ message: 'Call status must be a string' })
         @MaxLength(50, { message: 'Call status cannot exceed 50 characters' })
         call_status?: string;

         @ApiPropertyOptional({
           description: 'Status of the visit (e.g., completed, scheduled, cancelled)',
           example: 'completed',
           maxLength: 50,
         })
         @IsOptional()
         @IsString({ message: 'Visit status must be a string' })
         @MaxLength(50, { message: 'Visit status cannot exceed 50 characters' })
         visit_status?: string;

         @ApiPropertyOptional({
           description: 'Date for next follow-up',
           example: '2024-02-15T10:00:00.000Z',
         })
         @IsOptional()
         @IsString({ message: 'Next followup date must be a valid date string' })
         next_followup_date?: string;

         @ApiPropertyOptional({
           description: 'Status of the follow-up (e.g., pending, completed, cancelled)',
           example: 'pending',
           maxLength: 50,
         })
         @IsOptional()
         @IsString({ message: 'Followup status must be a string' })
         @MaxLength(50, { message: 'Followup status cannot exceed 50 characters' })
         followup_status?: string;

         @ApiPropertyOptional({
           description: 'Duration of the call in minutes',
           example: 15,
           minimum: 0,
         })
         @IsOptional()
         @Transform(({ value }) => value ? parseInt(value, 10) : undefined)
         @IsInt({ message: 'Call duration must be an integer' })
         call_duration_minutes?: number;
}
