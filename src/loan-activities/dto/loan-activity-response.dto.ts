import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for loan activity response
 */
export class LoanActivityResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the loan activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'UUID of the loan client this activity is associated with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  loan_client_id?: string;

  @ApiPropertyOptional({
    description: 'Loan account number',
    example: '**********',
  })
  loan_account_number?: string;

  @ApiPropertyOptional({
    description: 'UUID of the purpose of this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  purpose_id?: string;

  @ApiPropertyOptional({
    description: 'Current loan balance',
    example: '50000.00',
  })
  loan_balance?: string;

  @ApiPropertyOptional({
    description: 'Number of days in arrears',
    example: 30,
  })
  arrears_days?: number;

  @ApiPropertyOptional({
    description: 'Comments about the loan activity',
    example: 'Customer requested loan restructuring due to financial difficulties',
  })
  comment?: string;

  @ApiProperty({
    description: 'UUID of the RM user performing this activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  rm_user_id: string;

  @ApiProperty({
    description: 'Date and time when the activity was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  created_at: string;

  @ApiPropertyOptional({
    description: 'Whether this activity was created via API',
    example: false,
  })
  via_api?: boolean;

           @ApiPropertyOptional({
           description: 'API call reference for tracking',
           example: 'api-call-12345',
         })
         api_call_reference?: string;

         @ApiPropertyOptional({
           description: 'Type of interaction (e.g., call, visit, email)',
           example: 'call',
         })
         interaction_type?: string;

         @ApiPropertyOptional({
           description: 'Status of the call (e.g., answered, missed, busy)',
           example: 'answered',
         })
         call_status?: string;

         @ApiPropertyOptional({
           description: 'Status of the visit (e.g., completed, scheduled, cancelled)',
           example: 'completed',
         })
         visit_status?: string;

         @ApiPropertyOptional({
           description: 'Date for next follow-up',
           example: '2024-02-15T10:00:00.000Z',
         })
         next_followup_date?: string;

         @ApiPropertyOptional({
           description: 'Status of the follow-up (e.g., pending, completed, cancelled)',
           example: 'pending',
         })
         followup_status?: string;

         @ApiPropertyOptional({
           description: 'Duration of the call in minutes',
           example: 15,
         })
         call_duration_minutes?: number;

         @ApiProperty({
           description: 'Date and time when the activity was last updated',
           example: '2024-01-15T10:30:00.000Z',
         })
         updated_at: string;

  @ApiPropertyOptional({
    description: 'Purpose details',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
      name: { type: 'string', example: 'Loan Review' },
      description: { type: 'string', example: 'Review of loan terms and conditions' },
    },
  })
  purpose?: {
    id: string;
    name: string;
    description?: string;
  };

  @ApiPropertyOptional({
    description: 'RM user details',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
      name: { type: 'string', example: 'John Doe' },
      email: { type: 'string', example: '<EMAIL>' },
      rm_code: { type: 'string', example: 'RM001' },
    },
  })
  rm_user?: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  };

  @ApiPropertyOptional({
    description: 'Loan client details',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
      customer_name: { type: 'string', example: 'ABC Company Ltd' },
      account_number: { type: 'string', example: '**********' },
    },
  })
  loan_client?: {
    id: string;
    customer_name?: string;
    account_number?: string;
  };

  @ApiPropertyOptional({
    description: 'Array of file attachments',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
        file_url: { type: 'string', example: 'https://example.com/files/document.pdf' },
        created_at: { type: 'string', example: '2024-01-15T10:30:00.000Z' },
      },
    },
  })
  attachments?: Array<{
    id: string;
    file_url?: string;
    created_at: string;
  }>;
}
