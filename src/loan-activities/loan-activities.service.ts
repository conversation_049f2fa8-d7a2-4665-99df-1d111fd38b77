import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { FileStorageService } from '../common/services/file-storage.service';
import { CreateLoanActivityDto } from './dto/create-loan-activity.dto';
import { LoanActivityResponseDto } from './dto/loan-activity-response.dto';

/**
 * Service handling all loan activity-related business logic
 * Provides operations for creating and managing loan activities
 */
@Injectable()
export class LoanActivitiesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileStorageService: FileStorageService,
  ) {}

  /**
   * Creates a new loan activity with optional attachments
   * @param createLoanActivityDto - Data for creating the loan activity
   * @returns Promise<LoanActivityResponseDto> - The created loan activity
   */
  async createLoanActivity(
    createLoanActivityDto: CreateLoanActivityDto,
  ): Promise<LoanActivityResponseDto> {
         const {
       loan_client_id,
       loan_account_number,
       purpose_id,
       loan_balance,
       arrears_days,
       comment,
       rm_user_id,
       via_api = false,
       api_call_reference,
       interaction_type,
       call_status,
       visit_status,
       next_followup_date,
       followup_status,
       call_duration_minutes,
       attachments = [],
     } = createLoanActivityDto;

    // Validate that the RM user exists
    const rmUser = await this.prisma.user.findUnique({
      where: { id: rm_user_id },
      select: {
        id: true,
        name: true,
        email: true,
        rm_code: true,
      },
    });

    if (!rmUser) {
      throw new NotFoundException(
        `RM user with ID '${rm_user_id}' not found`,
      );
    }

               // Validate that the loan client exists if provided
           let loanClient: any = null;
           if (loan_client_id) {
             loanClient = await this.prisma.loanClient.findUnique({
               where: { id: loan_client_id },
               select: {
                 id: true,
                 customer_name: true,
                 account_number: true,
               },
             });

             if (!loanClient) {
               throw new NotFoundException(
                 `Loan client with ID '${loan_client_id}' not found`,
               );
             }
           }

               // Validate that the purpose exists if provided
           let purpose: any = null;
           if (purpose_id) {
             purpose = await this.prisma.purposeOfActivity.findUnique({
               where: { id: purpose_id },
               select: {
                 id: true,
                 name: true,
                 description: true,
               },
             });

             if (!purpose) {
               throw new NotFoundException(
                 `Purpose with ID '${purpose_id}' not found`,
               );
             }
           }

    try {
      // Create the loan activity with attachments in a transaction
      const loanActivity = await this.prisma.$transaction(async (tx) => {
        // Create the loan activity
                 const newLoanActivity = await tx.loanActivity.create({
           data: {
             loan_client_id: loan_client_id || null,
             loan_account_number: loan_account_number || null,
             purpose_id: purpose_id || null,
             loan_balance: loan_balance ? parseFloat(loan_balance) : null,
             arrears_days: arrears_days || null,
             comment: comment || null,
             rm_user_id,
             via_api,
             api_call_reference: api_call_reference || null,
             interaction_type: interaction_type || null,
             call_status: call_status || null,
             visit_status: visit_status || null,
             next_followup_date: next_followup_date ? new Date(next_followup_date) : null,
             followup_status: followup_status || null,
             call_duration_minutes: call_duration_minutes || null,
           },
          include: {
            rm_user: {
              select: {
                id: true,
                name: true,
                email: true,
                rm_code: true,
              },
            },
            loan_client: {
              select: {
                id: true,
                customer_name: true,
                account_number: true,
              },
            },
            purpose: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            attachments: {
              select: {
                id: true,
                file_url: true,
                created_at: true,
              },
              orderBy: {
                created_at: 'desc',
              },
            },
          },
        });

        // Handle file attachments if provided
        if (attachments && attachments.length > 0) {
                           const attachmentPromises = attachments.map(async (attachment) => {
                   // Upload file to storage
                   const savedFile = await this.fileStorageService.saveFile(
                     attachment.file,
                     'loan-activities',
                   );

                               // Create attachment record
                   return tx.activityAttachment.create({
                     data: {
                       loan_activity_id: newLoanActivity.id,
                       file_url: savedFile.url,
                     },
                     select: {
                       id: true,
                       file_url: true,
                       created_at: true,
                     },
                   });
          });

          const createdAttachments = await Promise.all(attachmentPromises);
          newLoanActivity.attachments = createdAttachments;
        }

        return newLoanActivity;
      });

      // Transform the response to match the DTO structure
      return this.transformToLoanActivityResponse(loanActivity);
    } catch (error) {
      // Handle specific database errors
      if (error.code === 'P2002') {
        throw new BadRequestException(
          'A loan activity with similar details already exists',
        );
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Transforms a loan activity database record to response DTO
   * @param loanActivity - The loan activity record from database
   * @returns LoanActivityResponseDto - The transformed response
   */
  private transformToLoanActivityResponse(
    loanActivity: any,
  ): LoanActivityResponseDto {
         return {
       id: loanActivity.id,
       loan_client_id: loanActivity.loan_client_id,
       loan_account_number: loanActivity.loan_account_number,
       purpose_id: loanActivity.purpose_id,
       loan_balance: loanActivity.loan_balance?.toString(),
       arrears_days: loanActivity.arrears_days,
       comment: loanActivity.comment,
       rm_user_id: loanActivity.rm_user_id,
       created_at: loanActivity.created_at.toISOString(),
       updated_at: loanActivity.updated_at.toISOString(),
       via_api: loanActivity.via_api,
       api_call_reference: loanActivity.api_call_reference,
       interaction_type: loanActivity.interaction_type,
       call_status: loanActivity.call_status,
       visit_status: loanActivity.visit_status,
       next_followup_date: loanActivity.next_followup_date?.toISOString(),
       followup_status: loanActivity.followup_status,
       call_duration_minutes: loanActivity.call_duration_minutes,
      purpose: loanActivity.purpose
        ? {
            id: loanActivity.purpose.id,
            name: loanActivity.purpose.name,
            description: loanActivity.purpose.description,
          }
        : undefined,
      rm_user: loanActivity.rm_user
        ? {
            id: loanActivity.rm_user.id,
            name: loanActivity.rm_user.name,
            email: loanActivity.rm_user.email,
            rm_code: loanActivity.rm_user.rm_code,
          }
        : undefined,
      loan_client: loanActivity.loan_client
        ? {
            id: loanActivity.loan_client.id,
            customer_name: loanActivity.loan_client.customer_name,
            account_number: loanActivity.loan_client.account_number,
          }
        : undefined,
      attachments: loanActivity.attachments?.map((attachment: any) => ({
        id: attachment.id,
        file_url: attachment.file_url,
        created_at: attachment.created_at.toISOString(),
      })) || [],
    };
  }

  /**
   * Retrieves all loan activities with filtering and pagination
   * @param page - Page number (default: 1)
   * @param limit - Number of items per page (default: 10)
   * @param filters - Object containing filter criteria
   * @returns Promise<{ data: LoanActivityResponseDto[], total: number, page: number, limit: number, totalPages: number }>
   */
  async getAllLoanActivities(
    page: number = 1,
    limit: number = 10,
    filters: {
      loanClientId?: string;
      rmUserId?: string;
      purposeId?: string;
      interactionType?: string;
      followupStatus?: string;
      fromDate?: string;
      toDate?: string;
    } = {},
  ) {
    const skip = (page - 1) * limit;

    // Build where clause based on filters
    const whereClause: any = {};

    if (filters.loanClientId) {
      whereClause.loan_client_id = filters.loanClientId;
    }

    if (filters.rmUserId) {
      whereClause.rm_user_id = filters.rmUserId;
    }

    if (filters.purposeId) {
      whereClause.purpose_id = filters.purposeId;
    }

    if (filters.interactionType) {
      whereClause.interaction_type = filters.interactionType;
    }

    if (filters.followupStatus) {
      whereClause.followup_status = filters.followupStatus;
    }

    // Date range filtering
    if (filters.fromDate || filters.toDate) {
      whereClause.created_at = {};
      if (filters.fromDate) {
        whereClause.created_at.gte = new Date(filters.fromDate);
      }
      if (filters.toDate) {
        whereClause.created_at.lte = new Date(filters.toDate);
      }
    }

    // Execute parallel queries for better performance
    const [loanActivities, total] = await Promise.all([
      this.prisma.loanActivity.findMany({
        where: whereClause,
        include: {
          rm_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          loan_client: {
            select: {
              id: true,
              customer_name: true,
              account_number: true,
            },
          },
          purpose: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
              created_at: true,
            },
            orderBy: {
              created_at: 'desc',
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.loanActivity.count({
        where: whereClause,
      }),
    ]);

    return {
      data: loanActivities.map((activity) =>
        this.transformToLoanActivityResponse(activity),
      ),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Retrieves a loan activity by ID with optimized query
   * @param id - The loan activity ID
   * @returns Promise<LoanActivityResponseDto> - The loan activity details
   */
  async getLoanActivityById(id: string): Promise<LoanActivityResponseDto> {
    const loanActivity = await this.prisma.loanActivity.findUnique({
      where: { id },
      include: {
        rm_user: {
          select: {
            id: true,
            name: true,
            email: true,
            rm_code: true,
          },
        },
        loan_client: {
          select: {
            id: true,
            customer_name: true,
            account_number: true,
          },
        },
        purpose: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        attachments: {
          select: {
            id: true,
            file_url: true,
            created_at: true,
          },
          orderBy: {
            created_at: 'desc',
          },
        },
      },
    });

    if (!loanActivity) {
      throw new NotFoundException(`Loan activity with ID '${id}' not found`);
    }

    return this.transformToLoanActivityResponse(loanActivity);
  }

  /**
   * Retrieves loan activities for a specific loan client with pagination
   * @param loanClientId - The loan client ID
   * @param page - Page number (default: 1)
   * @param limit - Number of items per page (default: 10)
   * @returns Promise<{ data: LoanActivityResponseDto[], total: number, page: number, limit: number }>
   */
  async getLoanActivitiesByClient(
    loanClientId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;

    // Validate that the loan client exists
    const loanClient = await this.prisma.loanClient.findUnique({
      where: { id: loanClientId },
      select: { id: true },
    });

    if (!loanClient) {
      throw new NotFoundException(
        `Loan client with ID '${loanClientId}' not found`,
      );
    }

    // Execute parallel queries for better performance
    const [loanActivities, total] = await Promise.all([
      this.prisma.loanActivity.findMany({
        where: { loan_client_id: loanClientId },
        include: {
          rm_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          purpose: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
              created_at: true,
            },
            orderBy: {
              created_at: 'desc',
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.loanActivity.count({
        where: { loan_client_id: loanClientId },
      }),
    ]);

    return {
      data: loanActivities.map((activity) =>
        this.transformToLoanActivityResponse(activity),
      ),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Retrieves loan activities for a specific RM user with pagination
   * @param rmUserId - The RM user ID
   * @param page - Page number (default: 1)
   * @param limit - Number of items per page (default: 10)
   * @returns Promise<{ data: LoanActivityResponseDto[], total: number, page: number, limit: number }>
   */
  async getLoanActivitiesByRmUser(
    rmUserId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;

    // Validate that the RM user exists
    const rmUser = await this.prisma.user.findUnique({
      where: { id: rmUserId },
      select: { id: true },
    });

    if (!rmUser) {
      throw new NotFoundException(`RM user with ID '${rmUserId}' not found`);
    }

    // Execute parallel queries for better performance
    const [loanActivities, total] = await Promise.all([
      this.prisma.loanActivity.findMany({
        where: { rm_user_id: rmUserId },
        include: {
          loan_client: {
            select: {
              id: true,
              customer_name: true,
              account_number: true,
            },
          },
          purpose: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
              created_at: true,
            },
            orderBy: {
              created_at: 'desc',
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.loanActivity.count({
        where: { rm_user_id: rmUserId },
      }),
    ]);

    return {
      data: loanActivities.map((activity) =>
        this.transformToLoanActivityResponse(activity),
      ),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Retrieves all follow-ups from loan activities with pagination
   * @param page - Page number (default: 1)
   * @param limit - Number of items per page (default: 10)
   * @param status - Filter by followup status (optional)
   * @returns Promise<{ data: FollowupResponseDto[], total: number, page: number, limit: number, totalPages: number }>
   */
  async getFollowups(
    page: number = 1,
    limit: number = 10,
    status?: string,
  ) {
    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      next_followup_date: {
        not: null,
      },
    };

    if (status) {
      whereClause.followup_status = status;
    }

    // Execute parallel queries for better performance
    const [followups, total] = await Promise.all([
      this.prisma.loanActivity.findMany({
        where: whereClause,
        include: {
          loan_client: {
            select: {
              id: true,
              customer_name: true,
              anchor: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          rm_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
        },
        orderBy: {
          next_followup_date: 'asc',
        },
        skip,
        take: limit,
      }),
      this.prisma.loanActivity.count({
        where: whereClause,
      }),
    ]);

    // Transform the data to match the required format
    const transformedFollowups = followups.map((activity) => ({
      id: activity.id,
      customer_id: activity.loan_client?.id || null,
      customer_name: activity.loan_client?.customer_name || 'Unknown Customer',
      anchor_name: activity.loan_client?.anchor?.name || 'No Anchor',
      assigned_officer: activity.rm_user?.name || 'Unknown Officer',
      assigned_officer_id: activity.rm_user?.id || activity.rm_user_id,
      followup_date: activity.next_followup_date?.toISOString() || null,
      status: activity.followup_status || 'pending',
      created_date: activity.created_at.toISOString(),
    }));

    return {
      data: transformedFollowups,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Updates the followup status of a loan activity
   * @param id - The loan activity ID
   * @param followupStatus - The new followup status
   * @returns Promise<LoanActivityResponseDto> - The updated loan activity
   */
  async updateFollowupStatus(
    id: string,
    followupStatus: string,
  ): Promise<LoanActivityResponseDto> {
    // Validate that the loan activity exists
    const existingActivity = await this.prisma.loanActivity.findUnique({
      where: { id },
      select: { id: true },
    });

    if (!existingActivity) {
      throw new NotFoundException(`Loan activity with ID '${id}' not found`);
    }

    // Validate followup status
    const validStatuses = ['pending', 'completed', 'cancelled'];
    if (!validStatuses.includes(followupStatus)) {
      throw new BadRequestException(
        `Invalid followup status. Must be one of: ${validStatuses.join(', ')}`,
      );
    }

    try {
      // Update the followup status
      const updatedActivity = await this.prisma.loanActivity.update({
        where: { id },
        data: {
          followup_status: followupStatus,
        },
        include: {
          rm_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          loan_client: {
            select: {
              id: true,
              customer_name: true,
              account_number: true,
            },
          },
          purpose: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
              created_at: true,
            },
            orderBy: {
              created_at: 'desc',
            },
          },
        },
      });

      return this.transformToLoanActivityResponse(updatedActivity);
    } catch (error) {
      throw new BadRequestException(`Failed to update followup status: ${error.message}`);
    }
  }
}
