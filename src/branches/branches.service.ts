import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { BranchResponseDto } from './dto/branch-response.dto';
import { BranchUsersResponseDto } from './dto/branch-users.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all branch-related business logic
 * Implements CRUD operations with optimized database queries
 */
@Injectable()
export class BranchesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new branch
   * @param createBranchDto - Data for creating the branch
   * @returns Promise<BranchResponseDto> - The created branch
   * @throws NotFoundException if region doesn't exist
   * @throws ConflictException if branch name already exists in the region
   */
  async create(createBranchDto: CreateBranchDto): Promise<BranchResponseDto> {
    // Check if region exists
    const region = await this.prisma.region.findUnique({
      where: { id: createBranchDto.region_id },
    });

    if (!region) {
      throw new NotFoundException(`Region with ID '${createBranchDto.region_id}' not found`);
    }

    // Check if branch with same name already exists in the region
    const existingBranch = await this.prisma.branch.findFirst({
      where: {
        name: {
          equals: createBranchDto.name,
          mode: 'insensitive', // Case-insensitive comparison
        },
        region_id: createBranchDto.region_id,
      },
    });

    if (existingBranch) {
      throw new ConflictException(`Branch with name '${createBranchDto.name}' already exists in this region`);
    }

    try {
      // Create the new branch
      const branch = await this.prisma.branch.create({
        data: createBranchDto,
        include: {
          region: true, // Include region details
        },
      });

      return {
        id: branch.id,
        name: branch.name,
        region_id: branch.region_id,
        region: {
          id: branch.region.id,
          name: branch.region.name,
        },
        created_at: branch.created_at.toISOString(),
      };
    } catch (error) {
      if (error instanceof ConflictException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to create branch');
    }
  }

  /**
   * Retrieves all branches with pagination and optional search
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<BranchResponseDto>> - Paginated list of branches
   */
  async findAll(paginationDto: PaginationDto): Promise<PaginatedResponseDto<BranchResponseDto>> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build where clause for search functionality
    const whereClause: Prisma.BranchWhereInput = search
      ? {
          OR: [
            {
              name: {
                contains: search,
                mode: 'insensitive', // Case-insensitive search
              },
            },
            {
              region: {
                name: {
                  contains: search,
                  mode: 'insensitive', // Search by region name too
                },
              },
            },
          ],
        }
      : {};

    // Execute both count and data queries in parallel for better performance
    const [branches, total] = await Promise.all([
      this.prisma.branch.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: {
          region: true, // Include region details
        },
        orderBy: [
          { region: { name: 'asc' } }, // Sort by region name first
          { name: 'asc' }, // Then by branch name
        ],
      }),
      this.prisma.branch.count({
        where: whereClause,
      }),
    ]);

    // Transform data to response DTOs
    const data = branches.map((branch) => ({
      id: branch.id,
      name: branch.name,
      region_id: branch.region_id,
      region: {
        id: branch.region.id,
        name: branch.region.name,
      },
      created_at: branch.created_at.toISOString(),
    }));
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves a single branch by ID
   * @param id - The UUID of the branch
   * @returns Promise<BranchResponseDto> - The branch data
   * @throws NotFoundException if branch doesn't exist
   */
  async findOne(id: string): Promise<BranchResponseDto> {
    const branch = await this.prisma.branch.findUnique({
      where: { id },
      include: {
        region: true, // Include region details
      },
    });

    if (!branch) {
      throw new NotFoundException(`Branch with ID '${id}' not found`);
    }

    return {
      id: branch.id,
      name: branch.name,
      region_id: branch.region_id,
      region: {
        id: branch.region.id,
        name: branch.region.name,
      },
      created_at: branch.created_at.toISOString(),
    };
  }

  /**
   * Updates an existing branch
   * @param id - The UUID of the branch to update
   * @param updateBranchDto - Data for updating the branch
   * @returns Promise<BranchResponseDto> - The updated branch
   * @throws NotFoundException if branch or region doesn't exist
   * @throws ConflictException if new name conflicts with existing branch in the region
   */
  async update(id: string, updateBranchDto: UpdateBranchDto): Promise<BranchResponseDto> {
    // Check if branch exists
    const existingBranch = await this.prisma.branch.findUnique({
      where: { id },
      include: { region: true },
    });

    if (!existingBranch) {
      throw new NotFoundException(`Branch with ID '${id}' not found`);
    }

    // If region is being updated, check if new region exists
    if (updateBranchDto.region_id && updateBranchDto.region_id !== existingBranch.region_id) {
      const newRegion = await this.prisma.region.findUnique({
        where: { id: updateBranchDto.region_id },
      });

      if (!newRegion) {
        throw new NotFoundException(`Region with ID '${updateBranchDto.region_id}' not found`);
      }
    }

    // Check for name conflicts in the target region
    if (updateBranchDto.name || updateBranchDto.region_id) {
      const targetRegionId = updateBranchDto.region_id || existingBranch.region_id;
      const targetName = updateBranchDto.name || existingBranch.name;

      // Only check for conflicts if name or region is actually changing
      if (targetName !== existingBranch.name || targetRegionId !== existingBranch.region_id) {
        const conflictingBranch = await this.prisma.branch.findFirst({
          where: {
            name: {
              equals: targetName,
              mode: 'insensitive',
            },
            region_id: targetRegionId,
            id: {
              not: id, // Exclude current branch from conflict check
            },
          },
        });

        if (conflictingBranch) {
          throw new ConflictException(`Branch with name '${targetName}' already exists in the target region`);
        }
      }
    }

    try {
      const updatedBranch = await this.prisma.branch.update({
        where: { id },
        data: updateBranchDto,
        include: {
          region: true,
        },
      });

      return {
        id: updatedBranch.id,
        name: updatedBranch.name,
        region_id: updatedBranch.region_id,
        region: {
          id: updatedBranch.region.id,
          name: updatedBranch.region.name,
        },
        created_at: updatedBranch.created_at.toISOString(),
      };
    } catch (error) {
      throw new BadRequestException('Failed to update branch');
    }
  }

  /**
   * Deletes a branch by ID
   * @param id - The UUID of the branch to delete
   * @returns Promise<void>
   * @throws NotFoundException if branch doesn't exist
   * @throws ConflictException if branch has associated users or leads
   */
  async remove(id: string): Promise<void> {
    // Check if branch exists and has associated records
    const branch = await this.prisma.branch.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            users: true,
            leads: true,
          },
        },
      },
    });

    if (!branch) {
      throw new NotFoundException(`Branch with ID '${id}' not found`);
    }

    // Prevent deletion if branch has associated users or leads
    const totalAssociations = branch._count.users + branch._count.leads;
    if (totalAssociations > 0) {
      const associations: string[] = [];
      if (branch._count.users > 0) associations.push(`${branch._count.users} user(s)`);
      if (branch._count.leads > 0) associations.push(`${branch._count.leads} lead(s)`);

      throw new ConflictException(
        `Cannot delete branch '${branch.name}' because it has ${associations.join(' and ')}. Please reassign or remove these records first.`
      );
    }

    try {
      await this.prisma.branch.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException('Failed to delete branch');
    }
  }

  /**
   * Retrieves all users in a specific branch
   * @param id - The UUID of the branch
   * @returns Promise<BranchUsersResponseDto> - The branch with its users
   * @throws NotFoundException if branch doesn't exist
   */
  async getUsersByBranch(id: string): Promise<BranchUsersResponseDto> {
    const branch = await this.prisma.branch.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            name: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
      },
    });

    if (!branch) {
      throw new NotFoundException(`Branch with ID '${id}' not found`);
    }

    return {
      branch_id: branch.id,
      branch_name: branch.name,
      users: branch.users,
      total_users: branch.users.length,
    };
  }
}