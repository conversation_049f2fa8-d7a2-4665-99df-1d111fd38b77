import { ApiProperty } from '@nestjs/swagger';

export class BranchUserDto {
  @ApiProperty({
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    description: 'Unique identifier for the user'
  })
  id: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the user'
  })
  name: string;
}

export class BranchUsersResponseDto {
  @ApiProperty({
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    description: 'Unique identifier for the branch'
  })
  branch_id: string;

  @ApiProperty({
    example: 'Nairobi Main Branch',
    description: 'Name of the branch'
  })
  branch_name: string;

  @ApiProperty({
    type: [BranchUserDto],
    description: 'List of users in this branch'
  })
  users: BranchUserDto[];

  @ApiProperty({
    example: 15,
    description: 'Total number of users in this branch'
  })
  total_users: number;
}
