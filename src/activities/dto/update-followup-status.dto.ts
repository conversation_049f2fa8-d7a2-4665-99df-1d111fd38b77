import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class UpdateFollowupStatusDto {
  @ApiProperty({
    description: 'New status for the follow-up',
    enum: ['pending', 'completed', 'cancelled'],
    example: 'completed',
  })
  @IsNotEmpty({ message: 'Status is required' })
  @IsEnum(['pending', 'completed', 'cancelled'], {
    message: 'Status must be one of: pending, completed, cancelled',
  })
  status: 'pending' | 'completed' | 'cancelled';
}

export class UpdateFollowupStatusResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the updated follow-up',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Updated status of the follow-up',
    enum: ['pending', 'completed', 'cancelled'],
    example: 'completed',
  })
  status: string;

  @ApiProperty({
    description: 'Date and time when the status was updated',
    example: '2025-08-05T10:30:00.000Z',
  })
  updated_at: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Follow-up status updated successfully',
  })
  message: string;
}
