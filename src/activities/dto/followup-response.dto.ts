import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class FollowupResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the activity/follow-up',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Lead ID associated with this follow-up',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  lead_id: string | null;

  @ApiProperty({
    description: 'Name of the lead/customer',
    example: '<PERSON>',
  })
  lead_name: string;

  @ApiProperty({
    description: 'Name of the anchor associated with the lead',
    example: 'ABC Company Ltd',
  })
  anchor_name: string;

  @ApiProperty({
    description: 'Assigned officer information',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
      name: { type: 'string', example: '<PERSON>' },
      rm_code: { type: 'string', example: 'RM001' },
    },
  })
  assigned_officer: {
    id: string;
    name: string;
    rm_code: string;
  };

  @ApiProperty({
    description: 'Type of follow-up (call or visit)',
    enum: ['call', 'visit'],
    example: 'call',
  })
  followup_type: string;

  @ApiProperty({
    description: 'Reason for the follow-up (activity type)',
    example: 'Follow up',
  })
  followup_reason: string;

  @ApiProperty({
    description: 'Scheduled follow-up date and time',
    example: '2025-08-15T10:30:00.000Z',
  })
  followup_date: string;

  @ApiProperty({
    description: 'Current status of the follow-up',
    enum: ['pending', 'completed', 'cancelled'],
    example: 'pending',
  })
  status: string;

  @ApiProperty({
    description: 'Date and time when the follow-up was created',
    example: '2025-08-05T10:30:00.000Z',
  })
  created_date: string;

  @ApiPropertyOptional({
    description: 'Additional notes about the follow-up',
    example: 'Customer requested follow-up call next week',
  })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Purpose of the follow-up activity',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
      name: { type: 'string', example: 'Loan Application Follow-up' },
    },
  })
  purpose?: {
    id: string;
    name: string;
  };
}

export class FollowupsListResponseDto {
  @ApiProperty({
    description: 'Array of follow-ups',
    type: [FollowupResponseDto],
  })
  data: FollowupResponseDto[];

  @ApiProperty({
    description: 'Total number of follow-ups',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Summary statistics',
    type: 'object',
    properties: {
      pending: { type: 'number', example: 15 },
      completed: { type: 'number', example: 8 },
      cancelled: { type: 'number', example: 2 },
      call_followups: { type: 'number', example: 12 },
      visit_followups: { type: 'number', example: 13 },
    },
  })
  summary: {
    pending: number;
    completed: number;
    cancelled: number;
    call_followups: number;
    visit_followups: number;
  };
}
