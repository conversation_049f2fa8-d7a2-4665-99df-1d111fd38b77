import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UseInterceptors,
  UploadedFiles,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Patch,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ActivitiesService } from './activities.service';
import { CreateCallActivityDto } from './dto/create-call-activity.dto';
import { CreateCallActivityFormDto } from './dto/create-call-activity-form.dto';
import { CreateVisitActivityDto } from './dto/create-visit-activity.dto';
import { CreateVisitActivityFormDto } from './dto/create-visit-activity-form.dto';
import { CallActivityResponseDto } from './dto/call-activity-response.dto';
import { VisitActivityResponseDto } from './dto/visit-activity-response.dto';
import { InteractionHistoryResponseDto } from './dto/interaction-history-response.dto';
import { FollowupsListResponseDto } from './dto/followup-response.dto';
import { UpdateFollowupStatusDto, UpdateFollowupStatusResponseDto } from './dto/update-followup-status.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

/**
 * Controller handling all activity-related HTTP endpoints
 * Provides RESTful API for activity management
 */
@ApiTags('Activities')
@Controller('activities')
@UseGuards(JwtAuthGuard)
export class ActivitiesController {
  constructor(private readonly activitiesService: ActivitiesService) {}

  /**
   * Creates a new call activity
   * POST /call-activities
   */
  @Post('call-activities')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Call activity data with file attachments',
    schema: {
      type: 'object',
      properties: {
        call_type: {
          type: 'string',
          enum: ['First Contact', 'Follow up'],
          example: 'First Contact',
        },
        call_status: {
          type: 'string',
          enum: ['Success', 'Declined'],
          example: 'Success',
        },
        notes: {
          type: 'string',
          example: 'Customer showed interest in our loan products',
        },
        follow_up_date: {
          type: 'string',
          format: 'date-time',
          example: '2025-08-15T10:30:00.000Z',
        },
        purpose_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        leadID: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        performed_by_user_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'File attachments',
        },
      },
      required: ['call_type', 'call_status', 'purpose_id', 'leadID'],
    },
  })
  @ApiOperation({
    summary: 'Create a new call activity',
    description:
      'Creates a new call activity with the specified details. Automatically sets the activity type to "call". Supports file attachments via FormData and validates all required relationships. If lead_id or performed_by_user_id are not provided, default values will be used.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Call activity created successfully',
    type: CallActivityResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description: 'Lead, purpose, or user not found',
  })
  async createCallActivity(
    @Request() req: any,
    @Body(ValidationPipe) formData: CreateCallActivityFormDto,
    @UploadedFiles() files: Express.Multer.File[],
  ): Promise<CallActivityResponseDto> {
    // Convert FormData to the expected DTO format
    const createCallActivityDto: CreateCallActivityDto = {
      ...formData,
      performed_by_user_id: req.user.id, // Set to current logged-in user
      attachments:
        files?.map((file) => ({
          name: file.originalname,
          size: file.size,
          type: file.mimetype,
          file: file,
        })) || [],
    };

    return this.activitiesService.createCallActivity(createCallActivityDto);
  }

  /**
   * Creates a new visit activity
   * POST /visit-activities
   */
  @Post('visit-activities')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Visit activity data with file attachments',
    schema: {
      type: 'object',
      properties: {
        visit_type: {
          type: 'string',
          enum: ['First Visit', 'Follow up'],
          example: 'First Visit',
        },
        visit_status: {
          type: 'string',
          enum: ['Successful', 'Declined'],
          example: 'Successful',
        },
        notes: {
          type: 'string',
          example: 'Customer showed interest in our loan products',
        },
        follow_up_date: {
          type: 'string',
          format: 'date-time',
          example: '2025-08-15T10:30:00.000Z',
        },
        purpose_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        leadID: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        performed_by_user_id: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'File attachments',
        },
      },
      required: ['visit_type', 'visit_status', 'purpose_id', 'leadID'],
    },
  })
  @ApiOperation({
    summary: 'Create a new visit activity',
    description:
      'Creates a new visit activity with the specified details. Automatically sets the interaction_type to "visit" and stores visit_type in activity_type field. Supports file attachments via FormData and validates all required relationships. If lead_id or performed_by_user_id are not provided, default values will be used.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Visit activity created successfully',
    type: VisitActivityResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description: 'Lead, purpose, or user not found',
  })
  async createVisitActivity(
    @Request() req: any,
    @Body(ValidationPipe) formData: CreateVisitActivityFormDto,
    @UploadedFiles() files: Express.Multer.File[],
  ): Promise<VisitActivityResponseDto> {
    // Convert FormData to the expected DTO format
    const createVisitActivityDto: CreateVisitActivityDto = {
      ...formData,
      performed_by_user_id: req.user.id, // Set to current logged-in user
      attachments:
        files?.map((file) => ({
          name: file.originalname,
          size: file.size,
          type: file.mimetype,
          file: file,
        })) || [],
    };

    return this.activitiesService.createVisitActivity(createVisitActivityDto);
  }

  /**
   * Gets interaction history for a specific lead
   * GET /leads/:id/interaction-history
   */
  @Get('leads/:id/interaction-history')
  @ApiOperation({
    summary: 'Get interaction history for a lead',
    description:
      'Retrieves all interaction history (calls, visits, etc.) for a specific lead, ordered by most recent first.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Interaction history retrieved successfully',
    type: [InteractionHistoryResponseDto],
  })
  @ApiNotFoundResponse({
    description: 'Lead not found',
  })
  async getLeadInteractionHistory(
    @Param('id', ParseUUIDPipe) leadId: string,
  ): Promise<InteractionHistoryResponseDto[]> {
    return this.activitiesService.getLeadInteractionHistory(leadId);
  }

  /**
   * Gets all activities filtered by interaction type
   * GET /activities/by-interaction-type?type=call or GET /activities/by-interaction-type?type=visit
   */
  @Get('activities-by-interaction-type')
  @ApiOperation({
    summary: 'Get all activities by interaction type',
    description:
      'Retrieves all activities filtered by interaction_type. Use "call" to get only call activities or "visit" to get only visit activities. Returns activities ordered by most recent first.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/InteractionHistoryResponseDto' },
        },
        total: { type: 'number', example: 150 },
        interaction_type: { type: 'string', example: 'call' },
        message: { type: 'string', example: 'Retrieved 150 call activities successfully' },
      },
    },
  })
  @ApiQuery({
    name: 'type',
    required: true,
    type: String,
    description: 'Interaction type to filter by',
    example: 'call',
    enum: ['call', 'visit'],
  })
  @ApiBadRequestResponse({
    description: 'Invalid interaction type. Must be "call" or "visit"'
  })
  async getActivitiesByInteractionType(
    @Query('type') interactionType: string,
  ) {
    return this.activitiesService.getActivitiesByInteractionType(interactionType);
  }

  /**
   * Gets all follow-ups with comprehensive information
   * GET /followups
   */
  @Get('followups')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all follow-ups',
    description:
      'Retrieves all scheduled follow-ups with comprehensive information including lead details, anchor information, assigned officer, follow-up type, reason, date, status, and creation date. Follow-ups are activities that have a next_followup_date set. Results are sorted by creation time with most recently created follow-ups appearing first.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Follow-ups retrieved successfully',
    type: FollowupsListResponseDto,
  })
  async getAllFollowups(): Promise<FollowupsListResponseDto> {
    return this.activitiesService.getAllFollowups();
  }

  /**
   * Updates the status of a follow-up
   * PATCH /followups/:id/status
   */
  @Post('followups/:id/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update follow-up status',
    description:
      'Updates the status of a specific follow-up. Status can be changed to pending, completed, or cancelled. This allows frontend to manage follow-up workflow.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the follow-up (activity) to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Follow-up status updated successfully',
    type: UpdateFollowupStatusResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid status or follow-up not found',
  })
  @ApiNotFoundResponse({
    description: 'Follow-up not found',
  })
  async updateFollowupStatus(
    @Param('id', ParseUUIDPipe) followupId: string,
    @Body(ValidationPipe) updateDto: UpdateFollowupStatusDto,
  ): Promise<UpdateFollowupStatusResponseDto> {
    return this.activitiesService.updateFollowupStatus(followupId, updateDto);
  }
}
