import { Module } from '@nestjs/common';
import { SegmentHeadDashboardController } from './segment-head-dashboard.controller';
import { SegmentHeadDashboardService } from './segment-head-dashboard.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [SegmentHeadDashboardController],
  providers: [SegmentHeadDashboardService],
  exports: [SegmentHeadDashboardService],
})
export class SegmentHeadDashboardModule {}
