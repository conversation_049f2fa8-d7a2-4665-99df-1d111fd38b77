import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SegmentHeadDashboardService } from './segment-head-dashboard.service';
import { ActivityStatusResponseDto } from './dto/activity-status.dto';
import { ActivitySummaryResponseDto } from './dto/activity-summary.dto';
import { ActivitiesByFeedbackResponseDto } from './dto/activities-by-feedback.dto';

@ApiTags('Segment Head Dashboard')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('segment-head-dashboard')
export class SegmentHeadDashboardController {
  constructor(
    private readonly segmentHeadDashboardService: SegmentHeadDashboardService,
  ) {}

  @Get('activity-summary')
  @ApiOperation({
    summary: 'Get summary of overdue and upcoming activities',
    description: 'Retrieves the total count of overdue and upcoming activities without pagination.',
  })
  @ApiResponse({
    status: 200,
    description: 'Activity summary retrieved successfully',
    type: ActivitySummaryResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getActivitySummary(): Promise<ActivitySummaryResponseDto> {
    const summary = await this.segmentHeadDashboardService.getActivitySummary();
    return {
      summary,
      retrieved_at: new Date().toISOString(),
    };
  }

  @Get('activity-status')
  @ApiOperation({
    summary: 'Get upcoming and overdue activities',
    description: 'Retrieves activities that are upcoming (due in less than 3 days) or overdue (past due date with no subsequent activity on the same lead). Supports filtering by status, interaction type, region, branch, and staff name.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by activity status',
    enum: ['upcoming', 'overdue'],
    example: 'upcoming',
  })
  @ApiQuery({
    name: 'interaction_type',
    required: false,
    type: String,
    description: 'Filter by interaction type',
    enum: ['call', 'visit'],
    example: 'call',
  })
  @ApiQuery({
    name: 'region',
    required: false,
    type: String,
    description: 'Filter by region name (case-insensitive partial match)',
    example: 'Central',
  })
  @ApiQuery({
    name: 'branch',
    required: false,
    type: String,
    description: 'Filter by branch name (case-insensitive partial match)',
    example: 'Nairobi',
  })
  @ApiQuery({
    name: 'staff_name',
    required: false,
    type: String,
    description: 'Filter by staff name (case-insensitive partial match)',
    example: 'John',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 50, max: 100)',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: 'Activity status data retrieved successfully',
    type: ActivityStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getActivityStatus(
    @Query('status') status?: string,
    @Query('interaction_type') interactionType?: string,
    @Query('region') region?: string,
    @Query('branch') branch?: string,
    @Query('staff_name') staffName?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<ActivityStatusResponseDto> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? Math.min(parseInt(limit, 10), 100) : 50;

    return this.segmentHeadDashboardService.getUpcomingAndOverdueActivities(
      status,
      interactionType,
      region,
      branch,
      staffName,
      pageNumber,
      limitNumber,
    );
  }

  @Get('activities-by-feedback')
  @ApiOperation({
    summary: 'Get activities grouped by customer feedback',
    description: 'Retrieves the number of activities grouped by customer feedback category',
  })
  @ApiResponse({
    status: 200,
    description: 'Activities by feedback data retrieved successfully',
    type: ActivitiesByFeedbackResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async getActivitiesByFeedback(): Promise<ActivitiesByFeedbackResponseDto> {
    const data = await this.segmentHeadDashboardService.getActivitiesByCustomerFeedback();
    return {
      activities_by_feedback: data.activities_by_feedback,
      total_activities: data.total_activities,
      retrieved_at: new Date().toISOString(),
    };
  }
}
