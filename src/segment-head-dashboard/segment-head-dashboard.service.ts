import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ActivityStatusResponseDto } from './dto/activity-status.dto';
import { ActivitySummaryDto } from './dto/activity-summary.dto';
import { ActivityByFeedbackDto } from './dto/activities-by-feedback.dto';

@Injectable()
export class SegmentHeadDashboardService {
  constructor(private prisma: PrismaService) {}

  async getActivitySummary(): Promise<ActivitySummaryDto> {
    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    // Get all activities with next_followup_date
    const activities = await this.prisma.activity.findMany({
      where: {
        next_followup_date: {
          not: null,
        },
        lead: {
          isNot: null,
        },
      },
      include: {
        lead: true,
      },
    });

    let overdueCount = 0;
    let upcomingCount = 0;

    for (const activity of activities) {
      if (!activity.next_followup_date) continue;

      const isUpcoming = activity.next_followup_date >= now && activity.next_followup_date <= threeDaysFromNow;
      const isOverdue = activity.next_followup_date < now;

      if (isOverdue) {
        // Check if there's a newer activity on the same lead after the next_followup_date
        const newerActivity = await this.prisma.activity.findFirst({
          where: {
            lead_id: activity.lead_id,
            created_at: {
              gt: activity.next_followup_date,
            },
          },
        });

        // Only count if no newer activity was found (meaning it's truly overdue)
        if (!newerActivity) {
          overdueCount++;
        }
      } else if (isUpcoming) {
        upcomingCount++;
      }
    }

    return {
      overdue_count: overdueCount,
      upcoming_count: upcomingCount,
    };
  }

  async getUpcomingAndOverdueActivities(
    status?: string,
    interactionType?: string,
    region?: string,
    branch?: string,
    staffName?: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<ActivityStatusResponseDto> {
    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    // Build where conditions for filtering
    const whereConditions: any = {
      next_followup_date: {
        not: null,
      },
      lead: {
        isNot: null,
      },
    };

    // Add status filter (upcoming or overdue)
    if (status === 'upcoming') {
      whereConditions.next_followup_date = {
        ...whereConditions.next_followup_date,
        gte: now,
        lte: threeDaysFromNow,
      };
    } else if (status === 'overdue') {
      whereConditions.next_followup_date = {
        ...whereConditions.next_followup_date,
        lt: now,
      };
    }

    // Add interaction type filter
    if (interactionType) {
      whereConditions.interaction_type = interactionType;
    }

    // Add region filter
    if (region) {
      whereConditions.performed_by = {
        branch: {
          region: {
            name: {
              contains: region,
              mode: 'insensitive',
            },
          },
        },
      };
    }

    // Add branch filter
    if (branch) {
      whereConditions.performed_by = {
        ...whereConditions.performed_by,
        branch: {
          ...whereConditions.performed_by?.branch,
          name: {
            contains: branch,
            mode: 'insensitive',
          },
        },
      };
    }

    // Add staff name filter
    if (staffName) {
      whereConditions.performed_by = {
        ...whereConditions.performed_by,
        name: {
          contains: staffName,
          mode: 'insensitive',
        },
      };
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get activities with all necessary relationships
    const [activities, totalCount] = await Promise.all([
      this.prisma.activity.findMany({
        where: whereConditions,
        include: {
          lead: true,
          performed_by: {
            include: {
              branch: {
                include: {
                  region: true,
                },
              },
            },
          },
        },
        orderBy: {
          next_followup_date: 'asc',
        },
        skip: offset,
        take: limit,
      }),
      this.prisma.activity.count({
        where: whereConditions,
      }),
    ]);

    // Filter activities based on status logic
    const filteredActivities = await this.filterActivitiesByStatus(activities, status);

    // Transform the data to match the DTO
    const transformedActivities = filteredActivities.map((activity) => {
      const isUpcoming = activity.next_followup_date && activity.next_followup_date >= now && activity.next_followup_date <= threeDaysFromNow;
      const activityStatus = isUpcoming ? 'upcoming' : 'overdue';

      return {
        id: activity.id,
        customer_name: activity.lead?.customer_name || 'Unknown Customer',
        staff_name: activity.performed_by?.name || 'Unknown Staff',
        region: activity.performed_by?.branch?.region?.name || 'Unknown Region',
        branch: activity.performed_by?.branch?.name || 'Unknown Branch',
        interaction_type: activity.interaction_type || 'unknown',
        status: activityStatus,
        contact_info: activity.performed_by?.phone_number || 'No contact info',
        next_followup_date: activity.next_followup_date?.toISOString() || '',
        created_at: activity.created_at.toISOString(),
        notes: activity.notes,
      };
    });

    const totalPages = Math.ceil(totalCount / limit);

    return {
      activities: transformedActivities,
      total: totalCount,
      page,
      limit,
      total_pages: totalPages,
    };
  }

  private async filterActivitiesByStatus(activities: any[], status?: string): Promise<any[]> {
    if (!status) {
      return activities;
    }

    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    const filteredActivities: any[] = [];

    for (const activity of activities) {
      if (!activity.next_followup_date) continue;

      const isUpcoming = activity.next_followup_date >= now && activity.next_followup_date <= threeDaysFromNow;
      const isOverdue = activity.next_followup_date < now;

      if (isOverdue && status === 'overdue') {
        // Check if there's a newer activity on the same lead after the next_followup_date
        const newerActivity = await this.prisma.activity.findFirst({
          where: {
            lead_id: activity.lead_id,
            created_at: {
              gt: activity.next_followup_date,
            },
          },
          orderBy: {
            created_at: 'desc',
          },
        });

        // Only include if no newer activity was found (meaning it's truly overdue)
        if (!newerActivity) {
          filteredActivities.push(activity);
        }
      } else if (isUpcoming && status === 'upcoming') {
        filteredActivities.push(activity);
      } else if (!status) {
        // If no status filter, include both upcoming and overdue
        if (isUpcoming || (isOverdue && !(await this.hasNewerActivity(activity)))) {
          filteredActivities.push(activity);
        }
      }
    }

    return filteredActivities;
  }

  private async hasNewerActivity(activity: any): Promise<boolean> {
    const newerActivity = await this.prisma.activity.findFirst({
      where: {
        lead_id: activity.lead_id,
        created_at: {
          gt: activity.next_followup_date,
        },
      },
    });

    return !!newerActivity;
  }

  async getActivitiesByCustomerFeedback(): Promise<{
    activities_by_feedback: ActivityByFeedbackDto[];
    total_activities: number;
  }> {
    // Get all activities with customer feedback
    const activities = await this.prisma.activity.findMany({
      where: {
        customer_feedback_id: {
          not: null,
        },
      },
      include: {
        customer_feedback: true,
      },
    });

    // Group activities by customer feedback
    const feedbackMap = new Map<string, { name: string; count: number }>();
    
    for (const activity of activities) {
      if (activity.customer_feedback_id) {
        const current = feedbackMap.get(activity.customer_feedback_id);
        if (current) {
          feedbackMap.set(activity.customer_feedback_id, {
            name: activity.customer_feedback?.name || 'Unknown',
            count: current.count + 1,
          });
        } else {
          feedbackMap.set(activity.customer_feedback_id, {
            name: activity.customer_feedback?.name || 'Unknown',
            count: 1,
          });
        }
      }
    }

    // Transform the data to match the DTO
    const activitiesByFeedbackDto: ActivityByFeedbackDto[] = Array.from(feedbackMap.entries()).map(
      ([id, { name, count }]) => ({
        customer_feedback_id: id,
        customer_feedback_name: name,
        activity_count: count,
      }),
    );

    // Calculate total activities
    const totalActivities = activitiesByFeedbackDto.reduce((sum, item) => sum + item.activity_count, 0);

    return {
      activities_by_feedback: activitiesByFeedbackDto,
      total_activities: totalActivities,
    };
  }
}
