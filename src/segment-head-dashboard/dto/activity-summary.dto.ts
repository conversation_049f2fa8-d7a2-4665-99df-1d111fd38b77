import { ApiProperty } from '@nestjs/swagger';

export class ActivitySummaryDto {
  @ApiProperty({
    example: 25,
    description: 'Total number of overdue activities'
  })
  overdue_count: number;

  @ApiProperty({
    example: 42,
    description: 'Total number of upcoming activities'
  })
  upcoming_count: number;
}

export class ActivitySummaryResponseDto {
  @ApiProperty({
    type: ActivitySummaryDto,
    description: 'Summary of overdue and upcoming activities'
  })
  summary: ActivitySummaryDto;

  @ApiProperty({
    example: '2025-08-20T08:00:00.000Z',
    description: 'Timestamp when the data was retrieved'
  })
  retrieved_at: string;
}