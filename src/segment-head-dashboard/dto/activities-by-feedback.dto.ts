import { ApiProperty } from '@nestjs/swagger';

export class ActivityByFeedbackDto {
  @ApiProperty({
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    description: 'Unique identifier for the customer feedback category'
  })
  customer_feedback_id: string;

  @ApiProperty({
    example: 'Positive',
    description: 'Name of the customer feedback category'
  })
  customer_feedback_name: string;

  @ApiProperty({
    example: 25,
    description: 'Number of activities in this feedback category'
  })
  activity_count: number;
}

export class ActivitiesByFeedbackResponseDto {
  @ApiProperty({
    type: [ActivityByFeedbackDto],
    description: 'List of activities grouped by customer feedback category'
  })
  activities_by_feedback: ActivityByFeedbackDto[];

  @ApiProperty({
    example: 150,
    description: 'Total number of activities across all feedback categories'
  })
  total_activities: number;

  @ApiProperty({
    example: '2025-08-20T08:00:00.000Z',
    description: 'Timestamp when the data was retrieved'
  })
  retrieved_at: string;
}