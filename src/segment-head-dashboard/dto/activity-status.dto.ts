import { ApiProperty } from '@nestjs/swagger';

export class ActivityStatusDto {
  @ApiProperty({
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    description: 'Unique identifier for the activity'
  })
  id: string;

  @ApiProperty({
    example: 'ABC Manufacturing Ltd',
    description: 'Name of the customer/lead'
  })
  customer_name: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'Name of the staff member who performed the activity'
  })
  staff_name: string;

  @ApiProperty({
    example: 'Central Region',
    description: 'Region where the staff member is located'
  })
  region: string;

  @ApiProperty({
    example: 'Nairobi Main Branch',
    description: 'Branch where the staff member is located'
  })
  branch: string;

  @ApiProperty({
    example: 'call',
    description: 'Type of interaction (call or visit)',
    enum: ['call', 'visit']
  })
  interaction_type: string;

  @ApiProperty({
    example: 'upcoming',
    description: 'Status of the activity',
    enum: ['upcoming', 'overdue']
  })
  status: string;

  @ApiProperty({
    example: '+254712345678',
    description: 'Phone number of the staff member'
  })
  contact_info: string;

  @ApiProperty({
    example: '2025-08-22T10:00:00.000Z',
    description: 'Next followup date for the activity'
  })
  next_followup_date: string;

  @ApiProperty({
    example: '2025-08-19T14:30:00.000Z',
    description: 'Date when the activity was created'
  })
  created_at: string;

  @ApiProperty({
    example: 'Follow up on loan application',
    description: 'Notes or comments about the activity'
  })
  notes?: string;
}

export class ActivityStatusResponseDto {
  @ApiProperty({
    type: [ActivityStatusDto],
    description: 'List of upcoming and overdue activities'
  })
  activities: ActivityStatusDto[];

  @ApiProperty({
    example: 150,
    description: 'Total number of activities matching the criteria'
  })
  total: number;

  @ApiProperty({
    example: 1,
    description: 'Current page number'
  })
  page: number;

  @ApiProperty({
    example: 50,
    description: 'Number of items per page'
  })
  limit: number;

  @ApiProperty({
    example: 3,
    description: 'Total number of pages'
  })
  total_pages: number;
}
