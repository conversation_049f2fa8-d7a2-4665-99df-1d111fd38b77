import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateHolidayDto } from './dto/create-holiday.dto';
import { UpdateHolidayDto } from './dto/update-holiday.dto';
import { HolidayResponseDto } from './dto/holiday-response.dto';
import { isApplicable } from '../common/utils/date.utils';

@Injectable()
export class HolidaysService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new holiday
   * @param createHolidayDto - Holiday data
   * @param userId - ID of the user creating the holiday
   * @returns Promise<HolidayResponseDto> - Created holiday
   */
  async create(createHolidayDto: CreateHolidayDto, userId: string): Promise<HolidayResponseDto> {
    try {
      const holidayDate = new Date(createHolidayDto.date);
      
      // Normalize the date to start of day for comparison
      const normalizedDate = new Date(holidayDate.getFullYear(), holidayDate.getMonth(), holidayDate.getDate());

      // Check if a holiday with the same date already exists
      const existingHoliday = await this.prisma.holiday.findFirst({
        where: {
          date: {
            gte: normalizedDate,
            lt: new Date(normalizedDate.getTime() + 24 * 60 * 60 * 1000), // Next day
          },
        },
      });

      if (existingHoliday) {
        throw new ConflictException('A holiday with the same date already exists');
      }

      // Create the holiday
      const holiday = await this.prisma.holiday.create({
        data: {
          name: createHolidayDto.name,
          date: normalizedDate,
          added_by: userId,
        },
        include: {
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      // Check for TwoByTwoPhase records with matching expected_completion_date
      await this.updateTwoByTwoPhaseRecords(normalizedDate);

      return this.transformToResponseDto(holiday);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      console.error('Error creating holiday:', error);
      throw new BadRequestException('Failed to create holiday');
    }
  }

  /**
   * Get all holidays
   * @returns Promise<HolidayResponseDto[]> - Array of holidays
   */
  async findAll(): Promise<HolidayResponseDto[]> {
    try {
      const holidays = await this.prisma.holiday.findMany({
        include: {
          user: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          date: 'asc',
        },
      });

      const today = new Date();
      const todayNormalized = new Date(today.getFullYear(), today.getMonth(), today.getDate());

      return holidays.map(holiday => {
        const holidayDate = new Date(holiday.date);
        const holidayNormalized = new Date(holidayDate.getFullYear(), holidayDate.getMonth(), holidayDate.getDate());
        
        return {
          ...this.transformToResponseDto(holiday),
          is_upcoming: holidayNormalized >= todayNormalized,
        };
      });
    } catch (error) {
      console.error('Error fetching holidays:', error);
      throw new BadRequestException('Failed to fetch holidays');
    }
  }

  /**
   * Update a holiday
   * @param id - Holiday ID
   * @param updateHolidayDto - Update data
   * @returns Promise<HolidayResponseDto> - Updated holiday
   */
  async update(id: string, updateHolidayDto: UpdateHolidayDto): Promise<HolidayResponseDto> {
    try {
      // Check if holiday exists
      const existingHoliday = await this.prisma.holiday.findUnique({
        where: { id },
      });

      if (!existingHoliday) {
        throw new NotFoundException(`Holiday with ID '${id}' not found`);
      }

      const updateData: any = {};
      let newDate: Date | null = null;

      if (updateHolidayDto.name !== undefined) {
        updateData.name = updateHolidayDto.name;
      }

      if (updateHolidayDto.date !== undefined) {
        newDate = new Date(updateHolidayDto.date);
        const normalizedDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
        
        // Check if another holiday with the same date exists (excluding current holiday)
        const existingHolidayWithDate = await this.prisma.holiday.findFirst({
          where: {
            id: { not: id },
            date: {
              gte: normalizedDate,
              lt: new Date(normalizedDate.getTime() + 24 * 60 * 60 * 1000), // Next day
            },
          },
        });

        if (existingHolidayWithDate) {
          throw new ConflictException('A holiday with the same date already exists');
        }

        updateData.date = normalizedDate;
      }

      // Update the holiday
      const updatedHoliday = await this.prisma.holiday.update({
        where: { id },
        data: updateData,
        include: {
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      // If date was changed, update TwoByTwoPhase records
      if (newDate) {
        const normalizedDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
        await this.updateTwoByTwoPhaseRecords(normalizedDate);
      }

      return this.transformToResponseDto(updatedHoliday);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      console.error('Error updating holiday:', error);
      throw new BadRequestException('Failed to update holiday');
    }
  }

  /**
   * Delete a holiday
   * @param id - Holiday ID
   */
  async remove(id: string): Promise<void> {
    try {
      const holiday = await this.prisma.holiday.findUnique({
        where: { id },
      });

      if (!holiday) {
        throw new NotFoundException(`Holiday with ID '${id}' not found`);
      }

      await this.prisma.holiday.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting holiday:', error);
      throw new BadRequestException('Failed to delete holiday');
    }
  }

  /**
   * Update TwoByTwoPhase records that have expected_completion_date matching the holiday date
   * @param holidayDate - The holiday date
   */
  private async updateTwoByTwoPhaseRecords(holidayDate: Date): Promise<void> {
    try {
      // Find TwoByTwoPhase records with expected_completion_date matching the holiday date
      const matchingRecords = await this.prisma.twoByTwoPhase.findMany({
        where: {
          expected_completion_date: {
            gte: holidayDate,
            lt: new Date(holidayDate.getTime() + 24 * 60 * 60 * 1000), // Next day
          },
        },
      });

      for (const record of matchingRecords) {
        if (!record.expected_completion_date) continue;

        // Find the next applicable workday
        const nextWorkday = await this.findNextApplicableWorkday(record.expected_completion_date);

        // Update the record with the next workday
        await this.prisma.twoByTwoPhase.update({
          where: { id: record.id },
          data: {
            expected_completion_date: nextWorkday,
            is_verified_workday: true,
          },
        });

        console.log(
          `Updated TwoByTwoPhase ${record.id} expected_completion_date from ${record.expected_completion_date} to ${nextWorkday} due to holiday`,
        );
      }
    } catch (error) {
      console.error('Error updating TwoByTwoPhase records:', error);
      // Don't throw error here to avoid breaking holiday creation/update
    }
  }

  /**
   * Find the next applicable workday starting from a given date
   * @param startDate - The date to start searching from
   * @returns Promise<Date> - The next applicable workday
   */
  private async findNextApplicableWorkday(startDate: Date): Promise<Date> {
    let currentDate = new Date(startDate);
    currentDate.setDate(currentDate.getDate() + 1); // Start from the next day

    // Limit search to avoid infinite loops (max 30 days)
    let attempts = 0;
    const maxAttempts = 30;

    while (attempts < maxAttempts) {
      const isApplicableDate = await isApplicable(currentDate, this.prisma);

      if (isApplicableDate) {
        return currentDate;
      }

      currentDate.setDate(currentDate.getDate() + 1);
      attempts++;
    }

    // If no applicable day found in 30 days, return the original date + 1
    // This is a fallback to prevent infinite loops
    console.warn(
      `Could not find applicable workday within 30 days of ${startDate}, using fallback`,
    );
    const fallbackDate = new Date(startDate);
    fallbackDate.setDate(fallbackDate.getDate() + 1);
    return fallbackDate;
  }

  /**
   * Transform holiday data to response DTO
   * @param holiday - Holiday data from database
   * @returns HolidayResponseDto
   */
  private transformToResponseDto(holiday: any): HolidayResponseDto {
    return {
      id: holiday.id,
      name: holiday.name,
      date: holiday.date,
      user: holiday.user.name,
      created_at: holiday.created_at,
    };
  }
}
