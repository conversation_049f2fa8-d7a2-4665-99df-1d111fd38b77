import { IsOptional, IsString, IsDateString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating a holiday
 */
export class UpdateHolidayDto {
  @ApiPropertyOptional({
    description: 'Name of the holiday',
    example: 'Christmas Day',
  })
  @IsOptional()
  @IsString({ message: 'Holiday name must be a string' })
  name?: string;

  @ApiPropertyOptional({
    description: 'Date of the holiday (ISO 8601 format)',
    example: '2024-12-25T00:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Holiday date must be a valid ISO 8601 date string' })
  date?: string;
}
