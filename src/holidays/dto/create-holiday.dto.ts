import { IsNotEmpty, IsString, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a holiday
 */
export class CreateHolidayDto {
  @ApiProperty({
    description: 'Name of the holiday',
    example: 'Christmas Day',
  })
  @IsNotEmpty({ message: 'Holiday name is required' })
  @IsString({ message: 'Holiday name must be a string' })
  name: string;

  @ApiProperty({
    description: 'Date of the holiday (ISO 8601 format)',
    example: '2024-12-25T00:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  @IsNotEmpty({ message: 'Holiday date is required' })
  @IsDateString({}, { message: 'Holiday date must be a valid ISO 8601 date string' })
  date: string;
}
