import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for holiday response data
 */
export class HolidayResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the holiday',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Name of the holiday',
    example: 'Christmas Day',
  })
  name?: string;

  @ApiProperty({
    description: 'Date of the holiday',
    example: '2024-12-25T00:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  date: Date;

  @ApiProperty({
    description: 'Name of the user who added this holiday',
    example: '<PERSON>',
  })
  user: string;

  @ApiProperty({
    description: 'Timestamp when the holiday was created',
    example: '2024-01-15T08:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Whether the holiday is upcoming (today or in the future)',
    example: true,
  })
  is_upcoming?: boolean;
}
