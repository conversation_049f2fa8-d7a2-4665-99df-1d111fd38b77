import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { HolidaysService } from './holidays.service';
import { CreateHolidayDto } from './dto/create-holiday.dto';
import { UpdateHolidayDto } from './dto/update-holiday.dto';
import { HolidayResponseDto } from './dto/holiday-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

/**
 * Controller responsible for holiday-related HTTP endpoints
 *
 * This controller provides RESTful API endpoints for managing holidays,
 * including CRUD operations with comprehensive validation and error handling.
 * All endpoints require authentication and follow REST conventions.
 *
 * @controller holidays
 */
@ApiTags('holidays')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('holidays')
export class HolidaysController {
  constructor(private readonly holidaysService: HolidaysService) {}

  /**
   * Creates a new holiday
   * POST /holidays
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new holiday',
    description:
      'Creates a new holiday and automatically moves any TwoByTwoPhase records with matching expected_completion_date to the next applicable workday. Validates that no holiday with the same date already exists.',
  })
  @ApiResponse({
    status: 201,
    description: 'Holiday created successfully',
    type: HolidayResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  @ApiConflictResponse({
    description: 'Holiday with the same date already exists',
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async create(
    @Body(new ValidationPipe({ whitelist: true, transform: true })) createHolidayDto: CreateHolidayDto,
    @Request() req: any,
  ): Promise<HolidayResponseDto> {
    return this.holidaysService.create(createHolidayDto, req.user.id);
  }

  /**
   * Retrieves all holidays
   * GET /holidays
   */
  @Get()
  @ApiOperation({
    summary: 'Get all holidays',
    description:
      'Retrieves all holidays with information about who added them and whether they are upcoming (today or in the future).',
  })
  @ApiResponse({
    status: 200,
    description: 'Holidays retrieved successfully',
    type: [HolidayResponseDto],
  })
  @ApiBadRequestResponse({ description: 'Database connection failed' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findAll(): Promise<HolidayResponseDto[]> {
    return this.holidaysService.findAll();
  }

  /**
   * Updates an existing holiday
   * PATCH /holidays/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update holiday',
    description:
      'Updates an existing holiday. If the date changes, automatically moves any TwoByTwoPhase records with matching expected_completion_date to the next applicable workday. Validates that no other holiday with the same date exists.',
  })
  @ApiParam({
    name: 'id',
    description: 'Holiday UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Holiday updated successfully',
    type: HolidayResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Holiday not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  @ApiConflictResponse({
    description: 'Holiday with the same date already exists',
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, transform: true })) updateHolidayDto: UpdateHolidayDto,
  ): Promise<HolidayResponseDto> {
    return this.holidaysService.update(id, updateHolidayDto);
  }

  /**
   * Deletes a holiday
   * DELETE /holidays/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete holiday',
    description:
      'Deletes an existing holiday. This does not affect existing TwoByTwoPhase records that may have been moved due to this holiday.',
  })
  @ApiParam({
    name: 'id',
    description: 'Holiday UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Holiday deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Holiday not found' })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format or database connection failed',
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.holidaysService.remove(id);
  }
}
