import { <PERSON><PERSON>ption<PERSON>, <PERSON><PERSON><PERSON>, IsString, IsDateString, IsI<PERSON>, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ScheduledTaskStatus } from '@prisma/client';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class QueryScheduledTasksDto {
  @ApiPropertyOptional({ description: 'Filter by task type', example: 'send-email' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by task status', 
    enum: ScheduledTaskStatus,
    example: ScheduledTaskStatus.PENDING 
  })
  @IsOptional()
  @IsEnum(ScheduledTaskStatus)
  status?: ScheduledTaskStatus;

  @ApiPropertyOptional({ description: 'Filter by queue name', example: 'emails' })
  @IsOptional()
  @IsString()
  queueName?: string;

  @ApiPropertyOptional({ description: 'Filter by creator user ID', example: 'user-123' })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiPropertyOptional({ description: 'Filter tasks from this run time', example: '2024-01-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  runAtFrom?: Date;

  @ApiPropertyOptional({ description: 'Filter tasks until this run time', example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  runAtTo?: Date;

  @ApiPropertyOptional({ description: 'Filter by priority', example: 5 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ description: 'Page number', example: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', example: 50, default: 50 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 50;
}
