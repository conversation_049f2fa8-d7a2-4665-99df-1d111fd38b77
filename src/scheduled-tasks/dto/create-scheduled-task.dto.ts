import { IsString, IsOptional, IsDate<PERSON>tring, IsEnum, IsInt, IsObject, Min, Max, IsNotEmpty } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ScheduledTaskInterval } from '@prisma/client';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateScheduledTaskDto {
  @ApiProperty({ description: 'Task type identifier', example: 'send-email' })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiPropertyOptional({ description: 'Human-readable task name', example: 'Send welcome email' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Task description', example: 'Send welcome email to new users' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Task payload/parameters', example: { userId: '123', template: 'welcome' } })
  @IsObject()
  payload: any;

  @ApiProperty({ description: 'When to run the task', example: '2024-01-01T10:00:00Z' })
  @IsNotEmpty({ message: 'runAt is required' })
  @Transform(({ value }) => new Date(value))
  runAt: Date;

  @ApiPropertyOptional({ 
    description: 'Interval type for recurring tasks', 
    enum: ScheduledTaskInterval,
    example: ScheduledTaskInterval.HOURS 
  })
  @IsOptional()
  @IsEnum(ScheduledTaskInterval)
  intervalType?: ScheduledTaskInterval;

  @ApiPropertyOptional({ description: 'Interval value (e.g., 5 for every 5 hours)', example: 5 })
  @IsOptional()
  @IsInt()
  @Min(1)
  intervalValue?: number;

  @ApiPropertyOptional({ description: 'Cron expression for complex schedules', example: '0 9 * * 1-5' })
  @IsOptional()
  @IsString()
  cronExpression?: string;

  @ApiPropertyOptional({ description: 'Maximum retry attempts', example: 3, default: 3 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(10)
  maxAttempts?: number;

  @ApiPropertyOptional({ description: 'Task priority (higher = more important)', example: 5, default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ description: 'BullMQ queue name', example: 'emails', default: 'default' })
  @IsOptional()
  @IsString()
  queueName?: string;
}
