import { IsString, IsOptional, IsDateString, IsEnum, IsInt, IsObject, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { ScheduledTaskInterval, ScheduledTaskStatus } from '@prisma/client';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateScheduledTaskDto {
  @ApiPropertyOptional({ description: 'Human-readable task name', example: 'Send welcome email' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Task description', example: 'Send welcome email to new users' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Task payload/parameters', example: { userId: '123', template: 'welcome' } })
  @IsOptional()
  @IsObject()
  payload?: any;

  @ApiPropertyOptional({ description: 'When to run the task', example: '2024-01-01T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  runAt?: Date;

  @ApiPropertyOptional({ 
    description: 'Interval type for recurring tasks', 
    enum: ScheduledTaskInterval,
    example: ScheduledTaskInterval.HOURS 
  })
  @IsOptional()
  @IsEnum(ScheduledTaskInterval)
  intervalType?: ScheduledTaskInterval;

  @ApiPropertyOptional({ description: 'Interval value (e.g., 5 for every 5 hours)', example: 5 })
  @IsOptional()
  @IsInt()
  @Min(1)
  intervalValue?: number;

  @ApiPropertyOptional({ description: 'Cron expression for complex schedules', example: '0 9 * * 1-5' })
  @IsOptional()
  @IsString()
  cronExpression?: string;

  @ApiPropertyOptional({ description: 'Maximum retry attempts', example: 3 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(10)
  maxAttempts?: number;

  @ApiPropertyOptional({ description: 'Task priority (higher = more important)', example: 5 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ 
    description: 'Task status', 
    enum: ScheduledTaskStatus,
    example: ScheduledTaskStatus.PAUSED 
  })
  @IsOptional()
  @IsEnum(ScheduledTaskStatus)
  status?: ScheduledTaskStatus;
}
