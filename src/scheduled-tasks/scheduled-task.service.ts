import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ScheduledTaskStatus, ScheduledTaskInterval, TaskExecutionStatus, Prisma } from '@prisma/client';
import { addMinutes, addHours, addDays, addWeeks, addMonths, isBefore, isAfter } from 'date-fns';
import * as cron from 'node-cron';

export interface CreateScheduledTaskDto {
  type: string;
  name?: string;
  description?: string;
  payload: any;
  runAt: Date;
  intervalType?: ScheduledTaskInterval;
  intervalValue?: number;
  cronExpression?: string;
  maxAttempts?: number;
  priority?: number;
  queueName?: string;
  createdBy?: string;
}

export interface UpdateScheduledTaskDto {
  name?: string;
  description?: string;
  payload?: any;
  runAt?: Date;
  intervalType?: ScheduledTaskInterval;
  intervalValue?: number;
  cronExpression?: string;
  maxAttempts?: number;
  priority?: number;
  status?: ScheduledTaskStatus;
}

export interface ScheduledTaskFilters {
  type?: string;
  status?: ScheduledTaskStatus;
  queueName?: string;
  createdBy?: string;
  runAtFrom?: Date;
  runAtTo?: Date;
  priority?: number;
}

@Injectable()
export class ScheduledTaskService {
  private readonly logger = new Logger(ScheduledTaskService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateScheduledTaskDto) {
    this.logger.log(`Creating scheduled task: ${data.type}`);

    // Validate the task data
    this.validateTaskData(data);

    // Calculate next run time for recurring tasks
    const nextRunAt = this.calculateNextRunTime(data);

    const task = await this.prisma.scheduledTask.create({
      data: {
        type: data.type,
        name: data.name,
        description: data.description,
        payload: data.payload,
        run_at: data.runAt,
        interval_type: data.intervalType,
        interval_value: data.intervalValue,
        cron_expression: data.cronExpression,
        max_attempts: data.maxAttempts || 3,
        priority: data.priority || 0,
        queue_name: data.queueName || 'default',
        next_run_at: nextRunAt,
        created_by: data.createdBy,
      },
      include: {
        user: true,
        executions: true,
      },
    });

    this.logger.log(`Created scheduled task: ${task.id}`);
    return task;
  }

  async findAll(filters: ScheduledTaskFilters = {}, page = 1, limit = 50) {
    const where: Prisma.ScheduledTaskWhereInput = {};

    if (filters.type) where.type = filters.type;
    if (filters.status) where.status = filters.status;
    if (filters.queueName) where.queue_name = filters.queueName;
    if (filters.createdBy) where.created_by = filters.createdBy;
    if (filters.priority !== undefined) where.priority = filters.priority;

    if (filters.runAtFrom || filters.runAtTo) {
      where.run_at = {};
      if (filters.runAtFrom) where.run_at.gte = filters.runAtFrom;
      if (filters.runAtTo) where.run_at.lte = filters.runAtTo;
    }

    const [tasks, total] = await Promise.all([
      this.prisma.scheduledTask.findMany({
        where,
        include: {
          user: true,
          executions: {
            orderBy: { started_at: 'desc' },
            take: 1,
          },
        },
        orderBy: [
          { priority: 'desc' },
          { run_at: 'asc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prisma.scheduledTask.count({ where }),
    ]);

    return {
      tasks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const task = await this.prisma.scheduledTask.findUnique({
      where: { id },
      include: {
        user: true,
        executions: {
          orderBy: { started_at: 'desc' },
        },
      },
    });

    if (!task) {
      throw new NotFoundException(`Scheduled task with ID ${id} not found`);
    }

    return task;
  }

  async update(id: string, data: UpdateScheduledTaskDto) {
    this.logger.log(`Updating scheduled task: ${id}`);

    const existingTask = await this.findOne(id);

    // Prevent updating running tasks
    if (existingTask.status === ScheduledTaskStatus.RUNNING) {
      throw new BadRequestException('Cannot update a running task');
    }

    // Calculate new next run time if interval data changed
    let nextRunAt = existingTask.next_run_at;
    if (data.runAt || data.intervalType || data.intervalValue || data.cronExpression) {
      const taskData = {
        ...existingTask,
        ...data,
        runAt: data.runAt || existingTask.run_at,
      };
      nextRunAt = this.calculateNextRunTime(taskData);
    }

    const updatedTask = await this.prisma.scheduledTask.update({
      where: { id },
      data: {
        ...data,
        next_run_at: nextRunAt,
        updated_at: new Date(),
      },
      include: {
        user: true,
        executions: {
          orderBy: { started_at: 'desc' },
          take: 5,
        },
      },
    });

    this.logger.log(`Updated scheduled task: ${id}`);
    return updatedTask;
  }

  async cancel(id: string) {
    this.logger.log(`Cancelling scheduled task: ${id}`);

    const task = await this.findOne(id);

    if (task.status === ScheduledTaskStatus.COMPLETED) {
      throw new BadRequestException('Cannot cancel a completed task');
    }

    if (task.status === ScheduledTaskStatus.RUNNING) {
      throw new BadRequestException('Cannot cancel a running task');
    }

    const cancelledTask = await this.prisma.scheduledTask.update({
      where: { id },
      data: {
        status: ScheduledTaskStatus.CANCELLED,
        updated_at: new Date(),
      },
      include: {
        user: true,
        executions: true,
      },
    });

    this.logger.log(`Cancelled scheduled task: ${id}`);
    return cancelledTask;
  }

  async delete(id: string) {
    this.logger.log(`Deleting scheduled task: ${id}`);

    const task = await this.findOne(id);

    if (task.status === ScheduledTaskStatus.RUNNING) {
      throw new BadRequestException('Cannot delete a running task');
    }

    await this.prisma.scheduledTask.delete({
      where: { id },
    });

    this.logger.log(`Deleted scheduled task: ${id}`);
  }

  async pause(id: string) {
    this.logger.log(`Pausing scheduled task: ${id}`);

    const task = await this.update(id, { status: ScheduledTaskStatus.PAUSED });
    return task;
  }

  async resume(id: string) {
    this.logger.log(`Resuming scheduled task: ${id}`);

    const task = await this.update(id, { status: ScheduledTaskStatus.PENDING });
    return task;
  }

  // Get tasks ready to be executed
  async getTasksReadyForExecution(limit = 100) {
    const now = new Date();

    return this.prisma.scheduledTask.findMany({
      where: {
        status: ScheduledTaskStatus.PENDING,
        run_at: {
          lte: now,
        },
      },
      orderBy: [
        { priority: 'desc' },
        { run_at: 'asc' },
      ],
      take: limit,
      include: {
        executions: {
          orderBy: { started_at: 'desc' },
          take: 1,
        },
      },
    });
  }

  // Get recurring tasks that need to be scheduled
  async getRecurringTasksForScheduling(limit = 100) {
    const now = new Date();

    return this.prisma.scheduledTask.findMany({
      where: {
        status: ScheduledTaskStatus.COMPLETED,
        OR: [
          { interval_type: { not: null } },
          { cron_expression: { not: null } },
        ],
        next_run_at: {
          lte: now,
        },
      },
      orderBy: [
        { priority: 'desc' },
        { next_run_at: 'asc' },
      ],
      take: limit,
    });
  }

  // Mark task as running and create execution record
  async markAsRunning(id: string, workerId?: string) {
    const task = await this.prisma.scheduledTask.update({
      where: { id },
      data: {
        status: ScheduledTaskStatus.RUNNING,
        last_run_at: new Date(),
        attempts: { increment: 1 },
        updated_at: new Date(),
      },
    });

    // Create execution record
    const execution = await this.prisma.scheduledTaskExecution.create({
      data: {
        task_id: id,
        worker_id: workerId,
        attempt_number: task.attempts,
        status: TaskExecutionStatus.RUNNING,
      },
    });

    return { task, execution };
  }

  // Mark task as completed
  async markAsCompleted(id: string, executionId: string, result?: any, durationMs?: number) {
    const now = new Date();

    // Update task
    const task = await this.prisma.scheduledTask.update({
      where: { id },
      data: {
        status: ScheduledTaskStatus.COMPLETED,
        completed_at: now,
        updated_at: now,
      },
    });

    // Update execution record
    await this.prisma.scheduledTaskExecution.update({
      where: { id: executionId },
      data: {
        status: TaskExecutionStatus.COMPLETED,
        completed_at: now,
        result: result,
        duration_ms: durationMs,
      },
    });

    // Schedule next run for recurring tasks
    if (task.interval_type || task.cron_expression) {
      await this.scheduleNextRun(task);
    }

    return task;
  }

  // Mark task as failed
  async markAsFailed(id: string, executionId: string, errorMessage: string, durationMs?: number) {
    const task = await this.findOne(id);
    const now = new Date();

    const shouldRetry = task.attempts < task.max_attempts;
    const status = shouldRetry ? ScheduledTaskStatus.PENDING : ScheduledTaskStatus.FAILED;

    // Update task
    const updatedTask = await this.prisma.scheduledTask.update({
      where: { id },
      data: {
        status,
        failed_at: shouldRetry ? undefined : now,
        error_message: errorMessage,
        updated_at: now,
      },
    });

    // Update execution record
    await this.prisma.scheduledTaskExecution.update({
      where: { id: executionId },
      data: {
        status: TaskExecutionStatus.FAILED,
        completed_at: now,
        error_message: errorMessage,
        duration_ms: durationMs,
      },
    });

    return updatedTask;
  }

  private validateTaskData(data: CreateScheduledTaskDto) {
    if (!data.type) {
      throw new BadRequestException('Task type is required');
    }

    if (!data.runAt) {
      throw new BadRequestException('Run time is required');
    }

    if (isBefore(data.runAt, new Date())) {
      throw new BadRequestException('Run time cannot be in the past');
    }

    // Validate recurring task data
    if (data.intervalType && !data.intervalValue) {
      throw new BadRequestException('Interval value is required when interval type is specified');
    }

    if (data.intervalValue && !data.intervalType && !data.cronExpression) {
      throw new BadRequestException('Interval type or cron expression is required when interval value is specified');
    }

    // Validate cron expression
    if (data.cronExpression && !cron.validate(data.cronExpression)) {
      throw new BadRequestException('Invalid cron expression');
    }
  }

  private calculateNextRunTime(data: any): Date | null {
    if (!data.intervalType && !data.cronExpression) {
      return null; // One-time task
    }

    const baseTime = data.runAt;

    if (data.cronExpression) {
      // For cron expressions, we'll calculate this in the scheduler
      return null;
    }

    if (data.intervalType && data.intervalValue) {
      switch (data.intervalType) {
        case ScheduledTaskInterval.MINUTES:
          return addMinutes(baseTime, data.intervalValue);
        case ScheduledTaskInterval.HOURS:
          return addHours(baseTime, data.intervalValue);
        case ScheduledTaskInterval.DAYS:
          return addDays(baseTime, data.intervalValue);
        case ScheduledTaskInterval.WEEKS:
          return addWeeks(baseTime, data.intervalValue);
        case ScheduledTaskInterval.MONTHS:
          return addMonths(baseTime, data.intervalValue);
        default:
          return null;
      }
    }

    return null;
  }

  private async scheduleNextRun(task: any) {
    let nextRunAt: Date | null = null;

    if (task.cron_expression) {
      // Calculate next run time based on cron expression
      // This would require a cron parser library
      // For now, we'll handle this in the scheduler service
      return;
    }

    if (task.interval_type && task.interval_value) {
      const now = new Date();
      switch (task.interval_type) {
        case ScheduledTaskInterval.MINUTES:
          nextRunAt = addMinutes(now, task.interval_value);
          break;
        case ScheduledTaskInterval.HOURS:
          nextRunAt = addHours(now, task.interval_value);
          break;
        case ScheduledTaskInterval.DAYS:
          nextRunAt = addDays(now, task.interval_value);
          break;
        case ScheduledTaskInterval.WEEKS:
          nextRunAt = addWeeks(now, task.interval_value);
          break;
        case ScheduledTaskInterval.MONTHS:
          nextRunAt = addMonths(now, task.interval_value);
          break;
      }
    }

    if (nextRunAt) {
      await this.prisma.scheduledTask.update({
        where: { id: task.id },
        data: {
          status: ScheduledTaskStatus.PENDING,
          run_at: nextRunAt,
          next_run_at: this.calculateNextRunTime({
            ...task,
            runAt: nextRunAt,
          }),
          attempts: 0,
          completed_at: null,
          failed_at: null,
          error_message: null,
          updated_at: new Date(),
        },
      });
    }
  }
}
