import { ScheduledTaskInterval } from '@prisma/client';
import { addMinutes, addHours, addDays, addWeeks, addMonths, isBefore, isAfter, isValid } from 'date-fns';
import * as cron from 'node-cron';

export class TaskUtils {
  /**
   * Validate task scheduling data
   */
  static validateTaskData(data: {
    type: string;
    runAt: Date;
    intervalType?: ScheduledTaskInterval;
    intervalValue?: number;
    cronExpression?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate required fields
    if (!data.type || data.type.trim().length === 0) {
      errors.push('Task type is required');
    }

    if (!data.runAt) {
      errors.push('Run time is required');
    } else if (!isValid(data.runAt)) {
      errors.push('Invalid run time format');
    } else if (isBefore(data.runAt, new Date())) {
      errors.push('Run time cannot be in the past');
    }

    // Validate recurring task data
    if (data.intervalType && !data.intervalValue) {
      errors.push('Interval value is required when interval type is specified');
    }

    if (data.intervalValue && data.intervalValue <= 0) {
      errors.push('Interval value must be greater than 0');
    }

    if (data.intervalValue && !data.intervalType && !data.cronExpression) {
      errors.push('Interval type or cron expression is required when interval value is specified');
    }

    // Validate cron expression
    if (data.cronExpression) {
      if (!cron.validate(data.cronExpression)) {
        errors.push('Invalid cron expression');
      }
    }

    // Validate interval combinations
    if (data.intervalType && data.cronExpression) {
      errors.push('Cannot specify both interval type and cron expression');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Calculate next run time for recurring tasks
   */
  static calculateNextRunTime(
    baseTime: Date,
    intervalType?: ScheduledTaskInterval,
    intervalValue?: number,
    cronExpression?: string,
  ): Date | null {
    if (!intervalType && !cronExpression) {
      return null; // One-time task
    }

    if (cronExpression) {
      // For cron expressions, we would need a cron parser library
      // For now, return null and handle in scheduler
      return null;
    }

    if (intervalType && intervalValue) {
      switch (intervalType) {
        case ScheduledTaskInterval.MINUTES:
          return addMinutes(baseTime, intervalValue);
        case ScheduledTaskInterval.HOURS:
          return addHours(baseTime, intervalValue);
        case ScheduledTaskInterval.DAYS:
          return addDays(baseTime, intervalValue);
        case ScheduledTaskInterval.WEEKS:
          return addWeeks(baseTime, intervalValue);
        case ScheduledTaskInterval.MONTHS:
          return addMonths(baseTime, intervalValue);
        default:
          return null;
      }
    }

    return null;
  }

  /**
   * Check if a task is recurring
   */
  static isRecurringTask(intervalType?: ScheduledTaskInterval, cronExpression?: string): boolean {
    return !!(intervalType || cronExpression);
  }

  /**
   * Generate human-readable description of task schedule
   */
  static getScheduleDescription(
    intervalType?: ScheduledTaskInterval,
    intervalValue?: number,
    cronExpression?: string,
  ): string {
    if (cronExpression) {
      return `Cron: ${cronExpression}`;
    }

    if (intervalType && intervalValue) {
      const unit = intervalType.toLowerCase();
      const unitName = intervalValue === 1 ? unit.slice(0, -1) : unit;
      return `Every ${intervalValue} ${unitName}`;
    }

    return 'One-time task';
  }

  /**
   * Calculate task priority based on various factors
   */
  static calculateTaskPriority(
    basePriority: number,
    runAt: Date,
    taskType: string,
    attempts: number,
  ): number {
    let priority = basePriority;

    // Increase priority for overdue tasks
    const now = new Date();
    if (isBefore(runAt, now)) {
      const overdueMinutes = Math.floor((now.getTime() - runAt.getTime()) / (1000 * 60));
      priority += Math.min(overdueMinutes / 10, 5); // Max 5 points for being overdue
    }

    // Increase priority for failed tasks (retry with higher priority)
    if (attempts > 0) {
      priority += attempts * 2;
    }

    // Adjust priority based on task type
    const highPriorityTypes = ['send-email', 'notification', 'alert'];
    if (highPriorityTypes.includes(taskType)) {
      priority += 3;
    }

    return Math.min(priority, 10); // Cap at 10
  }

  /**
   * Generate task name if not provided
   */
  static generateTaskName(type: string, payload: any): string {
    switch (type) {
      case 'send-email':
        return `Send email to ${payload.to || 'recipient'}`;
      case 'generate-report':
        return `Generate ${payload.reportType || 'report'} report`;
      case 'data-processing':
        return `Process ${payload.type || 'data'}`;
      case 'cleanup':
        return `Cleanup ${payload.cleanupType || 'data'}`;
      case 'notification':
        return `Send ${payload.notificationType || 'notification'}`;
      default:
        return `Execute ${type} task`;
    }
  }

  /**
   * Validate task payload based on type
   */
  static validateTaskPayload(type: string, payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload || typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { isValid: false, errors };
    }

    switch (type) {
      case 'send-email':
        if (!payload.to) errors.push('Email recipient (to) is required');
        if (!payload.subject) errors.push('Email subject is required');
        if (!payload.template && !payload.html && !payload.text) {
          errors.push('Email template, html, or text content is required');
        }
        break;

      case 'generate-report':
        if (!payload.reportType) errors.push('Report type is required');
        if (!payload.userId) errors.push('User ID is required');
        break;

      case 'data-processing':
        if (!payload.type) errors.push('Processing type is required');
        if (!payload.userId) errors.push('User ID is required');
        break;

      case 'cleanup':
        if (!payload.cleanupType) errors.push('Cleanup type is required');
        break;

      case 'notification':
        if (!payload.notificationType) errors.push('Notification type is required');
        break;

      default:
        // For custom task types, we don't validate payload structure
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get recommended retry delay based on attempt number
   */
  static getRetryDelay(attemptNumber: number): number {
    // Exponential backoff: 2^attempt * 1000ms, capped at 5 minutes
    const delay = Math.pow(2, attemptNumber) * 1000;
    return Math.min(delay, 5 * 60 * 1000); // Max 5 minutes
  }

  /**
   * Check if task should be retried based on error type
   */
  static shouldRetryTask(error: Error, attemptNumber: number, maxAttempts: number): boolean {
    if (attemptNumber >= maxAttempts) {
      return false;
    }

    // Don't retry certain types of errors
    const nonRetryableErrors = [
      'ValidationError',
      'AuthenticationError',
      'AuthorizationError',
      'NotFoundError',
    ];

    const errorType = error.constructor.name;
    if (nonRetryableErrors.includes(errorType)) {
      return false;
    }

    // Don't retry if error message indicates a permanent failure
    const permanentFailureMessages = [
      'invalid email address',
      'user not found',
      'permission denied',
      'invalid payload',
    ];

    const errorMessage = error.message.toLowerCase();
    if (permanentFailureMessages.some(msg => errorMessage.includes(msg))) {
      return false;
    }

    return true;
  }

  /**
   * Sanitize task payload for logging (remove sensitive data)
   */
  static sanitizePayloadForLogging(payload: any): any {
    if (!payload || typeof payload !== 'object') {
      return payload;
    }

    const sanitized = { ...payload };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credential'];

    const sanitizeObject = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          const lowerKey = key.toLowerCase();
          if (sensitiveFields.some(field => lowerKey.includes(field))) {
            result[key] = '[REDACTED]';
          } else {
            result[key] = sanitizeObject(value);
          }
        }
        return result;
      }

      return obj;
    };

    return sanitizeObject(sanitized);
  }
}
