import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ScheduledTaskService } from './scheduled-task.service';
import { TaskHandlerRegistry } from './task-handlers/task-handler.registry';
import { CreateScheduledTaskDto } from './dto/create-scheduled-task.dto';
import { UpdateScheduledTaskDto } from './dto/update-scheduled-task.dto';
import { QueryScheduledTasksDto } from './dto/query-scheduled-tasks.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Scheduled Tasks')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('scheduled-tasks')
export class ScheduledTaskController {
  constructor(
    private readonly scheduledTaskService: ScheduledTaskService,
    private readonly taskHandlerRegistry: TaskHandlerRegistry,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new scheduled task' })
  @ApiResponse({ status: 201, description: 'Task created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid task data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Body() createScheduledTaskDto: CreateScheduledTaskDto, @Request() req) {
    return this.scheduledTaskService.create({
      ...createScheduledTaskDto,
      createdBy: req.user.id,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all scheduled tasks with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(@Query() query: QueryScheduledTasksDto) {
    const { page, limit, ...filters } = query;
    return this.scheduledTaskService.findAll(filters, page, limit);
  }

  @Get('stats/overview')
  @ApiOperation({ summary: 'Get scheduled tasks statistics overview' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getStats() {
    // This would be implemented in the service
    return {
      message: 'Statistics endpoint - to be implemented',
    };
  }

  @Get('task-types')
  @ApiOperation({ summary: 'Get all available task types and their descriptions' })
  @ApiResponse({ status: 200, description: 'Task types retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getTaskTypes() {
    return {
      taskTypes: this.taskHandlerRegistry.getAllHandlers(),
      registeredTypes: this.taskHandlerRegistry.getRegisteredTaskTypes(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific scheduled task by ID' })
  @ApiResponse({ status: 200, description: 'Task retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(@Param('id') id: string) {
    return this.scheduledTaskService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a scheduled task' })
  @ApiResponse({ status: 200, description: 'Task updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid update data or task is running' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async update(@Param('id') id: string, @Body() updateScheduledTaskDto: UpdateScheduledTaskDto) {
    return this.scheduledTaskService.update(id, updateScheduledTaskDto);
  }

  @Post(':id/cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Cancel a scheduled task' })
  @ApiResponse({ status: 200, description: 'Task cancelled successfully' })
  @ApiResponse({ status: 400, description: 'Cannot cancel completed or running task' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async cancel(@Param('id') id: string) {
    return this.scheduledTaskService.cancel(id);
  }

  @Post(':id/pause')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Pause a scheduled task' })
  @ApiResponse({ status: 200, description: 'Task paused successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async pause(@Param('id') id: string) {
    return this.scheduledTaskService.pause(id);
  }

  @Post(':id/resume')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resume a paused scheduled task' })
  @ApiResponse({ status: 200, description: 'Task resumed successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async resume(@Param('id') id: string) {
    return this.scheduledTaskService.resume(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a scheduled task' })
  @ApiResponse({ status: 204, description: 'Task deleted successfully' })
  @ApiResponse({ status: 400, description: 'Cannot delete running task' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async remove(@Param('id') id: string) {
    await this.scheduledTaskService.delete(id);
  }

  @Get(':id/executions')
  @ApiOperation({ summary: 'Get execution history for a task' })
  @ApiResponse({ status: 200, description: 'Execution history retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Task not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getExecutions(@Param('id') id: string) {
    const task = await this.scheduledTaskService.findOne(id);
    return {
      taskId: id,
      executions: task.executions,
    };
  }

}
