import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TaskExecutionStatus } from '@prisma/client';

@Injectable()
export class TaskExecutionService {
  private readonly logger = new Logger(TaskExecutionService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createExecution(taskId: string, workerId?: string, attemptNumber = 1) {
    this.logger.log(`Creating execution record for task ${taskId}`);

    return this.prisma.scheduledTaskExecution.create({
      data: {
        task_id: taskId,
        worker_id: workerId,
        attempt_number: attemptNumber,
        status: TaskExecutionStatus.RUNNING,
      },
    });
  }

  async updateExecution(
    executionId: string,
    status: TaskExecutionStatus,
    result?: any,
    errorMessage?: string,
    durationMs?: number,
  ) {
    this.logger.log(`Updating execution ${executionId} with status ${status}`);

    const updateData: any = {
      status,
      completed_at: new Date(),
    };

    if (result !== undefined) {
      updateData.result = result;
    }

    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    if (durationMs !== undefined) {
      updateData.duration_ms = durationMs;
    }

    return this.prisma.scheduledTaskExecution.update({
      where: { id: executionId },
      data: updateData,
    });
  }

  async getExecutionHistory(taskId: string, limit = 50) {
    return this.prisma.scheduledTaskExecution.findMany({
      where: { task_id: taskId },
      orderBy: { started_at: 'desc' },
      take: limit,
    });
  }

  async getExecutionStats(taskId?: string) {
    const where = taskId ? { task_id: taskId } : {};

    const [total, running, completed, failed] = await Promise.all([
      this.prisma.scheduledTaskExecution.count({ where }),
      this.prisma.scheduledTaskExecution.count({
        where: { ...where, status: TaskExecutionStatus.RUNNING },
      }),
      this.prisma.scheduledTaskExecution.count({
        where: { ...where, status: TaskExecutionStatus.COMPLETED },
      }),
      this.prisma.scheduledTaskExecution.count({
        where: { ...where, status: TaskExecutionStatus.FAILED },
      }),
    ]);

    return {
      total,
      running,
      completed,
      failed,
      successRate: total > 0 ? (completed / total) * 100 : 0,
    };
  }

  async getAverageExecutionTime(taskType?: string, days = 30) {
    const since = new Date();
    since.setDate(since.getDate() - days);

    const executions = await this.prisma.scheduledTaskExecution.findMany({
      where: {
        status: TaskExecutionStatus.COMPLETED,
        completed_at: { gte: since },
        duration_ms: { not: null },
        ...(taskType && {
          task: { type: taskType },
        }),
      },
      select: {
        duration_ms: true,
      },
    });

    if (executions.length === 0) {
      return 0;
    }

    const totalDuration = executions.reduce((sum, exec) => sum + (exec.duration_ms || 0), 0);
    return Math.round(totalDuration / executions.length);
  }

  async cleanupOldExecutions(olderThanDays = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    this.logger.log(`Cleaning up executions older than ${olderThanDays} days`);

    const result = await this.prisma.scheduledTaskExecution.deleteMany({
      where: {
        started_at: { lt: cutoffDate },
        status: { in: [TaskExecutionStatus.COMPLETED, TaskExecutionStatus.FAILED] },
      },
    });

    this.logger.log(`Cleaned up ${result.count} old execution records`);
    return result.count;
  }

  async getFailedExecutions(limit = 100) {
    return this.prisma.scheduledTaskExecution.findMany({
      where: { status: TaskExecutionStatus.FAILED },
      include: {
        task: {
          select: {
            id: true,
            type: true,
            name: true,
          },
        },
      },
      orderBy: { started_at: 'desc' },
      take: limit,
    });
  }

  async getRunningExecutions() {
    return this.prisma.scheduledTaskExecution.findMany({
      where: { status: TaskExecutionStatus.RUNNING },
      include: {
        task: {
          select: {
            id: true,
            type: true,
            name: true,
            max_attempts: true,
          },
        },
      },
      orderBy: { started_at: 'asc' },
    });
  }

  async getExecutionsByWorker(workerId: string, limit = 50) {
    return this.prisma.scheduledTaskExecution.findMany({
      where: { worker_id: workerId },
      include: {
        task: {
          select: {
            id: true,
            type: true,
            name: true,
          },
        },
      },
      orderBy: { started_at: 'desc' },
      take: limit,
    });
  }
}
