import { Job } from 'bullmq';

export interface TaskHandler {
  /**
   * Handle the execution of a specific task type
   * @param payload - The task payload
   * @param job - The BullMQ job instance for progress tracking
   * @returns Promise<any> - The result of the task execution
   */
  handle(payload: any, job: Job): Promise<any>;

  /**
   * Validate the payload for this task type
   * @param payload - The task payload to validate
   * @returns { isValid: boolean; errors: string[] }
   */
  validatePayload(payload: any): { isValid: boolean; errors: string[] };

  /**
   * Get the task type this handler supports
   */
  getTaskType(): string;

  /**
   * Get a human-readable description of what this task does
   */
  getDescription(): string;
}
