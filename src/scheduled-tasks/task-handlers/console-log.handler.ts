import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';

@Injectable()
export class ConsoleLogHandler implements TaskHandler {
  private readonly logger = new Logger(ConsoleLogHandler.name);

  getTaskType(): string {
    return 'console-log';
  }

  getDescription(): string {
    return 'Logs a message to the console with specified log level';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload || typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { isValid: false, errors };
    }

    if (!payload.message || typeof payload.message !== 'string') {
      errors.push('Message is required and must be a string');
    }

    if (payload.level && !['error', 'warn', 'log', 'debug', 'verbose'].includes(payload.level)) {
      errors.push('Log level must be one of: error, warn, log, debug, verbose');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: any, job: Job): Promise<any> {
    const { message, level = 'log', metadata = {} } = payload;

    await job.updateProgress(25);

    // Log the message with the specified level
    const timestamp = new Date().toISOString();
    const logMessage = `[SCHEDULED TASK] ${message}`;

    switch (level) {
      case 'error':
        this.logger.error(logMessage, metadata);
        break;
      case 'warn':
        this.logger.warn(logMessage, metadata);
        break;
      case 'debug':
        this.logger.debug(logMessage, metadata);
        break;
      case 'verbose':
        this.logger.verbose(logMessage, metadata);
        break;
      default:
        this.logger.log(logMessage, metadata);
        break;
    }

    await job.updateProgress(75);

    // Also log to console directly for immediate visibility
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`, metadata);

    await job.updateProgress(100);

    return {
      type: 'console-log',
      message,
      level,
      timestamp,
      metadata,
      success: true,
    };
  }
}
