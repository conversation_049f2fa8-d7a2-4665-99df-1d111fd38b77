import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { format, addHours } from 'date-fns';

@Injectable()
export class DailyNairobiTaskHandler implements TaskHandler {
  private readonly logger = new Logger(DailyNairobiTaskHandler.name);

  getTaskType(): string {
    return 'daily-nairobi-task';
  }

  getDescription(): string {
    return 'Executes a daily task at a specific time in Africa/Nairobi timezone';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload || typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { isValid: false, errors };
    }

    if (!payload.message || typeof payload.message !== 'string') {
      errors.push('Message is required and must be a string');
    }

    if (payload.timezone && payload.timezone !== 'Africa/Nairobi') {
      errors.push('This handler only supports Africa/Nairobi timezone');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: any, job: Job): Promise<any> {
    const {
      message,
      timezone = 'Africa/Nairobi',
      localTime = '22:45',
      metadata = {},
    } = payload;

    await job.updateProgress(20);

    // Get current time in Nairobi (UTC+3)
    const now = new Date();
    const nairobiTime = addHours(now, 3); // Nairobi is UTC+3
    const nairobiTimeString = format(nairobiTime, 'yyyy-MM-dd HH:mm:ss');

    await job.updateProgress(40);

    // Log the execution
    const executionMessage = `${message} - Executed at ${nairobiTimeString}`;
    this.logger.log(executionMessage);
    console.log(`[NAIROBI DAILY TASK] ${executionMessage}`);

    await job.updateProgress(60);

    // Perform the actual task logic here
    // For example, you could:
    // - Send notifications
    // - Generate reports
    // - Perform system maintenance
    // - Update database records
    // - Call external APIs

    // Example: Log some system information
    const systemInfo = {
      executionTime: nairobiTimeString,
      timezone,
      localTime,
      utcTime: now.toISOString(),
      processId: process.pid,
      nodeVersion: process.version,
      platform: process.platform,
    };

    this.logger.log('Daily Nairobi task system info:', systemInfo);

    await job.updateProgress(80);

    // You can add more specific logic here based on your needs
    // For example, checking if it's a weekday, weekend, specific date, etc.
    const dayOfWeek = format(nairobiTime, 'EEEE');
    const isWeekend = ['Saturday', 'Sunday'].includes(dayOfWeek);

    let additionalActions: string[] = [];

    if (isWeekend) {
      additionalActions.push('Weekend maintenance tasks');
      this.logger.log('Executing weekend-specific tasks');
    } else {
      additionalActions.push('Weekday business tasks');
      this.logger.log('Executing weekday-specific tasks');
    }

    await job.updateProgress(100);

    return {
      type: 'daily-nairobi-task',
      message,
      executionTime: nairobiTimeString,
      timezone,
      localTime,
      utcTime: now.toISOString(),
      dayOfWeek,
      isWeekend,
      additionalActions,
      systemInfo,
      metadata,
      success: true,
    };
  }
}
