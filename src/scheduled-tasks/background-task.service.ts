import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ScheduledTaskService } from './scheduled-task.service';
import { QueueService } from '../queue/queue.service';
import { CreateScheduledTaskDto } from './dto/create-scheduled-task.dto';

@Injectable()
export class BackgroundTaskService {
  private readonly logger = new Logger(BackgroundTaskService.name);

  constructor(
    private readonly scheduledTaskService: ScheduledTaskService,
    @Inject(forwardRef(() => QueueService))
    private readonly queueService: QueueService,
  ) {}

  /**
   * Run a task immediately in the background
   * @param taskType - The type of task to run
   * @param payload - The task payload
   * @param options - Additional options
   */
  async runNow(
    taskType: string,
    payload: any,
    options: {
      name?: string;
      description?: string;
      priority?: number;
      maxAttempts?: number;
    } = {},
  ) {
    this.logger.log(`Triggering immediate task: ${taskType}`);

    const taskData: CreateScheduledTaskDto = {
      type: taskType,
      name: options.name || `Background ${taskType} task`,
      description:
        options.description || `Automatically triggered ${taskType} task`,
      payload,
      runAt: new Date(Date.now() + 1000), // Run in 1 second
      priority: options.priority || 5,
      maxAttempts: options.maxAttempts || 3,
    };

    // Create the scheduled task
    const task = await this.scheduledTaskService.create(taskData);

    // Add to queue for immediate processing
    await this.queueService.addScheduledTaskJob(
      {
        taskId: task.id,
        type: taskType,
        payload,
        attemptNumber: 1,
      },
      {
        priority: options.priority || 5,
        attempts: options.maxAttempts || 3,
      },
    );

    this.logger.log(`Task queued for immediate execution: ${task.id}`);
    return task;
  }

  /**
   * Schedule a task to run at a specific time
   * @param taskType - The type of task to run
   * @param payload - The task payload
   * @param runAt - When to run the task
   * @param options - Additional options
   */
  async schedule(
    taskType: string,
    payload: any,
    runAt: Date,
    options: {
      name?: string;
      description?: string;
      priority?: number;
      maxAttempts?: number;
      intervalType?: 'MINUTES' | 'HOURS' | 'DAYS' | 'WEEKS' | 'MONTHS';
      intervalValue?: number;
      cronExpression?: string;
    } = {},
  ) {
    this.logger.log(`Scheduling task: ${taskType} for ${runAt.toISOString()}`);

    const taskData: CreateScheduledTaskDto = {
      type: taskType,
      name: options.name || `Scheduled ${taskType} task`,
      description: options.description || `Scheduled ${taskType} task`,
      payload,
      runAt,
      priority: options.priority || 5,
      maxAttempts: options.maxAttempts || 3,
      intervalType: options.intervalType,
      intervalValue: options.intervalValue,
      cronExpression: options.cronExpression,
    };

    const task = await this.scheduledTaskService.create(taskData);
    this.logger.log(`Task scheduled successfully: ${task.id}`);
    return task;
  }

  /**
   * Schedule a recurring task
   * @param taskType - The type of task to run
   * @param payload - The task payload
   * @param cronExpression - Cron expression for scheduling
   * @param options - Additional options
   */
  async scheduleRecurring(
    taskType: string,
    payload: any,
    cronExpression: string,
    options: {
      name?: string;
      description?: string;
      priority?: number;
      maxAttempts?: number;
      startAt?: Date;
    } = {},
  ) {
    this.logger.log(
      `Scheduling recurring task: ${taskType} with cron: ${cronExpression}`,
    );

    const startAt = options.startAt || new Date();

    const taskData: CreateScheduledTaskDto = {
      type: taskType,
      name: options.name || `Recurring ${taskType} task`,
      description: options.description || `Recurring ${taskType} task`,
      payload,
      runAt: startAt,
      cronExpression,
      priority: options.priority || 5,
      maxAttempts: options.maxAttempts || 3,
    };

    const task = await this.scheduledTaskService.create(taskData);
    this.logger.log(`Recurring task scheduled successfully: ${task.id}`);
    return task;
  }

  /**
   * Schedule a task with interval-based recurrence
   * @param taskType - The type of task to run
   * @param payload - The task payload
   * @param intervalType - Type of interval
   * @param intervalValue - Interval value
   * @param options - Additional options
   */
  async scheduleInterval(
    taskType: string,
    payload: any,
    intervalType: 'MINUTES' | 'HOURS' | 'DAYS' | 'WEEKS' | 'MONTHS',
    intervalValue: number,
    options: {
      name?: string;
      description?: string;
      priority?: number;
      maxAttempts?: number;
      startAt?: Date;
    } = {},
  ) {
    this.logger.log(
      `Scheduling interval task: ${taskType} every ${intervalValue} ${intervalType}`,
    );

    const startAt = options.startAt || new Date();

    const taskData: CreateScheduledTaskDto = {
      type: taskType,
      name: options.name || `Interval ${taskType} task`,
      description:
        options.description || `Runs every ${intervalValue} ${intervalType}`,
      payload,
      runAt: startAt,
      intervalType,
      intervalValue,
      priority: options.priority || 5,
      maxAttempts: options.maxAttempts || 3,
    };

    const task = await this.scheduledTaskService.create(taskData);
    this.logger.log(`Interval task scheduled successfully: ${task.id}`);
    return task;
  }

  /**
   * Cancel a scheduled task
   * @param taskId - The ID of the task to cancel
   */
  async cancelTask(taskId: string) {
    this.logger.log(`Cancelling task: ${taskId}`);
    return await this.scheduledTaskService.cancel(taskId);
  }

  /**
   * Get task status
   * @param taskId - The ID of the task
   */
  async getTaskStatus(taskId: string) {
    return await this.scheduledTaskService.findOne(taskId);
  }

  /**
   * Get all tasks created by this service
   */
  async getTasks() {
    return await this.scheduledTaskService.findAll({});
  }
}
