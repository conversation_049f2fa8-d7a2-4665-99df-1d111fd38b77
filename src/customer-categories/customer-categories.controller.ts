import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CustomerCategoriesService } from './customer-categories.service';
import { CreateCustomerCategoryDto } from './dto/create-customer-category.dto';
import { UpdateCustomerCategoryDto } from './dto/update-customer-category.dto';
import { CustomerCategoryResponseDto } from './dto/customer-category-response.dto';
import { CustomerCategoryApiResponseDto } from './dto/customer-category-api-response.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';

/**
 * Controller handling all customer category-related HTTP endpoints
 * Provides RESTful API for customer category management
 */
@ApiTags('Customer Categories')
@Controller('customer-categories')
export class CustomerCategoriesController {
  constructor(
    private readonly customerCategoriesService: CustomerCategoriesService,
  ) {}

  /**
   * Get all customer categories (new API format)
   * GET /customer-categories
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all customer categories',
    description:
      'Returns all customer categories with fields: id, name, customers_count, added_by, added_on',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer categories fetched successfully',
    type: ApiResponseDto<CustomerCategoryApiResponseDto[]>,
  })
  async getAllCategories(): Promise<
    ApiResponseDto<CustomerCategoryApiResponseDto[]>
  > {
    return this.customerCategoriesService.findAllCategories();
  }

  /**
   * Create a new customer category (new API format)
   * POST /customer-categories
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new customer category',
    description:
      'Creates a new customer category. Only "name" is required, added_by is set from auth context, customers_count and added_on are auto-set.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Customer category created successfully',
    type: ApiResponseDto<CustomerCategoryApiResponseDto>,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Customer category name already exists' })
  async createCategory(
    @Body(ValidationPipe) createDto: CreateCustomerCategoryDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<CustomerCategoryApiResponseDto>> {
    return this.customerCategoriesService.create(createDto, req.user.id);
  }

  /**
   * Creates a new customer category (legacy endpoint - kept for backward compatibility)
   * POST /customer-categories/legacy
   */
  @Post('legacy')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new customer category (legacy)',
    description:
      'Creates a new customer category with the provided name. Category names must be unique (case-insensitive).',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Customer category created successfully',
    type: CustomerCategoryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Customer category name already exists' })
  async createLegacy(
    @Body(ValidationPipe) createCustomerCategoryDto: CreateCustomerCategoryDto,
  ): Promise<CustomerCategoryResponseDto> {
    // For legacy endpoint, we'll use a default user ID or handle differently
    // This would need to be updated based on your requirements
    throw new Error(
      'Legacy endpoint not implemented - use the new authenticated endpoint',
    );
  }

  /**
   * Retrieves all customer categories with pagination and search
   * GET /customer-categories
   */
  @Get()
  @ApiOperation({
    summary: 'Get all customer categories',
    description:
      'Retrieves a paginated list of customer categories with optional search functionality. Includes lead count for each category.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for category names',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer categories retrieved successfully',
    type: PaginatedResponseDto<CustomerCategoryResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<CustomerCategoryResponseDto>> {
    return this.customerCategoriesService.findAll(paginationDto);
  }

  /**
   * Retrieves a single customer category by ID
   * GET /customer-categories/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get customer category by ID',
    description:
      'Retrieves a single customer category by its UUID. Includes lead count and basic category information.',
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer category retrieved successfully',
    type: CustomerCategoryResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerCategoryResponseDto> {
    return this.customerCategoriesService.findOne(id);
  }

  /**
   * Updates an existing customer category
   * PATCH /customer-categories/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update customer category',
    description:
      'Updates an existing customer category. Only provided fields will be updated. Category names must remain unique.',
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer category updated successfully',
    type: CustomerCategoryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  @ApiConflictResponse({ description: 'Customer category name already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateCustomerCategoryDto: UpdateCustomerCategoryDto,
  ): Promise<CustomerCategoryResponseDto> {
    return this.customerCategoriesService.update(id, updateCustomerCategoryDto);
  }

  /**
   * Deletes a customer category
   * DELETE /customer-categories/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete customer category',
    description:
      'Deletes a customer category by ID. Cannot delete categories that have associated leads.',
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Customer category deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  @ApiConflictResponse({
    description: 'Customer category has associated leads and cannot be deleted',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.customerCategoriesService.remove(id);
  }

  /**
   * Retrieves all leads for a specific customer category
   * GET /customer-categories/:id/leads
   */
  @Get(':id/leads')
  @ApiOperation({
    summary: 'Get leads by customer category',
    description:
      'Retrieves all leads belonging to a specific customer category with pagination and search.',
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for customer name, client ID, or phone number',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leads retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  async getCategoryLeads(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ) {
    return this.customerCategoriesService.getCategoryLeads(id, paginationDto);
  }

  /**
   * Update customer category (new API format)
   * PATCH /customer-categories/:id
   */
  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update customer category',
    description:
      'Updates a customer category by ID. Only the name can be updated.',
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer category updated successfully',
    type: ApiResponseDto<CustomerCategoryApiResponseDto>,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  @ApiConflictResponse({ description: 'Customer category name already exists' })
  async updateCategory(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateCustomerCategoryDto,
  ): Promise<ApiResponseDto<CustomerCategoryApiResponseDto>> {
    return this.customerCategoriesService.updateCategory(id, updateDto);
  }

  /**
   * Delete customer category (new API format)
   * DELETE /customer-categories/:id
   */
  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete customer category',
    description:
      'Deletes a customer category by ID. Cannot delete categories that have associated leads.',
  })
  @ApiParam({ name: 'id', description: 'Customer category UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer category deleted successfully',
    type: ApiResponseDto<null>,
  })
  @ApiNotFoundResponse({ description: 'Customer category not found' })
  @ApiConflictResponse({
    description: 'Cannot delete category with associated leads',
  })
  async removeCategory(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<null>> {
    return this.customerCategoriesService.removeCategory(id);
  }
}
