import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for customer category API response
 * Follows the required API response format with success, message, and data
 */
export class CustomerCategoryApiResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the customer category',
    example: '7d9f6a45-2e83-42a9-9e7d-89ffb6e921ab',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the customer category',
    example: 'VIP',
  })
  name: string;

  @ApiProperty({
    description: 'Number of customers (leads) associated with this category',
    example: 5,
  })
  customers_count: number;

  @ApiProperty({
    description: 'Username of the user who added this category',
    example: 'admin',
  })
  added_by: string;

  @ApiProperty({
    description: 'Date when the category was added',
    example: '2025-08-20T09:15:00.000Z',
  })
  added_on: string;
}
