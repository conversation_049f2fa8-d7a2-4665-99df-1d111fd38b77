import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerCategoryDto } from './dto/create-customer-category.dto';
import { UpdateCustomerCategoryDto } from './dto/update-customer-category.dto';
import { CustomerCategoryResponseDto } from './dto/customer-category-response.dto';
import { CustomerCategoryApiResponseDto } from './dto/customer-category-api-response.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { Prisma } from '@prisma/client';

/**
 * Service responsible for handling all customer category-related business logic
 * Implements CRUD operations with optimized database queries
 */
@Injectable()
export class CustomerCategoriesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new customer category
   * @param createCustomerCategoryDto - Data for creating the customer category
   * @param userId - ID of the user creating the category
   * @returns Promise<ApiResponseDto<CustomerCategoryApiResponseDto>> - The created customer category
   * @throws ConflictException if category name already exists
   */
  async create(
    createCustomerCategoryDto: CreateCustomerCategoryDto,
    userId: string,
  ): Promise<ApiResponseDto<CustomerCategoryApiResponseDto>> {
    try {
      // Check if customer category with same name already exists
      const existingCategory = await this.prisma.customerCategory.findFirst({
        where: {
          name: {
            equals: createCustomerCategoryDto.name.trim(),
            mode: 'insensitive', // Case-insensitive comparison
          },
        },
      });

      if (existingCategory) {
        throw new ConflictException(
          `Customer category with name '${createCustomerCategoryDto.name}' already exists`,
        );
      }

      // Create the new customer category
      const category = await this.prisma.customerCategory.create({
        data: {
          name: createCustomerCategoryDto.name.trim(),
          added_by: userId,
        },
        include: {
          _count: {
            select: { leads: true }, // Get lead count for response
          },
          user: {
            select: { name: true },
          },
        },
      });

      const responseData: CustomerCategoryApiResponseDto = {
        id: category.id,
        name: category.name,
        customers_count: category._count.leads,
        added_by: category.user.name,
        added_on: category.created_at.toISOString(),
      };

      return ApiResponseDto.success(
        'Customer category created successfully',
        responseData,
      );
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create customer category');
    }
  }

  /**
   * Retrieves all customer categories (for new API format)
   * @returns Promise<ApiResponseDto<CustomerCategoryApiResponseDto[]>> - All customer categories
   */
  async findAllCategories(): Promise<
    ApiResponseDto<CustomerCategoryApiResponseDto[]>
  > {
    try {
      const categories = await this.prisma.customerCategory.findMany({
        include: {
          _count: {
            select: { leads: true }, // Include lead count
          },
          user: {
            select: { name: true },
          },
        },
        orderBy: {
          name: 'asc', // Sort by name alphabetically
        },
      });

      const data = categories.map((category) => ({
        id: category.id,
        name: category.name,
        customers_count: category._count.leads,
        added_by: category.user.name,
        added_on: category.created_at.toISOString(),
      }));

      return ApiResponseDto.success(
        'Customer categories fetched successfully',
        data,
      );
    } catch (error) {
      throw new BadRequestException('Failed to fetch customer categories');
    }
  }

  /**
   * Retrieves all customer categories with pagination and optional search
   * @param paginationDto - Pagination and search parameters
   * @returns Promise<PaginatedResponseDto<CustomerCategoryResponseDto>> - Paginated list of customer categories
   */
  async findAll(
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<CustomerCategoryResponseDto>> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build where clause for search functionality
    const whereClause: Prisma.CustomerCategoryWhereInput = search
      ? {
          name: {
            contains: search,
            mode: 'insensitive', // Case-insensitive search
          },
        }
      : {};

    // Execute both count and data queries in parallel for better performance
    const [categories, total] = await Promise.all([
      this.prisma.customerCategory.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: {
          _count: {
            select: { leads: true }, // Include lead count
          },
        },
        orderBy: {
          name: 'asc', // Sort by name alphabetically
        },
      }),
      this.prisma.customerCategory.count({
        where: whereClause,
      }),
    ]);

    // Transform data to response DTOs
    const data = categories.map((category) => ({
      id: category.id,
      name: category.name,
      leadCount: category._count.leads,
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves a single customer category by ID
   * @param id - The UUID of the customer category
   * @returns Promise<CustomerCategoryResponseDto> - The customer category data
   * @throws NotFoundException if customer category doesn't exist
   */
  async findOne(id: string): Promise<CustomerCategoryResponseDto> {
    const category = await this.prisma.customerCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { leads: true },
        },
      },
    });

    if (!category) {
      throw new NotFoundException(
        `Customer category with ID '${id}' not found`,
      );
    }

    return {
      id: category.id,
      name: category.name,
      leadCount: category._count.leads,
    };
  }

  /**
   * Updates an existing customer category
   * @param id - The UUID of the customer category to update
   * @param updateCustomerCategoryDto - Data for updating the customer category
   * @returns Promise<CustomerCategoryResponseDto> - The updated customer category
   * @throws NotFoundException if customer category doesn't exist
   * @throws ConflictException if new name conflicts with existing category
   */
  async update(
    id: string,
    updateCustomerCategoryDto: UpdateCustomerCategoryDto,
  ): Promise<CustomerCategoryResponseDto> {
    // Check if customer category exists
    const existingCategory = await this.prisma.customerCategory.findUnique({
      where: { id },
    });

    if (!existingCategory) {
      throw new NotFoundException(
        `Customer category with ID '${id}' not found`,
      );
    }

    // If name is being updated, check for conflicts
    if (
      updateCustomerCategoryDto.name &&
      updateCustomerCategoryDto.name !== existingCategory.name
    ) {
      const conflictingCategory = await this.prisma.customerCategory.findFirst({
        where: {
          name: {
            equals: updateCustomerCategoryDto.name,
            mode: 'insensitive',
          },
          id: {
            not: id, // Exclude current category from conflict check
          },
        },
      });

      if (conflictingCategory) {
        throw new ConflictException(
          `Customer category with name '${updateCustomerCategoryDto.name}' already exists`,
        );
      }
    }

    try {
      const updatedCategory = await this.prisma.customerCategory.update({
        where: { id },
        data: updateCustomerCategoryDto,
        include: {
          _count: {
            select: { leads: true },
          },
        },
      });

      return {
        id: updatedCategory.id,
        name: updatedCategory.name,
        leadCount: updatedCategory._count.leads,
      };
    } catch (error) {
      throw new BadRequestException('Failed to update customer category');
    }
  }

  /**
   * Deletes a customer category by ID
   * @param id - The UUID of the customer category to delete
   * @returns Promise<void>
   * @throws NotFoundException if customer category doesn't exist
   * @throws ConflictException if customer category has associated leads
   */
  async remove(id: string): Promise<void> {
    // Check if customer category exists and has leads
    const category = await this.prisma.customerCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { leads: true },
        },
      },
    });

    if (!category) {
      throw new NotFoundException(
        `Customer category with ID '${id}' not found`,
      );
    }

    // Prevent deletion if customer category has leads
    if (category._count.leads > 0) {
      throw new ConflictException(
        `Cannot delete customer category '${category.name}' because it has ${category._count.leads} associated lead(s). Please reassign or remove these leads first.`,
      );
    }

    try {
      await this.prisma.customerCategory.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException('Failed to delete customer category');
    }
  }
  /**
   * Retrieves all leads for a specific customer category
   * @param categoryId - The UUID of the customer category
   * @param paginationDto - Pagination parameters
   * @returns Promise<PaginatedResponseDto<any>> - Paginated list of leads
   * @throws NotFoundException if customer category doesn't exist
   */
  async getCategoryLeads(categoryId: string, paginationDto: PaginationDto) {
    // Verify customer category exists
    const category = await this.prisma.customerCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException(
        `Customer category with ID '${categoryId}' not found`,
      );
    }

    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: Prisma.LeadWhereInput = {
      customer_category_id: categoryId,
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ],
      }),
    };

    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        skip,
        take: limit,
        select: {
          id: true,
          customer_name: true,
          account_number: true,
          account_number_assigned_at: true,
          phone_number: true,
          type_of_lead: true,
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          customer_name: 'asc',
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: leads,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Updates a customer category (new API format)
   * @param id - The UUID of the customer category to update
   * @param updateDto - Data for updating the customer category
   * @returns Promise<ApiResponseDto<CustomerCategoryApiResponseDto>> - The updated customer category
   */
  async updateCategory(
    id: string,
    updateDto: UpdateCustomerCategoryDto,
  ): Promise<ApiResponseDto<CustomerCategoryApiResponseDto>> {
    try {
      // Check if category exists
      const existingCategory = await this.prisma.customerCategory.findUnique({
        where: { id },
        include: {
          user: {
            select: { name: true },
          },
        },
      });

      if (!existingCategory) {
        throw new NotFoundException('Customer category not found');
      }

      // Check if another category with the same name exists (case-insensitive)
      if (updateDto.name) {
        const duplicateCategory = await this.prisma.customerCategory.findFirst({
          where: {
            name: {
              equals: updateDto.name.trim(),
              mode: 'insensitive',
            },
            id: {
              not: id,
            },
          },
          select: { id: true, name: true },
        });

        if (duplicateCategory) {
          throw new ConflictException(
            `Customer category with name "${updateDto.name}" already exists`,
          );
        }
      }

      // Update the category
      const updatedCategory = await this.prisma.customerCategory.update({
        where: { id },
        data: {
          name: updateDto.name?.trim(),
        },
        include: {
          _count: {
            select: { leads: true },
          },
          user: {
            select: { name: true },
          },
        },
      });

      const responseData: CustomerCategoryApiResponseDto = {
        id: updatedCategory.id,
        name: updatedCategory.name,
        customers_count: updatedCategory._count.leads,
        added_by: updatedCategory.user.name,
        added_on: updatedCategory.created_at.toISOString(),
      };

      return ApiResponseDto.success(
        'Customer category updated successfully',
        responseData,
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update customer category');
    }
  }

  /**
   * Deletes a customer category (new API format)
   * @param id - The UUID of the customer category to delete
   * @returns Promise<ApiResponseDto<null>> - Success response
   */
  async removeCategory(id: string): Promise<ApiResponseDto<null>> {
    try {
      // Check if category exists
      const existingCategory = await this.prisma.customerCategory.findUnique({
        where: { id },
        include: {
          _count: {
            select: { leads: true },
          },
        },
      });

      if (!existingCategory) {
        throw new NotFoundException('Customer category not found');
      }

      // Check if category has associated leads
      if (existingCategory._count.leads > 0) {
        throw new ConflictException(
          'Cannot delete customer category that has associated leads',
        );
      }

      // Delete the category
      await this.prisma.customerCategory.delete({
        where: { id },
      });

      return ApiResponseDto.success(
        'Customer category deleted successfully',
        null,
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete customer category');
    }
  }
}
