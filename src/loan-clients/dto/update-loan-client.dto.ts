import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsDateString,
} from 'class-validator';

/**
 * Data Transfer Object for updating a loan client
 * All fields are optional for partial updates
 */
export class UpdateLoanClientDto {
  @ApiPropertyOptional({
    description: 'Customer name',
    example: 'John Doe Updated',
  })
  @IsOptional()
  @IsString({ message: 'Customer name must be a string' })
  customerName?: string;

  @ApiPropertyOptional({
    description: 'Phone number in international format',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Type of lead/client',
    example: 'Existing Customer',
  })
  @IsOptional()
  @IsString({ message: 'Type of lead must be a string' })
  typeOfLead?: string;

  @ApiPropertyOptional({
    description: 'Current status of the loan client',
    example: 'Active',
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'Account number (if already assigned)',
    example: 'ACC-2024-001234',
  })
  @IsOptional()
  @IsString({ message: 'Account number must be a string' })
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Date when account number was assigned',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Account number assigned date must be a valid date' })
  accountNumberAssignedAt?: string;

  // Foreign key relationships - can use either ID or identifier
  @ApiPropertyOptional({
    description: 'Anchor ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;

  @ApiPropertyOptional({
    description: 'Anchor name (alternative to anchorId)',
    example: 'ABC Company Ltd',
  })
  @IsOptional()
  @IsString({ message: 'Anchor identifier must be a string' })
  anchorIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Customer category ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customerCategoryId?: string;

  @ApiPropertyOptional({
    description: 'Customer category name (alternative to customerCategoryId)',
    example: 'Individual',
  })
  @IsOptional()
  @IsString({ message: 'Customer category identifier must be a string' })
  customerCategoryIdentifier?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isicSectorId?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector name (alternative to isicSectorId)',
    example: 'Manufacturing',
  })
  @IsOptional()
  @IsString({ message: 'ISIC sector identifier must be a string' })
  isicSectorIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Branch ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branchId?: string;

  @ApiPropertyOptional({
    description: 'Branch name (alternative to branchId)',
    example: 'Nairobi Branch',
  })
  @IsOptional()
  @IsString({ message: 'Branch identifier must be a string' })
  branchIdentifier?: string;

  @ApiPropertyOptional({
    description: 'RM user ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'RM user ID must be a valid UUID' })
  rmUserId?: string;

  @ApiPropertyOptional({
    description: 'RM code (alternative to rmUserId)',
    example: 'RM001',
  })
  @IsOptional()
  @IsString({ message: 'RM identifier must be a string' })
  rmIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Assigned user ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Assigned user ID must be a valid UUID' })
  assignedUser?: string;

  @ApiPropertyOptional({
    description: 'Employer ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employerId?: string;

  @ApiPropertyOptional({
    description: 'Employer name (alternative to employerId)',
    example: 'XYZ Corporation',
  })
  @IsOptional()
  @IsString({ message: 'Employer identifier must be a string' })
  employerIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship name (alternative to anchorRelationshipId)',
    example: 'Employee',
  })
  @IsOptional()
  @IsString({ message: 'Anchor relationship identifier must be a string' })
  anchorRelationshipIdentifier?: string;

  // Contact person information
  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'Jane Smith',
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  contactPersonPhone?: string;
}

/**
 * Data Transfer Object for loan client contact person operations
 */
export class CreateLoanClientContactPersonDto {
  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'Jane Smith',
  })
  @IsString({ message: 'Contact person name must be a string' })
  name: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
  })
  @IsString({ message: 'Contact person phone must be a string' })
  phoneNumber: string;
}

export class UpdateLoanClientContactPersonDto {
  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'Jane Smith Updated',
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  name?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  phoneNumber?: string;
}
