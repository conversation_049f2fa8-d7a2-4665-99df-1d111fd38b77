import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsNotEmpty,
  IsArray,
  ArrayNotEmpty,
  ValidateNested,
  IsPhoneNumber,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Data Transfer Object for creating a new loan client with the new format
 * Matches the structure of CreateLeadNewDto exactly
 */
export class CreateLoanClientNewDto {
  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>',
  })
  @IsString({ message: 'Customer name must be a string' })
  @IsNotEmpty({ message: 'Customer name is required' })
  customerName: string;

  @ApiPropertyOptional({
    description: 'Phone number in international format',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Type of lead/client',
    example: 'Existing Customer',
  })
  @IsOptional()
  @IsString({ message: 'Type of lead must be a string' })
  leadType?: string;

  @ApiPropertyOptional({
    description: 'Current status of the loan client',
    example: 'Active',
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'Branch ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branchId?: string;

  @ApiPropertyOptional({
    description: 'Customer category ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customerCategoryId?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isicSectorId?: string;

  @ApiPropertyOptional({
    description: 'Employer ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employerId?: string;

  @ApiPropertyOptional({
    description: 'Employer name (alternative to employerId)',
    example: 'ABC Company Ltd',
  })
  @IsOptional()
  @IsString({ message: 'Employer name must be a string' })
  employerName?: string;

  @ApiPropertyOptional({
    description: 'Parent loan client ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Parent loan client ID must be a valid UUID' })
  parentLoanClientId?: string;

  @ApiPropertyOptional({
    description: 'Anchor ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;

  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'Jane Smith',
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  contactPersonPhone?: string;

  @ApiPropertyOptional({
    description: 'Creation date in ISO format',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created date must be a valid ISO date string' })
  createdDate?: string;
}

/**
 * Data Transfer Object for creating a new loan client
 * Supports flexible foreign key handling with both IDs and identifiers
 */
export class CreateLoanClientDto {
  @ApiProperty({
    description: 'Customer name',
    example: 'John Doe',
  })
  @IsString({ message: 'Customer name must be a string' })
  @IsNotEmpty({ message: 'Customer name is required' })
  customerName: string;

  @ApiPropertyOptional({
    description: 'Phone number in international format',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Type of lead/client',
    example: 'Existing Customer',
  })
  @IsOptional()
  @IsString({ message: 'Type of lead must be a string' })
  typeOfLead?: string;

  @ApiPropertyOptional({
    description: 'Current status of the loan client',
    example: 'Active',
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'Account number (if already assigned)',
    example: 'ACC-2024-001234',
  })
  @IsOptional()
  @IsString({ message: 'Account number must be a string' })
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Date when account number was assigned',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Account number assigned date must be a valid date' })
  accountNumberAssignedAt?: string;

  // Foreign key relationships - can use either ID or identifier
  @ApiPropertyOptional({
    description: 'Anchor ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;

  @ApiPropertyOptional({
    description: 'Anchor name (alternative to anchorId)',
    example: 'ABC Company Ltd',
  })
  @IsOptional()
  @IsString({ message: 'Anchor identifier must be a string' })
  anchorIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Customer category ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customerCategoryId?: string;

  @ApiPropertyOptional({
    description: 'Customer category name (alternative to customerCategoryId)',
    example: 'Individual',
  })
  @IsOptional()
  @IsString({ message: 'Customer category identifier must be a string' })
  customerCategoryIdentifier?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isicSectorId?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector name (alternative to isicSectorId)',
    example: 'Manufacturing',
  })
  @IsOptional()
  @IsString({ message: 'ISIC sector identifier must be a string' })
  isicSectorIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Branch ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branchId?: string;

  @ApiPropertyOptional({
    description: 'Branch name (alternative to branchId)',
    example: 'Nairobi Branch',
  })
  @IsOptional()
  @IsString({ message: 'Branch identifier must be a string' })
  branchIdentifier?: string;

  @ApiPropertyOptional({
    description: 'RM user ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'RM user ID must be a valid UUID' })
  rmUserId?: string;

  @ApiPropertyOptional({
    description: 'RM code (alternative to rmUserId)',
    example: 'RM001',
  })
  @IsOptional()
  @IsString({ message: 'RM identifier must be a string' })
  rmIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Assigned user ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Assigned user ID must be a valid UUID' })
  assignedUser?: string;

  @ApiPropertyOptional({
    description: 'Employer ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employerId?: string;

  @ApiPropertyOptional({
    description: 'Employer name (alternative to employerId)',
    example: 'XYZ Corporation',
  })
  @IsOptional()
  @IsString({ message: 'Employer identifier must be a string' })
  employerIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship name (alternative to anchorRelationshipId)',
    example: 'Employee',
  })
  @IsOptional()
  @IsString({ message: 'Anchor relationship identifier must be a string' })
  anchorRelationshipIdentifier?: string;

  // Contact person information
  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'Jane Smith',
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  contactPersonPhone?: string;
}

/**
 * Data Transfer Object for flexible loan client creation
 * Used for bulk operations and Excel imports
 * Supports both ID-based and identifier-based foreign key relationships
 */
export class CreateLoanClientFlexibleDto {
  @ApiProperty({
    description: 'Customer name',
    example: 'John Doe',
  })
  @IsString({ message: 'Customer name must be a string' })
  @IsNotEmpty({ message: 'Customer name is required' })
  customerName: string;

  @ApiPropertyOptional({
    description: 'Phone number in international format',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Type of lead/client',
    example: 'Existing Customer',
  })
  @IsOptional()
  @IsString({ message: 'Type of lead must be a string' })
  leadType?: string;

  @ApiPropertyOptional({
    description: 'Current status of the loan client',
    example: 'Active',
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  leadStatus?: string;

  // Foreign key IDs (direct references)
  @ApiPropertyOptional({
    description: 'Branch ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branchId?: string;

  @ApiPropertyOptional({
    description: 'Customer category ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customerCategoryId?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isicSectorId?: string;

  @ApiPropertyOptional({
    description: 'Employer ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employerId?: string;

  @ApiPropertyOptional({
    description: 'Parent loan client ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Parent loan client ID must be a valid UUID' })
  parentLoanClientId?: string;

  @ApiPropertyOptional({
    description: 'Anchor ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;

  @ApiPropertyOptional({
    description: 'RM User ID (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'RM User ID must be a valid UUID' })
  rmUserId?: string;

  @ApiPropertyOptional({
    description: 'Assigned user',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString({ message: 'Assigned user must be a string' })
  assignedUser?: string;

  @ApiPropertyOptional({
    description: 'Account number',
    example: 'ACC-2024-001',
  })
  @IsOptional()
  @IsString({ message: 'Account number must be a string' })
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Account number assigned date in ISO format',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Account number assigned date must be a valid ISO date string' })
  accountNumberAssignedAt?: string;

  // Foreign key identifiers (names/codes for flexible resolution)
  @ApiPropertyOptional({
    description: 'Branch identifier (name or UUID)',
    example: 'Nairobi Branch',
  })
  @IsOptional()
  @IsString({ message: 'Branch identifier must be a string' })
  branchIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Customer category identifier (name or UUID)',
    example: 'Individual',
  })
  @IsOptional()
  @IsString({ message: 'Customer category identifier must be a string' })
  customerCategoryIdentifier?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector identifier (name or UUID)',
    example: 'Manufacturing',
  })
  @IsOptional()
  @IsString({ message: 'ISIC sector identifier must be a string' })
  isicSectorIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Employer identifier (name or UUID)',
    example: 'ABC Company Ltd',
  })
  @IsOptional()
  @IsString({ message: 'Employer identifier must be a string' })
  employerIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship identifier (name or UUID)',
    example: 'Employee',
  })
  @IsOptional()
  @IsString({ message: 'Anchor relationship identifier must be a string' })
  anchorRelationshipIdentifier?: string;

  // Contact person information
  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'Jane Smith',
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  contactPersonPhone?: string;

  @ApiPropertyOptional({
    description: 'Creation date in ISO format',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created date must be a valid ISO date string' })
  createdDate?: string;
}

/**
 * Data Transfer Object for bulk loan client creation
 */
export class BulkCreateLoanClientsDto {
  @ApiProperty({
    description: 'Array of loan clients to create',
    type: [CreateLoanClientFlexibleDto],
    example: [
      {
        customerName: 'John Doe',
        phoneNumber: '+************',
        branchIdentifier: 'Nairobi Branch',
        customerCategoryIdentifier: 'Individual',
        employerIdentifier: 'ABC Company',
        isicSectorIdentifier: 'Manufacturing',
        leadType: 'Existing Customer',
        leadStatus: 'Active'
      }
    ],
  })
  @IsArray({ message: 'Loan clients must be an array' })
  @ArrayNotEmpty({ message: 'At least one loan client is required' })
  @ValidateNested({ each: true })
  @Type(() => CreateLoanClientFlexibleDto)
  loanClients: CreateLoanClientFlexibleDto[];
}

/**
 * Data Transfer Object for Excel file upload
 */
export class ExcelUploadDto {
  // No additional fields needed - anchor information comes from Excel file
}

/**
 * Data Transfer Object for Excel file upload response
 */
export class ExcelUploadResponseDto {
  @ApiProperty({
    description: 'Whether the upload was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Summary message of the upload operation',
    example: 'Successfully processed 50 rows from Excel file',
  })
  message: string;

  @ApiProperty({
    description: 'Total number of rows found in Excel file',
    example: 52,
  })
  totalRows: number;

  @ApiProperty({
    description: 'Number of data rows processed (excluding headers)',
    example: 50,
  })
  processedRows: number;

  @ApiProperty({
    description: 'Number of loan clients successfully created',
    example: 45,
  })
  successfulCreations: number;

  @ApiProperty({
    description: 'Number of loan clients that failed to create',
    example: 5,
  })
  failedCreations: number;

  @ApiProperty({
    description: 'Array of successfully created loan clients',
    type: [Object],
  })
  createdLoanClients: any[];

  @ApiProperty({
    description: 'Array of parsing and creation errors',
    example: [
      {
        row: 3,
        error: 'Customer name is required',
        data: {}
      }
    ],
  })
  errors: Array<{
    row: number;
    error: string;
    data: any;
  }>;

  @ApiProperty({
    description: 'Detected column mappings from Excel headers',
    example: {
      'Customer Name': 'customerName',
      'Phone Number': 'phoneNumber',
      'Branch': 'branchIdentifier',
    },
  })
  columnMappings: Record<string, string>;

  @ApiProperty({
    description: 'Anchor creation and matching results',
    example: [
      {
        row: 2,
        anchorName: 'ABC Company',
        action: 'created',
        anchorId: '550e8400-e29b-41d4-a716-************'
      },
      {
        row: 3,
        anchorName: 'XYZ Corp',
        action: 'matched',
        anchorId: '550e8400-e29b-41d4-a716-************',
        similarity: 85,
        matchedAnchorName: 'XYZ Corporation'
      }
    ],
  })
  anchorCreationResults: Array<{
    row: number;
    anchorName: string;
    action: 'matched' | 'created' | 'skipped';
    anchorId?: string;
    similarity?: number;
    matchedAnchorName?: string;
  }>;
}
