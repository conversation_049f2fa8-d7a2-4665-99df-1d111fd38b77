import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for loan client contact person response
 */
export class LoanClientContactPersonResponseDto {
  @ApiProperty({
    description: 'Contact person ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Contact person name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Contact person phone number',
    example: '+254712345678',
  })
  phoneNumber: string;
}

/**
 * Data Transfer Object for loan client summary response
 * Used in list views and after creation/update operations
 */
export class LoanClientSummaryResponseDto {
  @ApiProperty({
    description: 'Loan client ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>',
  })
  customerName: string;

  @ApiPropertyOptional({
    description: 'Phone number',
    example: '+************',
  })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Type of lead/client',
    example: 'Existing Customer',
  })
  typeOfLead?: string;

  @ApiPropertyOptional({
    description: 'Current status',
    example: 'Active',
  })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'Account number',
    example: 'ACC-2024-001234',
  })
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Date when account number was assigned',
    example: '2024-01-15T10:30:00.000Z',
  })
  accountNumberAssignedAt?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update date',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: string;

  // Related entities
  @ApiPropertyOptional({
    description: 'Anchor information',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'ABC Company Ltd',
      email: '<EMAIL>',
      phoneNumber: '+************'
    },
  })
  anchor?: {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
  };

  @ApiPropertyOptional({
    description: 'Branch information',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Nairobi Branch',
      region: {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Central Region'
      }
    },
  })
  branch?: {
    id: string;
    name: string;
    region?: {
      id: string;
      name: string;
    };
  };

  @ApiPropertyOptional({
    description: 'RM user information',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'John Smith',
      rmCode: 'RM001'
    },
  })
  rmUser?: {
    id: string;
    name: string;
    rmCode: string;
  };

  @ApiPropertyOptional({
    description: 'Customer category information',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440005',
      name: 'Individual'
    },
  })
  customerCategory?: {
    id: string;
    name: string;
  };

  @ApiPropertyOptional({
    description: 'ISIC sector information',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440006',
      code: 'C10',
      name: 'Manufacturing'
    },
  })
  isicSector?: {
    id: string;
    code?: string;
    name: string;
  };

  @ApiPropertyOptional({
    description: 'Employer information',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440007',
      name: 'XYZ Corporation'
    },
  })
  employer?: {
    id: string;
    name: string;
  };

  @ApiPropertyOptional({
    description: 'Anchor relationship information',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440008',
      name: 'Employee'
    },
  })
  anchorRelationship?: {
    id: string;
    name: string;
  };

  // Activity counts and statistics
  @ApiProperty({
    description: 'Total number of loan activities',
    example: 15,
  })
  totalLoanActivities: number;

  @ApiPropertyOptional({
    description: 'Date of last loan activity',
    example: '2024-01-10T14:30:00.000Z',
  })
  lastLoanActivityDate?: string;

  @ApiProperty({
    description: 'Number of contact persons',
    example: 2,
  })
  contactPersonsCount: number;

  @ApiPropertyOptional({
    description: 'Last interaction (activity) details',
    type: 'object',
    properties: {
      activity_type: { type: 'string', example: 'call' },
      date_time: { type: 'string', example: '2024-01-15T10:30:00.000Z' },
    },
  })
  lastInteraction?: {
    activity_type: string;
    date_time: string;
  };
}

/**
 * Data Transfer Object for detailed loan client response
 * Used when retrieving a single loan client with full details
 */
export class LoanClientResponseDto extends LoanClientSummaryResponseDto {
  @ApiProperty({
    description: 'Contact persons for this loan client',
    type: [LoanClientContactPersonResponseDto],
  })
  contactPersons: LoanClientContactPersonResponseDto[];

  @ApiPropertyOptional({
    description: 'Assigned user information',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Jane Doe',
      rmCode: 'RM002'
    },
  })
  assignedUser?: {
    id: string;
    name: string;
    rmCode: string;
  };

  @ApiProperty({
    description: 'Recent loan activities (last 5)',
    type: [Object],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        loanAccountNumber: 'LOAN-2024-001',
        purpose: 'Payment Review',
        loanBalance: 50000.00,
        arrearsDays: 0,
        comment: 'Regular payment received',
        createdAt: '2024-01-10T14:30:00.000Z',
        rmUser: {
          id: '550e8400-e29b-41d4-a716-************',
          name: 'John Smith',
          rmCode: 'RM001'
        }
      }
    ],
  })
  recentLoanActivities: Array<{
    id: string;
    loanAccountNumber?: string;
    purpose?: string;
    loanBalance?: number;
    arrearsDays?: number;
    comment?: string;
    createdAt: string;
    rmUser: {
      id: string;
      name: string;
      rmCode: string;
    };
  }>;
}
