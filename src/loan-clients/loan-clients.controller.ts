import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  Header,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { LoanClientsService } from './loan-clients.service';
import {
  CreateLoanClientDto,
  CreateLoanClientNewDto,
  BulkCreateLoanClientsDto,
  ExcelUploadResponseDto,
  ExcelUploadDto
} from './dto/create-loan-client.dto';
import { UpdateLoanClientDto } from './dto/update-loan-client.dto';
import { 
  LoanClientResponseDto, 
  LoanClientSummaryResponseDto 
} from './dto/loan-client-response.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

/**
 * Controller handling all loan client-related HTTP endpoints
 * Provides RESTful API for loan client management
 */
@ApiTags('Loan Clients')
@Controller('loan-clients')
export class LoanClientsController {
  constructor(private readonly loanClientsService: LoanClientsService) {}

  /**
   * Creates a new loan client with the new format and returns summary data
   * POST /loan-clients/new-format
   */
  @Post('new-format')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new loan client with new format',
    description:
      'Creates a new loan client with the new format and returns summary data including activity counts and last interaction information.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Loan client created successfully',
    type: LoanClientSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description:
      'Referenced entity not found (customer category, ISIC sector, branch, or employer)',
  })
  @ApiConflictResponse({ description: 'Client ID already exists' })
  async createNewFormat(
    @Body(ValidationPipe) createLoanClientNewDto: CreateLoanClientNewDto,
  ): Promise<LoanClientSummaryResponseDto> {
    return this.loanClientsService.createNewFormat(createLoanClientNewDto);
  }

  /**
   * Creates a new loan client and returns summary data
   * POST /loan-clients
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new loan client',
    description:
      'Creates a new loan client with flexible input format and returns summary data including activity counts and last interaction.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Loan client created successfully',
    type: LoanClientSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description:
      'Referenced entity not found (customer category, ISIC sector, branch, or employer)',
  })
  @ApiConflictResponse({ description: 'Account number already exists' })
  async create(
    @Body(ValidationPipe) createLoanClientDto: CreateLoanClientDto,
  ): Promise<LoanClientSummaryResponseDto> {
    return this.loanClientsService.create(createLoanClientDto);
  }

  /**
   * Creates multiple loan clients in bulk
   * POST /loan-clients/bulk
   */
  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create multiple loan clients in bulk',
    description:
      'Creates multiple loan clients at once with flexible foreign key handling. Returns detailed results including successes and failures.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk creation completed',
    schema: {
      type: 'object',
      properties: {
        totalProcessed: { type: 'number', example: 10 },
        totalCreated: { type: 'number', example: 8 },
        totalFailed: { type: 'number', example: 2 },
        createdLoanClients: {
          type: 'array',
          items: { $ref: '#/components/schemas/LoanClientSummaryResponseDto' },
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              index: { type: 'number' },
              error: { type: 'string' },
              loanClientData: { type: 'object' },
            },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Some loan clients could not be created due to conflicts' })
  async createBulk(
    @Body(ValidationPipe) bulkCreateLoanClientsDto: BulkCreateLoanClientsDto,
  ) {
    return this.loanClientsService.createBulk(bulkCreateLoanClientsDto);
  }

  /**
   * Creates loan clients from an uploaded Excel file
   * POST /loan-clients/upload-excel
   */
  @Post('upload-excel')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FileInterceptor('file', {
    fileFilter: (req, file, callback) => {
      if (!file.originalname.match(/\.(xlsx|xls)$/)) {
        return callback(new BadRequestException('Only Excel files (.xlsx, .xls) are allowed'), false);
      }
      callback(null, true);
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Excel file containing loan client data',
    type: ExcelUploadDto,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel file (.xlsx or .xls)',
        },
      },
    },
  })
  @ApiOperation({
    summary: 'Create loan clients from Excel file',
    description:
      'Uploads an Excel file and creates loan clients from the data. Supports intelligent column mapping, anchor matching/creation, and comprehensive error reporting.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Excel file processed successfully',
    type: ExcelUploadResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or parsing errors'
  })
  @ApiConflictResponse({
    description: 'Some loan clients could not be created due to conflicts'
  })
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ExcelUploadResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.loanClientsService.createFromExcel(file);
  }

  /**
   * Retrieves all loan clients with pagination and search
   * GET /loan-clients
   */
  @Get()
  @ApiOperation({
    summary: 'Get all loan clients',
    description:
      'Retrieves a paginated list of loan clients with optional search functionality. Search works on customer name, account number, phone number, type of lead, RM name, and branch name. Includes comprehensive relationship data.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description:
      'Search term for customer name, account number, phone number, type of lead, RM name, or branch name',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Loan clients retrieved successfully',
    type: PaginatedResponseDto<LoanClientSummaryResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<LoanClientSummaryResponseDto>> {
    return this.loanClientsService.findAll(paginationDto);
  }

  /**
   * Exports loan clients to Excel file
   * GET /loan-clients/export-excel
   */
  @Get('export-excel')
  @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  @Header('Content-Disposition', 'attachment; filename="loan-clients-export.xlsx"')
  @ApiOperation({
    summary: 'Export loan clients to Excel',
    description:
      'Exports all loan clients to an Excel file with comprehensive data including relationships and activity counts. Supports optional search filtering.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Optional search term to filter exported loan clients',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Excel file generated successfully',
    schema: {
      type: 'string',
      format: 'binary',
    },
  })
  async exportExcel(
    @Query('search') search?: string,
    @Res() res?: Response,
  ) {
    const buffer = await this.loanClientsService.exportLoanClientsToExcel(search);
    
    if (res) {
      res.set({
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="loan-clients-export.xlsx"',
        'Content-Length': buffer.length.toString(),
      });
      res.end(buffer);
    }
    
    return buffer;
  }

  /**
   * Retrieves a single loan client by ID
   * GET /loan-clients/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get loan client by ID',
    description:
      'Retrieves a single loan client with comprehensive relationship data and activity counts. Includes all contact persons and recent loan activities.',
  })
  @ApiParam({ name: 'id', description: 'Loan client UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Loan client retrieved successfully',
    type: LoanClientResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Loan client not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<LoanClientResponseDto> {
    return this.loanClientsService.findOne(id);
  }

  /**
   * Updates a loan client
   * PATCH /loan-clients/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update loan client',
    description:
      'Updates a loan client with partial data. Supports flexible foreign key handling and validates all relationships.',
  })
  @ApiParam({ name: 'id', description: 'Loan client UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Loan client updated successfully',
    type: LoanClientSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Loan client not found' })
  @ApiConflictResponse({ description: 'Account number already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateLoanClientDto: UpdateLoanClientDto,
  ): Promise<LoanClientSummaryResponseDto> {
    return this.loanClientsService.update(id, updateLoanClientDto);
  }

  /**
   * Deletes a loan client
   * DELETE /loan-clients/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete loan client',
    description:
      'Deletes a loan client by ID. Cannot delete loan clients that have associated loan activities. All contact persons are also deleted.',
  })
  @ApiParam({ name: 'id', description: 'Loan client UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Loan client deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Loan client not found' })
  @ApiBadRequestResponse({
    description: 'Cannot delete loan client with existing dependencies',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.loanClientsService.remove(id);
  }
}
