import { Module } from '@nestjs/common';
import { LoanClientsService } from './loan-clients.service';
import { LoanClientsController } from './loan-clients.controller';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for handling loan client-related functionality
 * Provides operations for creating and managing loan clients
 */
@Module({
  imports: [PrismaModule],
  controllers: [LoanClientsController],
  providers: [LoanClientsService],
  exports: [LoanClientsService],
})
export class LoanClientsModule {}
