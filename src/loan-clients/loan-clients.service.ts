import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '@prisma/client';
import {
  CreateLoanClientDto,
  CreateLoanClientNewDto,
  BulkCreateLoanClientsDto,
  CreateLoanClientFlexibleDto,
  ExcelUploadResponseDto,
} from './dto/create-loan-client.dto';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { Express } from 'express';
import { UpdateLoanClientDto } from './dto/update-loan-client.dto';
import {
  CreateLoanClientContactPersonDto,
  UpdateLoanClientContactPersonDto,
} from './dto/update-loan-client.dto';
import {
  LoanClientSummaryResponseDto,
  LoanClientResponseDto,
} from './dto/loan-client-response.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';

/**
 * Service handling all loan client-related business logic
 * Provides CRUD operations, Excel import/export, and relationship management
 */
@Injectable()
export class LoanClientsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new loan client with the new format and returns summary data
   * Includes activity counts and last interaction information
   */
  async createNewFormat(
    createLoanClientNewDto: CreateLoanClientNewDto,
  ): Promise<LoanClientSummaryResponseDto> {
    const {
      branchId,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryId,
      customerName,
      employerId,
      employerName,
      isicSectorId,
      leadType,
      leadStatus,
      phoneNumber,
      parentLoanClientId,
      anchorRelationshipId,
      anchorId,
    } = createLoanClientNewDto;

    // Clean up empty strings and validate UUIDs
    const cleanBranchId =
      branchId && branchId.trim() !== '' ? branchId : undefined;
    const cleanCustomerCategoryId =
      customerCategoryId && customerCategoryId.trim() !== ''
        ? customerCategoryId
        : undefined;
    const cleanIsicSectorId =
      isicSectorId && isicSectorId.trim() !== '' ? isicSectorId : undefined;
    const cleanEmployerId =
      employerId && employerId.trim() !== '' ? employerId : undefined;
    const cleanParentLoanClientId =
      parentLoanClientId && parentLoanClientId.trim() !== ''
        ? parentLoanClientId
        : undefined;
    const cleanAnchorRelationshipId =
      anchorRelationshipId && anchorRelationshipId.trim() !== ''
        ? anchorRelationshipId
        : undefined;
    const cleanAnchorId =
      anchorId && anchorId.trim() !== '' ? anchorId : undefined;

    // Validate foreign key relationships exist
    if (cleanBranchId) {
      const branch = await this.prisma.branch.findUnique({
        where: { id: cleanBranchId },
      });
      if (!branch) {
        throw new NotFoundException(
          `Branch with ID '${cleanBranchId}' not found`,
        );
      }
    }

    if (cleanCustomerCategoryId) {
      const category = await this.prisma.customerCategory.findUnique({
        where: { id: cleanCustomerCategoryId },
      });
      if (!category) {
        throw new NotFoundException(
          `Customer category with ID '${cleanCustomerCategoryId}' not found`,
        );
      }
    }

    if (cleanIsicSectorId) {
      const sector = await this.prisma.iSICSector.findUnique({
        where: { id: cleanIsicSectorId },
      });
      if (!sector) {
        throw new NotFoundException(
          `ISIC sector with ID '${cleanIsicSectorId}' not found`,
        );
      }
    }

    // Handle employer - either by ID or create new one by name
    let finalEmployerId = cleanEmployerId;
    if (!finalEmployerId && employerName && employerName.trim() !== '') {
      // Check if employer with this name already exists
      const existingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: employerName.trim(),
            mode: 'insensitive',
          },
        },
      });

      if (existingEmployer) {
        finalEmployerId = existingEmployer.id;
      } else {
        // Create new employer
        const newEmployer = await this.prisma.employer.create({
          data: {
            name: employerName.trim(),
          },
        });
        finalEmployerId = newEmployer.id;
      }
    } else if (finalEmployerId) {
      // Validate employer ID exists
      const employer = await this.prisma.employer.findUnique({
        where: { id: finalEmployerId },
      });
      if (!employer) {
        throw new NotFoundException(
          `Employer with ID '${finalEmployerId}' not found`,
        );
      }
    }

    if (cleanParentLoanClientId) {
      const parentLoanClient = await this.prisma.loanClient.findUnique({
        where: { id: cleanParentLoanClientId },
      });
      if (!parentLoanClient) {
        throw new NotFoundException(
          `Parent loan client with ID '${cleanParentLoanClientId}' not found`,
        );
      }
    }

    if (cleanAnchorRelationshipId) {
      const anchorRelationship =
        await this.prisma.anchorRelationship.findUnique({
          where: { id: cleanAnchorRelationshipId },
        });
      if (!anchorRelationship) {
        throw new NotFoundException(
          `Anchor relationship with ID '${cleanAnchorRelationshipId}' not found`,
        );
      }
    }

    if (cleanAnchorId) {
      const anchor = await this.prisma.anchor.findUnique({
        where: { id: cleanAnchorId },
      });
      if (!anchor) {
        throw new NotFoundException(
          `Anchor with ID '${cleanAnchorId}' not found`,
        );
      }
    }

    // Get RM user (same logic as leads service)
    let rmUser;
    if (cleanBranchId) {
      rmUser = await this.prisma.user.findFirst({
        where: { branch_id: cleanBranchId },
      });
    } else {
      rmUser = await this.prisma.user.findFirst();
    }

    // Create loan client with contact person in a transaction
    const loanClient = await this.prisma.$transaction(async (tx) => {
      const newLoanClient = await tx.loanClient.create({
        data: {
          customer_name: customerName || 'Unknown Customer',
          customer_category_id: cleanCustomerCategoryId || undefined,
          isic_sector_id: cleanIsicSectorId || undefined,
          phone_number: phoneNumber || undefined,
          type_of_lead: leadType || 'New',
          lead_status: leadStatus || 'Pending',
          branch_id: cleanBranchId || undefined,
          rm_user_id: rmUser?.id || undefined,
          employer_id: finalEmployerId || undefined,
          anchor_relationship_id: cleanAnchorRelationshipId || undefined,
          anchor_id: cleanAnchorId || cleanParentLoanClientId || undefined,
          contact_persons:
            contactPersonName && contactPersonPhone
              ? {
                  create: [
                    {
                      name: contactPersonName,
                      phone_number: contactPersonPhone,
                    },
                  ],
                }
              : undefined,
        },
      });

      return newLoanClient;
    });

    // Return summary data
    return {
      id: loanClient.id,
      customerName: customerName || 'Unknown Customer',
      phoneNumber: phoneNumber || '',
      typeOfLead: leadType || 'New',
      leadStatus: leadStatus || 'Pending',
      accountNumber: undefined, // New loan clients don't have account numbers
      accountNumberAssignedAt: undefined, // New loan clients haven't been converted
      createdAt: loanClient.created_at.toISOString(),
      updatedAt: loanClient.updated_at.toISOString(),
      // Activity counts (new loan clients start with 0)
      totalLoanActivities: 0,
      lastLoanActivityDate: undefined,
      contactPersonsCount: contactPersonName && contactPersonPhone ? 1 : 0,
      lastInteraction: undefined, // New loan clients have no interactions yet
      // Relationship information
      branch: undefined,
      customerCategory: undefined,
      isicSector: undefined,
      employer: undefined,
      anchor: undefined,
      anchorRelationship: undefined,
      rmUser: undefined,
    };
  }

  /**
   * Creates a new loan client with flexible foreign key handling
   * Supports both ID-based and identifier-based relationships
   */
  async create(
    createLoanClientDto: CreateLoanClientDto,
  ): Promise<LoanClientSummaryResponseDto> {
    try {
      // Resolve foreign key relationships
      const resolvedData = await this.resolveForeignKeys(createLoanClientDto);

      // Check for duplicate account number if provided
      if (resolvedData.accountNumber) {
        const existingClient = await this.prisma.loanClient.findUnique({
          where: { account_number: resolvedData.accountNumber },
        });

        if (existingClient) {
          throw new ConflictException(
            `Loan client with account number '${resolvedData.accountNumber}' already exists`,
          );
        }
      }

      // Create loan client
      const loanClient = await this.prisma.loanClient.create({
        data: {
          customer_name: resolvedData.customerName,
          phone_number: resolvedData.phoneNumber,
          type_of_lead: resolvedData.typeOfLead,
          lead_status: resolvedData.leadStatus,
          account_number: resolvedData.accountNumber,
          account_number_assigned_at: resolvedData.accountNumberAssignedAt
            ? new Date(resolvedData.accountNumberAssignedAt)
            : null,
          anchor_id: resolvedData.anchorId,
          customer_category_id: resolvedData.customerCategoryId,
          isic_sector_id: resolvedData.isicSectorId,
          branch_id: resolvedData.branchId,
          rm_user_id: resolvedData.rmUserId,
          assigned_user: resolvedData.assignedUser,
          employer_id: resolvedData.employerId,
          anchor_relationship_id: resolvedData.anchorRelationshipId,
        },
        include: this.getLoanClientIncludeOptions(),
      });

      // Create contact person if provided
      if (resolvedData.contactPersonName && resolvedData.contactPersonPhone) {
        await this.prisma.loanClientContactPerson.create({
          data: {
            loan_client_id: loanClient.id,
            name: resolvedData.contactPersonName,
            phone_number: resolvedData.contactPersonPhone,
          },
        });
      }

      return this.transformToSummaryResponse(loanClient);
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create loan client: ${error.message}`,
      );
    }
  }

  /**
   * Creates multiple loan clients in bulk
   * Handles validation and error reporting for each client
   * Matches the leads service implementation exactly
   */
  async createBulk(bulkCreateDto: BulkCreateLoanClientsDto) {
    const { loanClients } = bulkCreateDto;
    const results = {
      totalProcessed: loanClients.length,
      totalCreated: 0,
      totalFailed: 0,
      createdLoanClients: [] as LoanClientSummaryResponseDto[],
      errors: [] as Array<{
        index: number;
        error: string;
        loanClientData: CreateLoanClientFlexibleDto;
      }>,
    };

    // Process in batches to avoid overwhelming the database
    const batchSize = 10;
    const createdLoanClients: LoanClientSummaryResponseDto[] = [];
    const errors: Array<{
      index: number;
      error: string;
      loanClientData: CreateLoanClientFlexibleDto;
    }> = [];

    for (let i = 0; i < loanClients.length; i += batchSize) {
      const batch = loanClients.slice(i, i + batchSize);

      // Process each item in the batch
      for (let j = 0; j < batch.length; j++) {
        const loanClientIndex = i + j;
        const loanClientData = batch[j];

        try {
          const createdLoanClient =
            await this.createSingleLoanClientWithFlexibleKeys(loanClientData);
          createdLoanClients.push(createdLoanClient);
        } catch (error) {
          errors.push({
            index: loanClientIndex,
            error: error.message || 'Unknown error occurred',
            loanClientData: loanClientData,
          });
        }
      }
    }

    return {
      totalProcessed: loanClients.length,
      totalCreated: createdLoanClients.length,
      totalFailed: errors.length,
      createdLoanClients,
      errors,
    };
  }

  /**
   * Helper method to create a single loan client with flexible foreign key handling
   * Resolves foreign keys by UUID or name, creating new records if needed
   */
  private async createSingleLoanClientWithFlexibleKeys(
    loanClientData: CreateLoanClientFlexibleDto,
  ): Promise<LoanClientSummaryResponseDto> {
    const {
      branchIdentifier,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryIdentifier,
      customerName,
      employerIdentifier,
      isicSectorIdentifier,
      leadType,
      phoneNumber,
      parentLoanClientId,
      leadStatus,
      anchorId,
      rmUserId,
      assignedUser,
      accountNumber,
      accountNumberAssignedAt,
      anchorRelationshipIdentifier,
      anchorRelationshipId,
    } = loanClientData;

    // Clean up empty strings
    const cleanParentLoanClientId =
      parentLoanClientId && parentLoanClientId.trim() !== ''
        ? parentLoanClientId
        : undefined;

    // Resolve foreign key relationships
    const [
      branchId,
      customerCategoryId,
      employerId,
      isicSectorId,
      parentLoanClient,
      resolvedAnchorRelationshipId,
    ] = await Promise.all([
      this.resolveBranchId(branchIdentifier),
      this.resolveCustomerCategoryId(customerCategoryIdentifier, undefined),
      this.resolveEmployerId(employerIdentifier),
      this.resolveIsicSectorId(isicSectorIdentifier),
      cleanParentLoanClientId
        ? this.prisma.loanClient.findUnique({
            where: { id: cleanParentLoanClientId },
          })
        : null,
      this.resolveAnchorRelationshipId(
        anchorRelationshipIdentifier,
        anchorRelationshipId,
      ),
    ]);

    // Validate parent loan client exists if provided
    if (cleanParentLoanClientId && !parentLoanClient) {
      throw new NotFoundException(
        `Parent loan client with ID '${cleanParentLoanClientId}' not found`,
      );
    }

    // Get RM user (same logic as original method)
    let rmUser;
    if (branchId) {
      rmUser = await this.prisma.user.findFirst({
        where: { branch_id: branchId },
      });
    } else {
      rmUser = await this.prisma.user.findFirst();
    }

    // Create loan client with contact person in a transaction
    const loanClient = await this.prisma.$transaction(async (tx) => {
      const newLoanClient = await tx.loanClient.create({
        data: {
          customer_name: customerName || 'Unknown Customer',
          customer_category_id: customerCategoryId || undefined,
          isic_sector_id: isicSectorId || undefined,
          phone_number: phoneNumber || undefined,
          type_of_lead: leadType || 'New',
          lead_status: leadStatus || 'Pending',
          branch_id: branchId || undefined,
          rm_user_id: rmUserId || rmUser?.id || undefined,
          assigned_user: assignedUser || undefined,
          employer_id: employerId || undefined,
          anchor_id: anchorId || cleanParentLoanClientId || undefined,
          anchor_relationship_id: resolvedAnchorRelationshipId || undefined,
          account_number: accountNumber || undefined,
          account_number_assigned_at: accountNumberAssignedAt
            ? new Date(accountNumberAssignedAt)
            : undefined,
          contact_persons:
            contactPersonName && contactPersonPhone
              ? {
                  create: [
                    {
                      name: contactPersonName,
                      phone_number: contactPersonPhone,
                    },
                  ],
                }
              : undefined,
        },
      });

      return newLoanClient;
    });

    // Return summary data
    return {
      id: loanClient.id,
      customerName: customerName || 'Unknown Customer',
      phoneNumber: phoneNumber || '',
      typeOfLead: leadType || 'New',
      leadStatus: leadStatus || 'Pending',
      accountNumber: undefined, // New loan clients don't have account numbers
      accountNumberAssignedAt: undefined, // New loan clients haven't been converted
      createdAt: loanClient.created_at.toISOString(),
      updatedAt: loanClient.updated_at.toISOString(),
      // Activity counts (new loan clients start with 0)
      totalLoanActivities: 0,
      lastLoanActivityDate: undefined,
      contactPersonsCount: contactPersonName && contactPersonPhone ? 1 : 0,
      lastInteraction: undefined, // New loan clients have no interactions yet
      // Relationship information
      branch: undefined,
      customerCategory: undefined,
      isicSector: undefined,
      employer: undefined,
      anchor: undefined,
      anchorRelationship: undefined,
      rmUser: undefined,
    };
  }

  /**
   * Creates loan clients from an uploaded Excel file
   * Parses Excel file, maps columns to loan client fields, and creates clients using bulk creation logic
   */
  async createFromExcel(
    file: Express.Multer.File,
  ): Promise<ExcelUploadResponseDto> {
    try {
      // Load all existing anchors for fuzzy matching
      const existingAnchors = await this.prisma.anchor.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          phone_number: true,
        },
      });

      // Parse Excel file
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];

      if (!sheetName) {
        throw new BadRequestException('Excel file contains no sheets');
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      if (rawData.length === 0) {
        throw new BadRequestException('Excel file is empty');
      }

      // Extract headers and data rows
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1) as any[][];

      if (dataRows.length === 0) {
        throw new BadRequestException('Excel file contains no data rows');
      }

      // Map Excel columns to loan client fields
      const columnMappings = this.mapExcelColumns(headers);

      // Convert Excel rows to loan client objects with anchor processing
      const { loanClients, parseErrors, anchorCreationResults } =
        await this.parseExcelRowsWithAnchors(
          dataRows,
          headers,
          columnMappings,
          existingAnchors,
        );

      // Create loan clients using bulk creation logic
      const bulkResult = await this.createBulk({ loanClients });

      // Combine parsing errors with creation errors
      const allErrors = [
        ...parseErrors,
        ...bulkResult.errors.map((error) => ({
          row: error.index + 2, // +2 because index is 0-based and we skip header row
          error: error.error,
          data: error.loanClientData,
        })),
      ];

      return {
        success: allErrors.length === 0,
        message: `Successfully processed ${dataRows.length} rows from Excel file`,
        totalRows: rawData.length,
        processedRows: dataRows.length,
        successfulCreations: bulkResult.totalCreated,
        failedCreations: allErrors.length,
        createdLoanClients: bulkResult.createdLoanClients,
        errors: allErrors,
        columnMappings,
        anchorCreationResults,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to process Excel file: ${error.message}`,
      );
    }
  }

  /**
   * Retrieves all loan clients with pagination and search
   */
  async findAll(
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<LoanClientSummaryResponseDto>> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build search conditions
    const whereClause = search
      ? {
          OR: [
            {
              customer_name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              phone_number: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              account_number: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              type_of_lead: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
            {
              rm_user: {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            },
            {
              branch: {
                name: {
                  contains: search,
                  mode: 'insensitive' as const,
                },
              },
            },
          ],
        }
      : {};

    // Get total count and data
    const [total, loanClients] = await Promise.all([
      this.prisma.loanClient.count({ where: whereClause }),
      this.prisma.loanClient.findMany({
        where: whereClause,
        include: this.getLoanClientIncludeOptions(),
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
    ]);

    // Transform to response format
    const data = await Promise.all(
      loanClients.map((client) => this.transformToSummaryResponse(client)),
    );

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves a single loan client by ID with full details
   */
  async findOne(id: string): Promise<LoanClientResponseDto> {
    const loanClient = await this.prisma.loanClient.findUnique({
      where: { id },
      include: {
        ...this.getLoanClientIncludeOptions(),
        contact_persons: true,
        loan_activities: {
          include: {
            rm_user: {
              select: {
                id: true,
                name: true,
                rm_code: true,
              },
            },
            purpose: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            created_at: 'desc',
          },
          take: 5, // Get last 5 activities
        },
      },
    });

    if (!loanClient) {
      throw new NotFoundException(`Loan client with ID '${id}' not found`);
    }

    return this.transformToDetailedResponse(loanClient);
  }

  /**
   * Updates a loan client with validation of foreign key relationships
   */
  async update(
    id: string,
    updateLoanClientDto: UpdateLoanClientDto,
  ): Promise<LoanClientSummaryResponseDto> {
    const existingClient = await this.prisma.loanClient.findUnique({
      where: { id },
    });

    if (!existingClient) {
      throw new NotFoundException(`Loan client with ID '${id}' not found`);
    }

    try {
      // Resolve foreign key relationships
      const resolvedData = await this.resolveForeignKeys(updateLoanClientDto);

      // Check for duplicate account number if being updated
      if (
        resolvedData.accountNumber &&
        resolvedData.accountNumber !== existingClient.account_number
      ) {
        const duplicateClient = await this.prisma.loanClient.findUnique({
          where: { account_number: resolvedData.accountNumber },
        });

        if (duplicateClient) {
          throw new ConflictException(
            `Loan client with account number '${resolvedData.accountNumber}' already exists`,
          );
        }
      }

      // Update loan client
      const updatedClient = await this.prisma.loanClient.update({
        where: { id },
        data: {
          customer_name: resolvedData.customerName,
          phone_number: resolvedData.phoneNumber,
          type_of_lead: resolvedData.typeOfLead,
          lead_status: resolvedData.leadStatus,
          account_number: resolvedData.accountNumber,
          account_number_assigned_at: resolvedData.accountNumberAssignedAt
            ? new Date(resolvedData.accountNumberAssignedAt)
            : undefined,
          anchor_id: resolvedData.anchorId,
          customer_category_id: resolvedData.customerCategoryId,
          isic_sector_id: resolvedData.isicSectorId,
          branch_id: resolvedData.branchId,
          rm_user_id: resolvedData.rmUserId,
          assigned_user: resolvedData.assignedUser,
          employer_id: resolvedData.employerId,
          anchor_relationship_id: resolvedData.anchorRelationshipId,
        },
        include: this.getLoanClientIncludeOptions(),
      });

      // Update contact person if provided
      if (resolvedData.contactPersonName || resolvedData.contactPersonPhone) {
        const existingContact =
          await this.prisma.loanClientContactPerson.findFirst({
            where: { loan_client_id: id },
          });

        if (existingContact) {
          await this.prisma.loanClientContactPerson.update({
            where: { id: existingContact.id },
            data: {
              name: resolvedData.contactPersonName || existingContact.name,
              phone_number:
                resolvedData.contactPersonPhone || existingContact.phone_number,
            },
          });
        } else if (
          resolvedData.contactPersonName &&
          resolvedData.contactPersonPhone
        ) {
          await this.prisma.loanClientContactPerson.create({
            data: {
              loan_client_id: id,
              name: resolvedData.contactPersonName,
              phone_number: resolvedData.contactPersonPhone,
            },
          });
        }
      }

      return this.transformToSummaryResponse(updatedClient);
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update loan client: ${error.message}`,
      );
    }
  }

  /**
   * Deletes a loan client
   * Checks for dependencies before deletion
   */
  async remove(id: string): Promise<void> {
    const existingClient = await this.prisma.loanClient.findUnique({
      where: { id },
      include: {
        loan_activities: true,
        contact_persons: true,
      },
    });

    if (!existingClient) {
      throw new NotFoundException(`Loan client with ID '${id}' not found`);
    }

    // Check for dependencies
    if (existingClient.loan_activities.length > 0) {
      throw new BadRequestException(
        `Cannot delete loan client with existing loan activities. Found ${existingClient.loan_activities.length} activities.`,
      );
    }

    try {
      // Delete contact persons first (cascade)
      await this.prisma.loanClientContactPerson.deleteMany({
        where: { loan_client_id: id },
      });

      // Delete the loan client
      await this.prisma.loanClient.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException(
        `Failed to delete loan client: ${error.message}`,
      );
    }
  }

  /**
   * Exports all loan clients to Excel file
   */
  async exportLoanClientsToExcel(search?: string): Promise<Buffer> {
    const whereClause = {
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            account_number: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            type_of_lead: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            rm_user: {
              name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          },
          {
            branch: {
              name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          },
        ],
      }),
    };

    // Fetch loan clients with comprehensive data
    const loanClients = await this.prisma.loanClient.findMany({
      where: whereClause,
      include: {
        ...this.getLoanClientIncludeOptions(),
        loan_activities: {
          orderBy: {
            created_at: 'desc',
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Loan Clients Export');

    // Define headers
    const headers = [
      'ID',
      'Customer Name',
      'Phone Number',
      'Type of Lead',
      'Lead Status',
      'Account Number',
      'Account Number Assigned At',
      'Anchor Name',
      'Branch Name',
      'RM Name',
      'RM Code',
      'Customer Category',
      'ISIC Sector',
      'Employer',
      'Anchor Relationship',
      'Total Loan Activities',
      'Last Activity Date',
      'Created At',
      'Updated At',
    ];

    worksheet.addRow(headers);

    // Style headers
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // Add data rows
    loanClients.forEach((client) => {
      const lastActivity = client.loan_activities[0];
      worksheet.addRow([
        client.id,
        client.customer_name || '',
        client.phone_number || '',
        client.type_of_lead || '',
        client.lead_status || '',
        client.account_number || '',
        client.account_number_assigned_at?.toISOString() || '',
        client.anchor?.name || '',
        client.branch?.name || '',
        client.rm_user?.name || '',
        client.rm_user?.rm_code || '',
        client.customer_category?.name || '',
        client.isic_sector?.name || '',
        client.employer?.name || '',
        client.anchor_relationship?.name || '',
        client.loan_activities.length,
        lastActivity?.created_at?.toISOString() || '',
        client.created_at.toISOString(),
        client.updated_at.toISOString(),
      ]);
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  // Private helper methods

  /**
   * Gets standard include options for loan client queries
   */
  private getLoanClientIncludeOptions() {
    return {
      anchor: {
        select: {
          id: true,
          name: true,
          email: true,
          phone_number: true,
        },
      },
      branch: {
        select: {
          id: true,
          name: true,
          region: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      rm_user: {
        select: {
          id: true,
          name: true,
          rm_code: true,
        },
      },
      assigned_user_rel: {
        select: {
          id: true,
          name: true,
          rm_code: true,
        },
      },
      customer_category: {
        select: {
          id: true,
          name: true,
        },
      },
      isic_sector: {
        select: {
          id: true,
          code: true,
          name: true,
        },
      },
      employer: {
        select: {
          id: true,
          name: true,
        },
      },
      anchor_relationship: {
        select: {
          id: true,
          name: true,
        },
      },
    };
  }

  /**
   * Transforms a loan client entity to summary response format
   */
  private async transformToSummaryResponse(
    client: any,
  ): Promise<LoanClientSummaryResponseDto> {
    // Get activity counts and last interaction
    const [
      totalLoanActivities,
      lastActivity,
      contactPersonsCount,
      lastInteraction,
    ] = await Promise.all([
      this.prisma.loanActivity.count({
        where: { loan_client_id: client.id },
      }),
      this.prisma.loanActivity.findFirst({
        where: { loan_client_id: client.id },
        orderBy: { created_at: 'desc' },
        select: { created_at: true },
      }),
      this.prisma.loanClientContactPerson.count({
        where: { loan_client_id: client.id },
      }),
      this.prisma.loanActivity.findFirst({
        where: {
          loan_client_id: client.id,
          interaction_type: 'call', // Filter for call activities only
        },
        orderBy: { created_at: 'desc' },
        select: {
          interaction_type: true,
          created_at: true,
        },
      }),
    ]);

    return {
      id: client.id,
      customerName: client.customer_name || '',
      phoneNumber: client.phone_number,
      typeOfLead: client.type_of_lead,
      leadStatus: client.lead_status,
      accountNumber: client.account_number,
      accountNumberAssignedAt: client.account_number_assigned_at?.toISOString(),
      createdAt: client.created_at.toISOString(),
      updatedAt: client.updated_at.toISOString(),
      anchor: client.anchor
        ? {
            id: client.anchor.id,
            name: client.anchor.name,
            email: client.anchor.email,
            phoneNumber: client.anchor.phone_number,
          }
        : undefined,
      branch: client.branch
        ? {
            id: client.branch.id,
            name: client.branch.name,
            region: client.branch.region
              ? {
                  id: client.branch.region.id,
                  name: client.branch.region.name,
                }
              : undefined,
          }
        : undefined,
      rmUser: client.rm_user
        ? {
            id: client.rm_user.id,
            name: client.rm_user.name,
            rmCode: client.rm_user.rm_code,
          }
        : undefined,
      customerCategory: client.customer_category
        ? {
            id: client.customer_category.id,
            name: client.customer_category.name,
          }
        : undefined,
      isicSector: client.isic_sector
        ? {
            id: client.isic_sector.id,
            code: client.isic_sector.code,
            name: client.isic_sector.name,
          }
        : undefined,
      employer: client.employer
        ? {
            id: client.employer.id,
            name: client.employer.name,
          }
        : undefined,
      anchorRelationship: client.anchor_relationship
        ? {
            id: client.anchor_relationship.id,
            name: client.anchor_relationship.name,
          }
        : undefined,
      totalLoanActivities,
      lastLoanActivityDate: lastActivity?.created_at?.toISOString(),
      contactPersonsCount,
      lastInteraction: lastInteraction
        ? {
            activity_type: lastInteraction.interaction_type || 'call',
            date_time: lastInteraction.created_at.toISOString(),
          }
        : undefined,
    };
  }

  /**
   * Transforms a loan client entity to detailed response format
   */
  private transformToDetailedResponse(client: any): LoanClientResponseDto {
    return {
      id: client.id,
      customerName: client.customer_name || '',
      phoneNumber: client.phone_number,
      typeOfLead: client.type_of_lead,
      leadStatus: client.lead_status,
      accountNumber: client.account_number,
      accountNumberAssignedAt: client.account_number_assigned_at?.toISOString(),
      createdAt: client.created_at.toISOString(),
      updatedAt: client.updated_at.toISOString(),
      anchor: client.anchor
        ? {
            id: client.anchor.id,
            name: client.anchor.name,
            email: client.anchor.email,
            phoneNumber: client.anchor.phone_number,
          }
        : undefined,
      branch: client.branch
        ? {
            id: client.branch.id,
            name: client.branch.name,
            region: client.branch.region
              ? {
                  id: client.branch.region.id,
                  name: client.branch.region.name,
                }
              : undefined,
          }
        : undefined,
      rmUser: client.rm_user
        ? {
            id: client.rm_user.id,
            name: client.rm_user.name,
            rmCode: client.rm_user.rm_code,
          }
        : undefined,
      assignedUser: client.assigned_user_rel
        ? {
            id: client.assigned_user_rel.id,
            name: client.assigned_user_rel.name,
            rmCode: client.assigned_user_rel.rm_code,
          }
        : undefined,
      customerCategory: client.customer_category
        ? {
            id: client.customer_category.id,
            name: client.customer_category.name,
          }
        : undefined,
      isicSector: client.isic_sector
        ? {
            id: client.isic_sector.id,
            code: client.isic_sector.code,
            name: client.isic_sector.name,
          }
        : undefined,
      employer: client.employer
        ? {
            id: client.employer.id,
            name: client.employer.name,
          }
        : undefined,
      anchorRelationship: client.anchor_relationship
        ? {
            id: client.anchor_relationship.id,
            name: client.anchor_relationship.name,
          }
        : undefined,
      totalLoanActivities: client.loan_activities?.length || 0,
      lastLoanActivityDate:
        client.loan_activities?.[0]?.created_at?.toISOString(),
      contactPersonsCount: client.contact_persons?.length || 0,
      contactPersons:
        client.contact_persons?.map((cp: any) => ({
          id: cp.id,
          name: cp.name,
          phoneNumber: cp.phone_number,
        })) || [],
      recentLoanActivities:
        client.loan_activities?.map((activity: any) => ({
          id: activity.id,
          loanAccountNumber: activity.loan_account_number,
          purpose: activity.purpose?.name,
          loanBalance: activity.loan_balance
            ? parseFloat(activity.loan_balance.toString())
            : undefined,
          arrearsDays: activity.arrears_days,
          comment: activity.comment,
          createdAt: activity.created_at.toISOString(),
          rmUser: {
            id: activity.rm_user.id,
            name: activity.rm_user.name,
            rmCode: activity.rm_user.rm_code,
          },
        })) || [],
    };
  }

  /**
   * Maps Excel column headers to loan client field names
   * Uses exact and fuzzy matching to handle variations in column naming
   */
  private mapExcelColumns(headers: string[]): Record<string, string> {
    const mappings: Record<string, string> = {};

    // Define column mapping rules with priority (more specific patterns first)
    const columnRules = [
      {
        field: 'customerName',
        patterns: ['customer name', 'client name', 'loan client name'],
      },
      {
        field: 'phoneNumber',
        patterns: ['phone number', 'mobile number', 'telephone'],
      },
      {
        field: 'branchIdentifier',
        patterns: ['branch', 'branch name', 'office', 'location'],
      },
      {
        field: 'customerCategoryIdentifier',
        patterns: ['customer category', 'category', 'client category'],
      },
      {
        field: 'employerIdentifier',
        patterns: ['employer', 'company', 'organization'],
      },
      {
        field: 'isicSectorIdentifier',
        patterns: ['isic sector', 'sector', 'industry'],
      },
      {
        field: 'leadType',
        patterns: ['lead type', 'type of lead', 'client type', 'type'],
      },
      {
        field: 'leadStatus',
        patterns: ['lead status', 'status', 'client status'],
      },
      {
        field: 'accountNumber',
        patterns: ['account number', 'account no', 'acc number'],
      },
      {
        field: 'accountNumberAssignedAt',
        patterns: ['account assigned date', 'assigned date', 'conversion date'],
      },
      {
        field: 'anchorName',
        patterns: ['anchor', 'anchor name', 'company name'],
      },
      {
        field: 'anchorRelationshipIdentifier',
        patterns: ['anchor relationship', 'relationship', 'relationship type'],
      },
      {
        field: 'contactPersonName',
        patterns: ['contact person', 'contact name', 'person name'],
      },
      {
        field: 'contactPersonPhone',
        patterns: ['contact phone', 'contact number', 'person phone'],
      },
      {
        field: 'rmCode',
        patterns: ['rm code', 'relationship manager', 'rm'],
      },
      {
        field: 'assignedUser',
        patterns: ['assigned user', 'assigned to', 'user'],
      },
    ];

    // Process each header
    headers.forEach((header) => {
      const normalizedHeader = header.toLowerCase().trim();
      let bestMatch: { field: string; score: number } | null = null;

      // Try to find the best matching rule
      for (const rule of columnRules) {
        for (const pattern of rule.patterns) {
          let score = 0;

          // Exact match gets highest score
          if (normalizedHeader === pattern) {
            score = 100;
          }
          // Contains match gets medium score
          else if (normalizedHeader.includes(pattern)) {
            score = 80;
          }
          // Pattern contains header gets lower score
          else if (pattern.includes(normalizedHeader)) {
            score = 60;
          }
          // Fuzzy match for similar words
          else {
            const similarity = this.calculateStringSimilarity(
              normalizedHeader,
              pattern,
            );
            if (similarity >= 70) {
              score = similarity;
            }
          }

          // Update best match if this score is higher
          if (score > 0 && (!bestMatch || score > bestMatch.score)) {
            bestMatch = { field: rule.field, score };
          }
        }
      }

      // Apply the best match if found
      if (bestMatch && bestMatch.score >= 60) {
        mappings[header] = bestMatch.field;
      }
    });

    return mappings;
  }

  /**
   * Parses Excel rows into loan client objects with anchor processing
   * Matches the leads service implementation exactly
   */
  private async parseExcelRowsWithAnchors(
    dataRows: any[][],
    headers: string[],
    columnMappings: Record<string, string>,
    existingAnchors: Array<{
      id: string;
      name: string;
      email: string;
      phone_number: string;
    }>,
  ): Promise<{
    loanClients: CreateLoanClientFlexibleDto[];
    parseErrors: Array<{ row: number; error: string; data: any }>;
    anchorCreationResults: Array<{
      row: number;
      anchorName: string;
      action: 'matched' | 'created' | 'skipped';
      anchorId?: string;
      similarity?: number;
      matchedAnchorName?: string;
    }>;
  }> {
    const loanClients: CreateLoanClientFlexibleDto[] = [];
    const parseErrors: Array<{ row: number; error: string; data: any }> = [];
    const anchorCreationResults: Array<{
      row: number;
      anchorName: string;
      action: 'matched' | 'created' | 'skipped';
      anchorId?: string;
      similarity?: number;
      matchedAnchorName?: string;
    }> = [];

    // Keep track of anchors created during this import to avoid duplicates
    const newlyCreatedAnchors = new Map<
      string,
      { id: string; name: string; email: string; phone_number: string }
    >();

    for (let rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
      const row = dataRows[rowIndex];

      try {
        const loanClientData: Partial<CreateLoanClientFlexibleDto> = {};
        const rowData: Record<string, any> = {};
        let anchorName: string | undefined = undefined;
        let rmCode: string | undefined = undefined;

        // Map row data to object using headers
        headers.forEach((header, colIndex) => {
          const value = row[colIndex];
          rowData[header] = value;

          const fieldName = columnMappings[header];
          if (
            fieldName &&
            value !== undefined &&
            value !== null &&
            value !== ''
          ) {
            // Clean and convert the value
            const cleanValue = String(value).trim();
            if (cleanValue) {
              if (fieldName === 'anchorName') {
                anchorName = cleanValue;
              } else if (fieldName === 'rmCode') {
                rmCode = cleanValue;
              } else {
                (loanClientData as any)[fieldName] = cleanValue;
              }
            }
          }
        });

        // Process anchor name if provided
        if (anchorName && typeof anchorName === 'string') {
          // First check if we already created this anchor in this import
          const normalizedAnchorName = (anchorName as string)
            .toLowerCase()
            .trim();
          let targetAnchor = newlyCreatedAnchors.get(normalizedAnchorName);

          if (targetAnchor) {
            // Use the anchor we created earlier in this import
            loanClientData.anchorId = targetAnchor.id;
            anchorCreationResults.push({
              row: rowIndex + 2,
              anchorName: anchorName,
              action: 'matched',
              anchorId: targetAnchor.id,
              similarity: 100,
              matchedAnchorName: targetAnchor.name,
            });
          } else {
            // Check for existing anchors with fuzzy matching
            const bestMatch = this.findBestAnchorMatch(
              anchorName,
              existingAnchors,
              60,
            );

            if (bestMatch) {
              // Found a matching existing anchor
              loanClientData.anchorId = bestMatch.anchor.id;
              anchorCreationResults.push({
                row: rowIndex + 2,
                anchorName: anchorName,
                action: 'matched',
                anchorId: bestMatch.anchor.id,
                similarity: bestMatch.similarity,
                matchedAnchorName: bestMatch.anchor.name,
              });
            } else {
              // No match found, create new anchor
              try {
                const newAnchor = await this.createAnchorFromName(anchorName);
                loanClientData.anchorId = newAnchor.id;

                // Add to our tracking maps
                newlyCreatedAnchors.set(normalizedAnchorName, newAnchor);
                existingAnchors.push(newAnchor); // Add to existing anchors for future matches

                anchorCreationResults.push({
                  row: rowIndex + 2,
                  anchorName: anchorName,
                  action: 'created',
                  anchorId: newAnchor.id,
                });
              } catch (error) {
                // If anchor creation fails, log it but continue with the loan client
                parseErrors.push({
                  row: rowIndex + 2,
                  error: `Failed to create anchor "${anchorName}": ${error.message}`,
                  data: row,
                });

                anchorCreationResults.push({
                  row: rowIndex + 2,
                  anchorName: anchorName,
                  action: 'skipped',
                });
              }
            }
          }
        }

        // Process RM code if provided
        if (rmCode && typeof rmCode === 'string') {
          try {
            const rmUser = await this.prisma.user.findFirst({
              where: {
                rm_code: (rmCode as string).trim(),
              },
            });

            if (rmUser) {
              loanClientData.rmUserId = rmUser.id;
            } else {
              // Log warning but continue processing
              console.warn(
                `RM code '${rmCode}' not found for row ${rowIndex + 2}`,
              );
            }
          } catch (error) {
            console.error(
              `Error looking up RM code '${rmCode}' for row ${rowIndex + 2}:`,
              error,
            );
          }
        }

        // Process anchor relationship identifier if provided
        if (
          loanClientData.anchorRelationshipIdentifier &&
          typeof loanClientData.anchorRelationshipIdentifier === 'string'
        ) {
          try {
            const anchorRelationship =
              await this.prisma.anchorRelationship.findFirst({
                where: {
                  name: {
                    contains:
                      loanClientData.anchorRelationshipIdentifier.trim(),
                    mode: 'insensitive',
                  },
                },
              });

            if (anchorRelationship) {
              loanClientData.anchorRelationshipId = anchorRelationship.id;
              // Remove the identifier since we now have the ID
              delete loanClientData.anchorRelationshipIdentifier;
            } else {
              // Log warning but continue processing
              console.warn(
                `Anchor relationship '${loanClientData.anchorRelationshipIdentifier}' not found for row ${rowIndex + 2}`,
              );
            }
          } catch (error) {
            console.error(
              `Error looking up anchor relationship '${loanClientData.anchorRelationshipIdentifier}' for row ${rowIndex + 2}:`,
              error,
            );
          }
        }

        // Process account number assigned date if provided
        if (
          loanClientData.accountNumberAssignedAt &&
          typeof loanClientData.accountNumberAssignedAt === 'string'
        ) {
          try {
            // Validate and parse the date
            const parsedDate = new Date(loanClientData.accountNumberAssignedAt);
            if (isNaN(parsedDate.getTime())) {
              parseErrors.push({
                row: rowIndex + 2,
                error: `Invalid account assigned date format: ${loanClientData.accountNumberAssignedAt}`,
                data: row,
              });
              delete loanClientData.accountNumberAssignedAt;
            } else {
              // Keep as ISO string for DTO validation
              loanClientData.accountNumberAssignedAt = parsedDate.toISOString();
            }
          } catch (error) {
            parseErrors.push({
              row: rowIndex + 2,
              error: `Error parsing account assigned date: ${error.message}`,
              data: row,
            });
            delete loanClientData.accountNumberAssignedAt;
          }
        }

        // Validate required fields or set defaults (same as original logic)
        if (!loanClientData.customerName) {
          // Try to find customer name in unmapped columns
          const possibleNameColumns = headers.filter(
            (h) => h.toLowerCase().includes('name') && !columnMappings[h],
          );
          if (possibleNameColumns.length > 0) {
            const nameValue = rowData[possibleNameColumns[0]];
            if (nameValue) {
              loanClientData.customerName = String(nameValue).trim();
            }
          }
        }

        // Set default values if not provided
        if (!loanClientData.leadType) {
          loanClientData.leadType = 'New';
        }
        if (!loanClientData.leadStatus) {
          loanClientData.leadStatus = 'Pending';
        }

        // Validate phone number format if provided
        if (loanClientData.phoneNumber) {
          loanClientData.phoneNumber = this.normalizePhoneNumber(
            loanClientData.phoneNumber,
          );
        }

        // Ensure customerName is set before pushing
        if (loanClientData.customerName) {
          loanClients.push(loanClientData as CreateLoanClientFlexibleDto);
        } else {
          parseErrors.push({
            row: rowIndex + 2,
            error: 'Customer name is required',
            data: row,
          });
        }
      } catch (error) {
        parseErrors.push({
          row: rowIndex + 2, // +2 because rowIndex is 0-based and we skip header row
          error: `Row parsing error: ${error.message}`,
          data: row,
        });
      }
    }

    return { loanClients, parseErrors, anchorCreationResults };
  }

  /**
   * Calculates Levenshtein distance for fuzzy matching
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1,
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Resolves foreign key relationships from identifiers to IDs
   */
  private async resolveForeignKeys(
    data: CreateLoanClientDto | UpdateLoanClientDto,
  ) {
    const resolved: any = { ...data };

    // Resolve anchor
    if (data.anchorIdentifier && !data.anchorId) {
      const anchor = await this.prisma.anchor.findFirst({
        where: {
          name: {
            contains: data.anchorIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (anchor) {
        resolved.anchorId = anchor.id;
      }
    }

    // Resolve customer category
    if (data.customerCategoryIdentifier && !data.customerCategoryId) {
      const category = await this.prisma.customerCategory.findFirst({
        where: {
          name: {
            contains: data.customerCategoryIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (category) {
        resolved.customerCategoryId = category.id;
      }
    }

    // Resolve ISIC sector
    if (data.isicSectorIdentifier && !data.isicSectorId) {
      const sector = await this.prisma.iSICSector.findFirst({
        where: {
          name: {
            contains: data.isicSectorIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (sector) {
        resolved.isicSectorId = sector.id;
      }
    }

    // Resolve branch
    if (data.branchIdentifier && !data.branchId) {
      const branch = await this.prisma.branch.findFirst({
        where: {
          name: {
            contains: data.branchIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (branch) {
        resolved.branchId = branch.id;
      }
    }

    // Resolve RM user
    if (data.rmIdentifier && !data.rmUserId) {
      const user = await this.prisma.user.findFirst({
        where: {
          rm_code: {
            contains: data.rmIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (user) {
        resolved.rmUserId = user.id;
      }
    }

    // Resolve employer
    if (data.employerIdentifier && !data.employerId) {
      const employer = await this.prisma.employer.findFirst({
        where: {
          name: {
            contains: data.employerIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (employer) {
        resolved.employerId = employer.id;
      }
    }

    // Resolve anchor relationship
    if (data.anchorRelationshipIdentifier && !data.anchorRelationshipId) {
      const relationship = await this.prisma.anchorRelationship.findFirst({
        where: {
          name: {
            contains: data.anchorRelationshipIdentifier,
            mode: 'insensitive',
          },
        },
      });
      if (relationship) {
        resolved.anchorRelationshipId = relationship.id;
      }
    }

    return resolved;
  }

  /**
   * Helper method to resolve branch ID from identifier (UUID or name)
   * Creates new branch if name is provided and doesn't exist
   */
  private async resolveBranchId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: cleanIdentifier },
      });
      return branch ? branch.id : undefined;
    }

    // Try to find by name (exact match first, then fuzzy)
    let branch = await this.prisma.branch.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (branch) {
      return branch.id;
    }

    // Try fuzzy match
    branch = await this.prisma.branch.findFirst({
      where: {
        name: {
          contains: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (branch) {
      return branch.id;
    }

    // Don't create new branch automatically since it requires a region
    // Return undefined if not found
    console.warn(
      `Branch "${cleanIdentifier}" not found and cannot be created automatically`,
    );
    return undefined;
  }

  /**
   * Helper method to resolve customer category ID from identifier (UUID or name)
   * Creates new customer category if name is provided and doesn't exist
   */
  private async resolveCustomerCategoryId(
    identifier?: string,
    userId?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const category = await this.prisma.customerCategory.findUnique({
        where: { id: cleanIdentifier },
      });
      return category ? category.id : undefined;
    }

    // Try to find by name (exact match first, then fuzzy)
    let category = await this.prisma.customerCategory.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (category) {
      return category.id;
    }

    // Try fuzzy match
    category = await this.prisma.customerCategory.findFirst({
      where: {
        name: {
          contains: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (category) {
      return category.id;
    }

    // Create new customer category if not found (only if userId is provided)
    if (!userId) {
      console.warn(
        `Customer category "${cleanIdentifier}" not found and cannot create without user context`,
      );
      return undefined;
    }

    try {
      const newCategory = await this.prisma.customerCategory.create({
        data: {
          name: cleanIdentifier,
          added_by: userId,
        },
      });
      return newCategory.id;
    } catch (error) {
      // If creation fails, return undefined
      console.warn(
        `Failed to create customer category "${cleanIdentifier}":`,
        error,
      );
      return undefined;
    }
  }

  /**
   * Helper method to resolve employer ID from identifier (UUID or name)
   * Creates new employer if name is provided and doesn't exist
   */
  private async resolveEmployerId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const employer = await this.prisma.employer.findUnique({
        where: { id: cleanIdentifier },
      });
      return employer ? employer.id : undefined;
    }

    // Try to find by name (exact match first, then fuzzy)
    let employer = await this.prisma.employer.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (employer) {
      return employer.id;
    }

    // Try fuzzy match
    employer = await this.prisma.employer.findFirst({
      where: {
        name: {
          contains: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (employer) {
      return employer.id;
    }

    // Create new employer if not found
    try {
      const newEmployer = await this.prisma.employer.create({
        data: {
          name: cleanIdentifier,
        },
      });
      return newEmployer.id;
    } catch (error) {
      // If creation fails, return undefined
      console.warn(`Failed to create employer "${cleanIdentifier}":`, error);
      return undefined;
    }
  }

  /**
   * Helper method to resolve ISIC sector ID from identifier (UUID or name)
   * Creates new ISIC sector if name is provided and doesn't exist
   */
  private async resolveIsicSectorId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const sector = await this.prisma.iSICSector.findUnique({
        where: { id: cleanIdentifier },
      });
      return sector ? sector.id : undefined;
    }

    // Try to find by name (exact match first, then fuzzy)
    let sector = await this.prisma.iSICSector.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (sector) {
      return sector.id;
    }

    // Try fuzzy match
    sector = await this.prisma.iSICSector.findFirst({
      where: {
        name: {
          contains: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (sector) {
      return sector.id;
    }

    // Create new ISIC sector if not found
    try {
      const newSector = await this.prisma.iSICSector.create({
        data: {
          name: cleanIdentifier,
        },
      });
      return newSector.id;
    } catch (error) {
      // If creation fails, return undefined
      console.warn(`Failed to create ISIC sector "${cleanIdentifier}":`, error);
      return undefined;
    }
  }

  /**
   * Helper method to resolve anchor relationship ID from identifier (UUID or name)
   * Does not create new anchor relationships - only resolves existing ones
   */
  private async resolveAnchorRelationshipId(
    identifier?: string,
    directId?: string,
  ): Promise<string | undefined> {
    // If direct ID is provided, validate and return it
    if (directId && directId.trim() !== '') {
      const cleanId = directId.trim();
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

      if (uuidRegex.test(cleanId)) {
        // Validate UUID exists
        const relationship = await this.prisma.anchorRelationship.findUnique({
          where: { id: cleanId },
        });
        return relationship ? relationship.id : undefined;
      }
    }

    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const relationship = await this.prisma.anchorRelationship.findUnique({
        where: { id: cleanIdentifier },
      });
      return relationship ? relationship.id : undefined;
    }

    // Try to find by name (exact match first, then fuzzy)
    let relationship = await this.prisma.anchorRelationship.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    if (relationship) {
      return relationship.id;
    }

    // Try fuzzy match
    relationship = await this.prisma.anchorRelationship.findFirst({
      where: {
        name: {
          contains: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    return relationship ? relationship.id : undefined;
  }

  /**
   * Finds the best matching anchor using fuzzy string matching
   * @param anchorName - Name to search for
   * @param existingAnchors - List of existing anchors to match against
   * @param threshold - Minimum similarity threshold (default: 60%)
   * @returns Best matching anchor or null if no match above threshold
   */
  private findBestAnchorMatch(
    anchorName: string,
    existingAnchors: Array<{
      id: string;
      name: string;
      email: string;
      phone_number: string;
    }>,
    threshold: number = 60,
  ): {
    anchor: { id: string; name: string; email: string; phone_number: string };
    similarity: number;
  } | null {
    if (!anchorName || !anchorName.trim()) return null;

    let bestMatch: {
      anchor: { id: string; name: string; email: string; phone_number: string };
      similarity: number;
    } | null = null;
    let highestSimilarity = 0;

    for (const anchor of existingAnchors) {
      const similarity = this.calculateStringSimilarity(
        anchorName,
        anchor.name,
      );

      if (similarity >= threshold && similarity > highestSimilarity) {
        highestSimilarity = similarity;
        bestMatch = { anchor, similarity };
      }
    }

    return bestMatch;
  }

  /**
   * Creates a new anchor from a name
   * @param anchorName - Name of the anchor to create
   * @returns Created anchor
   */
  private async createAnchorFromName(anchorName: string): Promise<{
    id: string;
    name: string;
    email: string;
    phone_number: string;
  }> {
    // Generate a basic email and phone number for the new anchor
    const sanitizedName = anchorName.toLowerCase().replace(/[^a-z0-9]/g, '');
    const email = `contact@${sanitizedName}.com`;
    const phoneNumber = '0700000000'; // Default placeholder phone number

    const newAnchor = await this.prisma.anchor.create({
      data: {
        name: anchorName.trim(),
        email: email,
        phone_number: phoneNumber,
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone_number: true,
      },
    });

    return newAnchor;
  }

  /**
   * Calculates string similarity using a simple algorithm
   * @param str1 - First string
   * @param str2 - Second string
   * @returns Similarity percentage (0-100)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;

    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 100;

    const longer = s1.length > s2.length ? s1 : s2;
    const shorter = s1.length > s2.length ? s2 : s1;

    if (longer.length === 0) return 100;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return Math.round(((longer.length - editDistance) / longer.length) * 100);
  }

  /**
   * Normalizes phone number format
   * Handles various input formats and converts to standard format
   */
  private normalizePhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\s+/g, '').replace(/[^\d+]/g, '');

    // Handle different formats
    if (cleaned.startsWith('+254')) {
      return cleaned;
    } else if (cleaned.startsWith('254')) {
      return '+' + cleaned;
    } else if (cleaned.startsWith('07') || cleaned.startsWith('01')) {
      return '+254' + cleaned.substring(1);
    } else if (
      cleaned.length === 9 &&
      (cleaned.startsWith('7') || cleaned.startsWith('1'))
    ) {
      return '+254' + cleaned;
    }

    // Return as-is if no pattern matches
    return phone;
  }
}
