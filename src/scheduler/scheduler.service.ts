import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as cron from 'node-cron';
import { QueueService } from '../queue/queue.service';
import { ScheduledTaskService } from '../scheduled-tasks/scheduled-task.service';

@Injectable()
export class SchedulerService implements OnModuleInit {
  private readonly logger = new Logger(SchedulerService.name);
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly queueService: QueueService,
    private readonly scheduledTaskService: ScheduledTaskService,
  ) {}

  async onModuleInit() {
    this.logger.log('🚀 Scheduler service initialized');

    // Start the scheduler (this will set up database polling)
    await this.startScheduler();

    this.logger.log('✅ Scheduler started and polling database for tasks');
  }

  async startScheduler() {
    this.logger.log('🔄 Starting database task polling...');

    // Process scheduled tasks from database - every minute
    this.scheduleTask('process-scheduled-tasks', '* * * * *', () =>
      this.processScheduledTasks(),
    );

    this.logger.log(
      `✅ Started database polling - checking every minute for tasks`,
    );
  }

  async stopScheduler() {
    this.logger.log('Stopping scheduled tasks...');

    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      this.logger.log(`Stopped task: ${name}`);
    }

    this.scheduledTasks.clear();
    this.logger.log('All scheduled tasks stopped');
  }

  private scheduleTask(
    name: string,
    cronExpression: string,
    callback: () => Promise<void>,
  ) {
    try {
      const task = cron.schedule(
        cronExpression,
        async () => {
          this.logger.log(`Executing scheduled task: ${name}`);
          try {
            await callback();
            this.logger.log(`Completed scheduled task: ${name}`);
          } catch (error) {
            this.logger.error(`Error in scheduled task ${name}:`, error);
          }
        },
        {
          timezone: this.configService.get('SCHEDULER_TIMEZONE') || 'UTC',
        },
      );

      this.scheduledTasks.set(name, task);
      this.logger.log(`Scheduled task: ${name} (${cronExpression})`);
    } catch (error) {
      this.logger.error(`Failed to schedule task ${name}:`, error);
    }
  }

  private async processScheduledTasks() {
    this.logger.log('🔍 Polling database for scheduled tasks...');

    try {
      // Get tasks ready for execution
      const tasksToProcess =
        await this.scheduledTaskService.getTasksReadyForExecution(50);

      if (tasksToProcess.length === 0) {
        this.logger.log('📭 No tasks ready for execution at this time');
        return;
      }

      this.logger.log(
        `🚀 Found ${tasksToProcess.length} tasks ready for execution`,
      );

      // Process tasks in smaller batches to prevent memory buildup
      const batchSize = 10;
      for (let i = 0; i < tasksToProcess.length; i += batchSize) {
        const batch = tasksToProcess.slice(i, i + batchSize);

        for (const task of batch) {
          try {
            // Add task to the appropriate queue
            await this.queueService.addScheduledTaskJob(
              {
                taskId: task.id,
                type: task.type,
                payload: task.payload,
                attemptNumber: task.attempts + 1,
              },
              {
                priority: task.priority,
                attempts: task.max_attempts,
              },
            );

            this.logger.log(
              `✅ Queued scheduled task: ${task.id} (${task.type}) - ${task.name}`,
            );
          } catch (error) {
            this.logger.error(
              `❌ Failed to queue task ${task.id} (${task.type}):`,
              error,
            );

            // Mark task as failed if we can't queue it
            await this.scheduledTaskService.markAsFailed(
              task.id,
              'system-execution-id', // We'll need to handle this better
              `Failed to queue task: ${error.message}`,
            );
          }
        }

        // Force garbage collection after each batch
        if (global.gc && i > 0) {
          global.gc();
        }

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < tasksToProcess.length) {
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      }

      this.logger.log(`Processed ${tasksToProcess.length} scheduled tasks`);
    } catch (error) {
      this.logger.error('Error processing scheduled tasks:', error);
    }
  }
}
