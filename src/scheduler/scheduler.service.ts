import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as cron from 'node-cron';
import { QueueService } from '../queue/queue.service';
// import { QueueCleanupService } from '../queue/queue-cleanup.service';
import { PrismaService } from '../prisma/prisma.service';
import { ScheduledTaskService } from '../scheduled-tasks/scheduled-task.service';
import { format, startOfDay, endOfDay, subDays, addMinutes } from 'date-fns';
import { MemoryMonitor } from '../common/utils/memory-monitor';

@Injectable()
export class SchedulerService implements OnModuleInit {
  private readonly logger = new Logger(SchedulerService.name);
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly queueService: QueueService,
    // private readonly queueCleanupService: QueueCleanupService,
    private readonly prisma: PrismaService,
    private readonly scheduledTaskService: ScheduledTaskService,
  ) {}

  async onModuleInit() {
    this.logger.log('🚀 Scheduler service initialized');

    // Start the scheduler (this will set up all cron jobs including database polling)
    await this.startScheduler();

    this.logger.log('✅ All scheduled tasks started successfully');
  }

  async startScheduler() {
    this.logger.log('Starting scheduled tasks...');

    // Daily reports at 6 AM
    this.scheduleTask('daily-reports', '0 6 * * *', () =>
      this.generateDailyReports(),
    );

    // Weekly reports on Monday at 8 AM
    this.scheduleTask('weekly-reports', '0 8 * * 1', () =>
      this.generateWeeklyReports(),
    );

    // Monthly reports on 1st of month at 9 AM
    this.scheduleTask('monthly-reports', '0 9 1 * *', () =>
      this.generateMonthlyReports(),
    );

    // Target progress calculation every hour
    this.scheduleTask('target-progress', '0 * * * *', () =>
      this.calculateTargetProgress(),
    );

    // Data cleanup - every day at 2 AM
    this.scheduleTask('data-cleanup', '0 2 * * *', () =>
      this.performDataCleanup(),
    );

    // Health check notifications - every 15 minutes
    this.scheduleTask('health-check', '*/15 * * * *', () =>
      this.performHealthCheck(),
    );

    // Reminder emails for pending activities - every day at 9 AM
    this.scheduleTask('activity-reminders', '0 9 * * *', () =>
      this.sendActivityReminders(),
    );

    // Process scheduled tasks from database - every minute for better responsiveness
    this.scheduleTask('process-scheduled-tasks', '* * * * *', () =>
      this.processScheduledTasks(),
    );

    // Process recurring tasks - every minute
    this.scheduleTask('process-recurring-tasks', '* * * * *', () =>
      this.processRecurringTasks(),
    );

    // Queue cleanup - every 2 hours (temporarily disabled)
    // this.scheduleTask('queue-cleanup', '0 */2 * * *', () => this.performQueueCleanup());

    // Memory monitoring and emergency cleanup - every 10 minutes (temporarily disabled)
    // this.scheduleTask('memory-check', '*/10 * * * *', () => this.performMemoryCheck());

    this.logger.log(`Started ${this.scheduledTasks.size} scheduled tasks`);
  }

  async stopScheduler() {
    this.logger.log('Stopping scheduled tasks...');

    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      this.logger.log(`Stopped task: ${name}`);
    }

    this.scheduledTasks.clear();
    this.logger.log('All scheduled tasks stopped');
  }

  private scheduleTask(
    name: string,
    cronExpression: string,
    callback: () => Promise<void>,
  ) {
    try {
      const task = cron.schedule(
        cronExpression,
        async () => {
          this.logger.log(`Executing scheduled task: ${name}`);
          try {
            await callback();
            this.logger.log(`Completed scheduled task: ${name}`);
          } catch (error) {
            this.logger.error(`Error in scheduled task ${name}:`, error);
          }
        },
        {
          timezone: this.configService.get('SCHEDULER_TIMEZONE') || 'UTC',
        },
      );

      this.scheduledTasks.set(name, task);
      this.logger.log(`Scheduled task: ${name} (${cronExpression})`);
    } catch (error) {
      this.logger.error(`Failed to schedule task ${name}:`, error);
    }
  }

  private async generateDailyReports() {
    this.logger.log('Generating daily reports...');

    // Get all active users who should receive daily reports
    const users = await this.prisma.user.findMany({
      where: {
        role: {
          name: {
            in: ['Manager', 'Admin', 'Supervisor'],
          },
        },
      },
      include: {
        branch: true,
        role: true,
      },
    });

    const yesterday = subDays(new Date(), 1);
    const filters = {
      dateFrom: startOfDay(yesterday),
      dateTo: endOfDay(yesterday),
    };

    for (const user of users) {
      // Queue daily activity report
      await this.queueService.addReportJob({
        userId: user.id,
        reportType: 'activities',
        filters: {
          ...filters,
          branchId: user.branch_id,
        },
        format: 'excel',
      });

      // Queue daily leads report
      await this.queueService.addReportJob({
        userId: user.id,
        reportType: 'leads',
        filters: {
          ...filters,
          branchId: user.branch_id,
        },
        format: 'excel',
      });
    }

    this.logger.log(`Queued daily reports for ${users.length} users`);
  }

  private async generateWeeklyReports() {
    this.logger.log('Generating weekly reports...');

    // Similar to daily reports but for weekly data
    const users = await this.prisma.user.findMany({
      where: {
        role: {
          name: {
            in: ['Manager', 'Admin', 'Regional Manager'],
          },
        },
      },
      include: {
        branch: true,
        role: true,
      },
    });

    const lastWeekStart = subDays(startOfDay(new Date()), 7);
    const lastWeekEnd = subDays(endOfDay(new Date()), 1);

    for (const user of users) {
      await this.queueService.addReportJob({
        userId: user.id,
        reportType: 'targets',
        filters: {
          dateFrom: lastWeekStart,
          dateTo: lastWeekEnd,
          branchId: user.branch_id,
        },
        format: 'excel',
      });
    }

    this.logger.log(`Queued weekly reports for ${users.length} users`);
  }

  private async generateMonthlyReports() {
    this.logger.log('Generating monthly reports...');

    // Generate comprehensive monthly reports for executives
    const executives = await this.prisma.user.findMany({
      where: {
        role: {
          name: {
            in: ['Admin', 'CEO', 'Regional Manager'],
          },
        },
      },
    });

    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    const monthStart = new Date(
      lastMonth.getFullYear(),
      lastMonth.getMonth(),
      1,
    );
    const monthEnd = new Date(
      lastMonth.getFullYear(),
      lastMonth.getMonth() + 1,
      0,
    );

    for (const executive of executives) {
      // Comprehensive monthly report
      await this.queueService.addReportJob({
        userId: executive.id,
        reportType: 'comprehensive',
        filters: {
          dateFrom: monthStart,
          dateTo: monthEnd,
        },
        format: 'excel',
      });
    }

    this.logger.log(
      `Queued monthly reports for ${executives.length} executives`,
    );
  }

  private async calculateTargetProgress() {
    this.logger.log('Calculating target progress...');

    // This would typically be a complex calculation
    // For now, we'll queue it as a data processing job
    await this.queueService.addDataProcessingJob({
      type: 'data-migration',
      userId: 'system',
      metadata: {
        migrationType: 'target-progress-calculation',
      },
    });

    this.logger.log('Queued target progress calculation');
  }

  private async performDataCleanup() {
    this.logger.log('Performing data cleanup...');

    try {
      // Clean up old completed jobs (older than 30 days)
      const thirtyDaysAgo = subDays(new Date(), 30);

      // Clean up old refresh tokens
      const deletedTokens = await this.prisma.refreshToken.deleteMany({
        where: {
          expires_at: {
            lt: new Date(),
          },
        },
      });

      // Clean up old OTPs
      const deletedOtps = await this.prisma.otp.deleteMany({
        where: {
          OR: [
            {
              expires_at: {
                lt: new Date(),
              },
            },
            {
              used: true,
              created_at: {
                lt: thirtyDaysAgo,
              },
            },
          ],
        },
      });

      this.logger.log(
        `Data cleanup completed: ${deletedTokens.count} tokens, ${deletedOtps.count} OTPs deleted`,
      );
    } catch (error) {
      this.logger.error('Error during data cleanup:', error);
    }
  }

  private async performHealthCheck() {
    this.logger.log('Performing health check...');

    try {
      // Check database connectivity
      await this.prisma.$queryRaw`SELECT 1`;

      // Check queue stats
      const queueStats = await this.queueService.getQueueStats();

      // Log any concerning queue statistics
      Object.entries(queueStats).forEach(([queueName, stats]) => {
        if (stats.failed > 10) {
          this.logger.warn(
            `Queue ${queueName} has ${stats.failed} failed jobs`,
          );
        }
        if (stats.waiting > 100) {
          this.logger.warn(
            `Queue ${queueName} has ${stats.waiting} waiting jobs`,
          );
        }
      });

      this.logger.log('Health check completed successfully');
    } catch (error) {
      this.logger.error('Health check failed:', error);

      // Send alert email to administrators
      await this.queueService.addEmailJob({
        to: this.configService.get('ADMIN_EMAIL') || '<EMAIL>',
        subject: 'System Health Check Failed',
        template: 'health-alert',
        context: {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }

  private async sendActivityReminders() {
    this.logger.log('Sending activity reminders...');

    try {
      // Find activities with upcoming follow-up dates
      const upcomingActivities = await this.prisma.activity.findMany({
        where: {
          next_followup_date: {
            gte: startOfDay(new Date()),
            lte: endOfDay(new Date()),
          },
          followup_status: 'pending',
        },
        include: {
          performed_by: true,
          lead: true,
        },
      });

      // Group activities by user
      const activitiesByUser = upcomingActivities.reduce(
        (acc, activity) => {
          const userId = activity.performed_by_user_id;
          if (!acc[userId]) {
            acc[userId] = [];
          }
          acc[userId].push(activity);
          return acc;
        },
        {} as Record<string, any[]>,
      );

      // Send reminder emails
      for (const [userId, activities] of Object.entries(activitiesByUser)) {
        const user = activities[0].performed_by;

        await this.queueService.addEmailJob({
          to: user.email,
          subject: `Activity Reminders - ${format(new Date(), 'yyyy-MM-dd')}`,
          template: 'activity-reminders',
          context: {
            userName: user.name,
            activities: activities.map((activity) => ({
              id: activity.id,
              customerName: activity.lead?.customer_name || 'Unknown',
              followupDate: format(
                activity.next_followup_date,
                'yyyy-MM-dd HH:mm',
              ),
              notes: activity.notes,
            })),
            totalCount: activities.length,
          },
        });
      }

      this.logger.log(
        `Sent activity reminders to ${Object.keys(activitiesByUser).length} users`,
      );
    } catch (error) {
      this.logger.error('Error sending activity reminders:', error);
    }
  }

  private async processScheduledTasks() {
    this.logger.log('🔍 Polling database for scheduled tasks...');

    try {
      // Get tasks ready for execution
      const tasksToProcess =
        await this.scheduledTaskService.getTasksReadyForExecution(50);

      if (tasksToProcess.length === 0) {
        this.logger.log('📭 No tasks ready for execution at this time');
        return;
      }

      this.logger.log(
        `🚀 Found ${tasksToProcess.length} tasks ready for execution`,
      );

      // Process tasks in smaller batches to prevent memory buildup
      const batchSize = 10;
      for (let i = 0; i < tasksToProcess.length; i += batchSize) {
        const batch = tasksToProcess.slice(i, i + batchSize);

        for (const task of batch) {
          try {
            // Add task to the appropriate queue
            await this.queueService.addScheduledTaskJob(
              {
                taskId: task.id,
                type: task.type,
                payload: task.payload,
                attemptNumber: task.attempts + 1,
              },
              {
                priority: task.priority,
                attempts: task.max_attempts,
              },
            );

            this.logger.log(
              `✅ Queued scheduled task: ${task.id} (${task.type}) - ${task.name}`,
            );
          } catch (error) {
            this.logger.error(
              `❌ Failed to queue task ${task.id} (${task.type}):`,
              error,
            );

            // Mark task as failed if we can't queue it
            await this.scheduledTaskService.markAsFailed(
              task.id,
              'system-execution-id', // We'll need to handle this better
              `Failed to queue task: ${error.message}`,
            );
          }
        }

        // Force garbage collection after each batch
        if (global.gc && i > 0) {
          global.gc();
        }

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < tasksToProcess.length) {
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      }

      this.logger.log(`Processed ${tasksToProcess.length} scheduled tasks`);
    } catch (error) {
      this.logger.error('Error processing scheduled tasks:', error);
    }
  }

  private async processRecurringTasks() {
    this.logger.log('Processing recurring tasks...');

    try {
      // Get recurring tasks that need to be rescheduled
      const recurringTasks =
        await this.scheduledTaskService.getRecurringTasksForScheduling(50);

      if (recurringTasks.length === 0) {
        return;
      }

      this.logger.log(
        `Found ${recurringTasks.length} recurring tasks to reschedule`,
      );

      // Process each recurring task
      for (const task of recurringTasks) {
        try {
          await this.rescheduleRecurringTask(task);
          this.logger.log(
            `Rescheduled recurring task: ${task.id} (${task.type})`,
          );
        } catch (error) {
          this.logger.error(`Failed to reschedule task ${task.id}:`, error);
        }
      }

      this.logger.log(`Processed ${recurringTasks.length} recurring tasks`);
    } catch (error) {
      this.logger.error('Error processing recurring tasks:', error);
    }
  }

  private async rescheduleRecurringTask(task: any) {
    let nextRunAt: Date;
    const now = new Date();

    if (task.cron_expression) {
      // Handle cron expressions
      // For now, we'll use a simple approach - you might want to use a cron parser library
      nextRunAt = addMinutes(now, 60); // Default to 1 hour for cron tasks
    } else if (task.interval_type && task.interval_value) {
      // Calculate next run time based on interval
      switch (task.interval_type) {
        case 'MINUTES':
          nextRunAt = addMinutes(now, task.interval_value);
          break;
        case 'HOURS':
          nextRunAt = addMinutes(now, task.interval_value * 60);
          break;
        case 'DAYS':
          nextRunAt = addMinutes(now, task.interval_value * 24 * 60);
          break;
        case 'WEEKS':
          nextRunAt = addMinutes(now, task.interval_value * 7 * 24 * 60);
          break;
        case 'MONTHS':
          nextRunAt = addMinutes(now, task.interval_value * 30 * 24 * 60); // Approximate
          break;
        default:
          throw new Error(`Unknown interval type: ${task.interval_type}`);
      }
    } else {
      throw new Error(
        'Recurring task must have either cron expression or interval',
      );
    }

    // Update the task with new run time
    await this.scheduledTaskService.update(task.id, {
      runAt: nextRunAt,
      status: 'PENDING',
    });

    // Reset task state for next execution
    await this.prisma.scheduledTask.update({
      where: { id: task.id },
      data: {
        attempts: 0,
        completed_at: null,
        failed_at: null,
        error_message: null,
        updated_at: new Date(),
      },
    });
  }
}
