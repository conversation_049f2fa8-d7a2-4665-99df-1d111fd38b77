import { Module } from '@nestjs/common';
import { SchedulerService } from './scheduler.service';
import { QueueModule } from '../queue/queue.module';
import { PrismaModule } from '../prisma/prisma.module';
import { ScheduledTaskModule } from '../scheduled-tasks/scheduled-task.module';

@Module({
  imports: [QueueModule, PrismaModule, ScheduledTaskModule],
  providers: [SchedulerService],
  exports: [SchedulerService],
})
export class SchedulerModule {}
