import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '@prisma/client';
import {
  CreateLeadNewDto,
  BulkCreateLeadsDto,
  CreateLeadFlexibleDto,
  ExcelUploadResponseDto,
} from './dto/create-lead-new.dto';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { Express } from 'express';
import { UpdateLeadDto } from './dto/update-lead.dto';
import {
  CreateLeadContactPersonDto,
  UpdateLeadContactPersonDto,
} from './dto/lead-contact-person.dto';
import { LeadSummaryResponseDto } from './dto/lead-summary-response.dto';
import { ConvertLeadDto } from './dto/convert-lead.dto';
import { ConvertLeadResponseDto } from './dto/convert-lead-response.dto';
import {
  LeadAttachmentsResponseDto,
  AttachmentInfoDto,
} from './dto/lead-attachments-response.dto';

import { PaginationDto } from '../common/dto/pagination.dto';

/**
 * Service handling all lead-related business logic
 * Provides CRUD operations and complex queries for leads
 */
@Injectable()
export class LeadsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new lead with the new format and returns summary data
   * Includes activity counts and last interaction information
   */
  async createNewFormat(
    createLeadNewDto: CreateLeadNewDto,
    authenticatedUser?: {
      id: string;
      name: string;
      email: string;
      rm_code: string;
      role_id: string;
      branch_id: string;
    },
  ): Promise<LeadSummaryResponseDto> {
    const {
      branchId,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryId,
      customerName,
      employerId,
      employerName,
      isicSectorId,
      leadType,
      leadStatus,
      phoneNumber,
      parentLeadId,
      anchorRelationshipId,
      anchorId,
    } = createLeadNewDto;

    // Convert empty strings to undefined
    const cleanBranchId =
      branchId && branchId.trim() !== '' ? branchId : undefined;
    const cleanCustomerCategoryId =
      customerCategoryId && customerCategoryId.trim() !== ''
        ? customerCategoryId
        : undefined;
    const cleanEmployerId =
      employerId && employerId.trim() !== '' ? employerId : undefined;
    const cleanIsicSectorId =
      isicSectorId && isicSectorId.trim() !== '' ? isicSectorId : undefined;
    const cleanParentLeadId =
      parentLeadId && parentLeadId.trim() !== '' ? parentLeadId : undefined;

    // Handle anchor relationship ID and anchor ID mapping
    const cleanAnchorRelationshipId =
      anchorRelationshipId && anchorRelationshipId.trim() !== ''
        ? anchorRelationshipId
        : undefined;

    const cleanAnchorId =
      anchorId && anchorId.trim() !== '' ? anchorId : undefined;

    // Validate foreign key relationships in parallel (only if IDs are provided)
    const [
      customerCategory,
      isicSector,
      branch,
      employer,
      parentLead,
      anchorRelationship,
      anchor,
    ] = await Promise.all([
      cleanCustomerCategoryId
        ? this.prisma.customerCategory.findUnique({
            where: { id: cleanCustomerCategoryId },
          })
        : null,
      cleanIsicSectorId
        ? this.prisma.iSICSector.findUnique({
            where: { id: cleanIsicSectorId },
          })
        : null,
      cleanBranchId
        ? this.prisma.branch.findUnique({ where: { id: cleanBranchId } })
        : null,
      cleanEmployerId
        ? this.prisma.employer.findUnique({ where: { id: cleanEmployerId } })
        : null,
      cleanParentLeadId
        ? this.prisma.lead.findUnique({ where: { id: cleanParentLeadId } })
        : null,
      cleanAnchorRelationshipId
        ? this.prisma.anchorRelationship.findUnique({
            where: { id: cleanAnchorRelationshipId },
          })
        : null,
      cleanAnchorId
        ? this.prisma.anchor.findUnique({ where: { id: cleanAnchorId } })
        : null,
    ]);

    // Validate relationships only if IDs are provided
    if (cleanCustomerCategoryId && !customerCategory) {
      throw new NotFoundException(
        `Customer category with ID '${cleanCustomerCategoryId}' not found`,
      );
    }
    if (cleanIsicSectorId && !isicSector) {
      throw new NotFoundException(
        `ISIC sector with ID '${cleanIsicSectorId}' not found`,
      );
    }
    if (cleanBranchId && !branch) {
      throw new NotFoundException(
        `Branch with ID '${cleanBranchId}' not found`,
      );
    }
    if (cleanEmployerId && !employer) {
      throw new NotFoundException(
        `Employer with ID '${cleanEmployerId}' not found`,
      );
    }
    if (cleanParentLeadId && !parentLead) {
      throw new NotFoundException(
        `Parent lead with ID '${cleanParentLeadId}' not found`,
      );
    }
    if (cleanAnchorRelationshipId && !anchorRelationship) {
      throw new NotFoundException(
        `Anchor relationship with ID '${cleanAnchorRelationshipId}' not found`,
      );
    }
    if (cleanAnchorId && !anchor) {
      throw new NotFoundException(
        `Anchor with ID '${cleanAnchorId}' not found`,
      );
    }

    // Handle employer name - create employer if name is provided but no ID
    let finalEmployerId = cleanEmployerId;
    if (!cleanEmployerId && employerName && employerName.trim() !== '') {
      // Check if employer with this name already exists
      const existingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: employerName.trim(),
            mode: 'insensitive',
          },
        },
      });

      if (existingEmployer) {
        finalEmployerId = existingEmployer.id;
      } else {
        // Create new employer
        const newEmployer = await this.prisma.employer.create({
          data: {
            name: employerName.trim(),
          },
        });
        finalEmployerId = newEmployer.id;
      }
    }

    // Use authenticated user as RM user if provided, otherwise fallback to branch-based lookup
    let rmUser;
    if (authenticatedUser) {
      // Use the authenticated user directly - no additional database query needed
      rmUser = {
        id: authenticatedUser.id,
        rm_code: authenticatedUser.rm_code,
        name: authenticatedUser.name,
        email: authenticatedUser.email,
        branch_id: authenticatedUser.branch_id,
      };
    } else {
      // Fallback to previous logic if no authenticated user is provided
      if (cleanBranchId) {
        rmUser = await this.prisma.user.findFirst({
          where: { branch_id: cleanBranchId },
        });
      } else {
        // If no branch is provided, get any user
        rmUser = await this.prisma.user.findFirst();
      }
    }

    // Create lead with contact person in a transaction
    const lead = await this.prisma.$transaction(async (tx) => {
      const newLead = await tx.lead.create({
        data: {
          customer_name: customerName || 'Unknown Customer',
          customer_category_id: cleanCustomerCategoryId || undefined,
          isic_sector_id: cleanIsicSectorId || undefined,
          phone_number: phoneNumber || undefined,
          type_of_lead: leadType || 'New',
          lead_status: leadStatus || 'Pending',
          branch_id: cleanBranchId || undefined,
          rm_user_id: rmUser?.id || undefined,
          employer_id: finalEmployerId || undefined,
          anchor_relationship_id: cleanAnchorRelationshipId || undefined,
          anchor_id: cleanAnchorId || cleanParentLeadId || undefined,
          contact_persons:
            contactPersonName && contactPersonPhone
              ? {
                  create: [
                    {
                      name: contactPersonName,
                      phone_number: contactPersonPhone,
                    },
                  ],
                }
              : undefined,
        },
      });

      return newLead;
    });

    // Fetch the created lead with all relationships to return proper summary data
    const createdLeadWithRelations = await this.prisma.lead.findUnique({
      where: { id: lead.id },
      include: this.getLeadIncludeOptions(),
    });

    // Return summary data using the same formatter as other methods
    return this.formatLeadSummaryResponse(createdLeadWithRelations);
  }

  /**
   * Creates multiple leads at once with automatic foreign key handling
   * For foreign key fields, if the value is not a UUID, attempts to find by name or creates new record
   */
  async createBulk(
    bulkCreateLeadsDto: BulkCreateLeadsDto,
    authenticatedUser?: {
      id: string;
      name: string;
      email: string;
      rm_code: string;
      role_id: string;
      branch_id: string;
    },
  ) {
    const { leads } = bulkCreateLeadsDto;
    const createdLeads: LeadSummaryResponseDto[] = [];
    const errors: Array<{ index: number; error: string; leadData: any }> = [];

    // Process leads in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < leads.length; i += batchSize) {
      const batch = leads.slice(i, i + batchSize);

      // Process each lead in the current batch
      for (let j = 0; j < batch.length; j++) {
        const leadIndex = i + j;
        const leadData = batch[j];

        try {
          const createdLead = await this.createSingleLeadWithFlexibleKeys(
            leadData,
            authenticatedUser,
          );
          createdLeads.push(createdLead);
        } catch (error) {
          errors.push({
            index: leadIndex,
            error: error.message || 'Unknown error occurred',
            leadData: leadData,
          });
        }
      }
    }

    return {
      success: errors.length === 0,
      message: `Successfully created ${createdLeads.length} leads${errors.length > 0 ? ` with ${errors.length} errors` : ''}`,
      totalCreated: createdLeads.length,
      createdLeads,
      errors,
    };
  }

  /**
   * Creates leads from an uploaded Excel file
   * Parses Excel file, maps columns to lead fields, and creates leads using bulk creation logic
   * Now supports anchor name matching from Excel data instead of a single anchor ID
   * @param file - The uploaded Excel file
   */
  async createFromExcel(
    file: Express.Multer.File,
    authenticatedUser?: {
      id: string;
      name: string;
      email: string;
      rm_code: string;
      role_id: string;
      branch_id: string;
    },
  ): Promise<ExcelUploadResponseDto> {
    try {
      // Load all existing anchors for fuzzy matching
      const existingAnchors = await this.prisma.anchor.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          phone_number: true,
        },
      });

      // Parse Excel file
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];

      if (!sheetName) {
        throw new BadRequestException('Excel file contains no sheets');
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      if (rawData.length === 0) {
        throw new BadRequestException('Excel file is empty');
      }

      // Extract headers and data rows
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1) as any[][];

      if (dataRows.length === 0) {
        throw new BadRequestException('Excel file contains no data rows');
      }

      // Map Excel columns to lead fields
      const columnMappings = this.mapExcelColumns(headers);

      // Convert Excel rows to lead objects with anchor processing
      const { leads, parseErrors, anchorCreationResults } =
        await this.parseExcelRowsWithAnchors(
          dataRows,
          headers,
          columnMappings,
          existingAnchors,
        );

      // Create leads using bulk creation logic
      const bulkResult = await this.createBulk({ leads }, authenticatedUser);

      // Combine parsing errors with creation errors
      const allErrors = [
        ...parseErrors,
        ...bulkResult.errors.map((error) => ({
          row: error.index + 2, // +2 because index is 0-based and we skip header row
          error: error.error,
          data: error.leadData,
        })),
      ];

      return {
        success: allErrors.length === 0,
        message: `Successfully processed ${dataRows.length} rows from Excel file`,
        totalRows: rawData.length,
        processedRows: dataRows.length,
        successfulCreations: bulkResult.totalCreated,
        failedCreations: allErrors.length,
        createdLeads: bulkResult.createdLeads,
        errors: allErrors,
        columnMappings,
        anchorCreationResults, // Include anchor creation results
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to process Excel file: ${error.message}`,
      );
    }
  }

  /**
   * Maps Excel column headers to lead field names
   * Uses exact and fuzzy matching to handle variations in column naming
   */
  private mapExcelColumns(headers: string[]): Record<string, string> {
    const mappings: Record<string, string> = {};

    // Define column mapping rules with priority (more specific patterns first)
    const columnRules = [
      {
        field: 'customerName',
        patterns: ['customer name', 'client name', 'lead name'],
      },
      {
        field: 'phoneNumber',
        patterns: ['phone number', 'mobile number', 'telephone'],
      },
      {
        field: 'branchIdentifier',
        patterns: ['branch', 'branch name', 'office', 'location'],
      },
      {
        field: 'customerCategoryIdentifier',
        patterns: ['customer category', 'category'],
      },
      {
        field: 'employerIdentifier',
        patterns: ['employer', 'company', 'organization', 'workplace'],
      },
      {
        field: 'isicSectorIdentifier',
        patterns: ['isic sector', 'sector', 'industry', 'business sector'],
      },
      { field: 'leadType', patterns: ['lead type', 'type of lead'] },
      { field: 'leadStatus', patterns: ['status', 'lead status', 'stage'] },
      {
        field: 'contactPersonName',
        patterns: ['contact person', 'contact name', 'representative'],
      },
      {
        field: 'contactPersonPhone',
        patterns: ['contact phone', 'contact number', 'representative phone'],
      },
      {
        field: 'clientId',
        patterns: ['client id', 'customer id', 'reference id'],
      },
      {
        field: 'anchorName',
        patterns: [
          'anchor',
          'anchor name',
          'anchor company',
          'parent company',
          'referring company',
          'source company',
        ],
      },
      {
        field: 'rmCode',
        patterns: [
          'rm',
          'rm code',
          'relationship manager',
          'assigned user',
          'assigned to',
          'user assigned',
          'officer',
          'assigned officer',
        ],
      },
      {
        field: 'assignedUser',
        patterns: [
          'assigned user id',
          'additional user',
          'secondary user',
          'backup user',
          'support user',
        ],
      },
      {
        field: 'accountNumber',
        patterns: [
          'account number',
          'account no',
          'acc number',
          'customer account',
          'account id',
        ],
      },
      {
        field: 'accountNumberAssignedAt',
        patterns: [
          'account assigned date',
          'account creation date',
          'account opened date',
          'account date',
        ],
      },
      {
        field: 'anchorRelationshipIdentifier',
        patterns: [
          'anchor relationship',
          'relationship type',
          'relationship with anchor',
          'anchor relation',
          'connection type',
        ],
      },
      // Fallback patterns (less specific)
      { field: 'customerName', patterns: ['name'] },
      { field: 'phoneNumber', patterns: ['phone', 'mobile', 'contact'] },
      { field: 'customerCategoryIdentifier', patterns: ['type'] },
    ];

    headers.forEach((header) => {
      const normalizedHeader = header.toLowerCase().trim();

      // Skip if already mapped
      if (mappings[header]) return;

      // Try exact matches first, then partial matches
      for (const rule of columnRules) {
        const exactMatch = rule.patterns.find(
          (pattern) => normalizedHeader === pattern,
        );
        if (exactMatch) {
          mappings[header] = rule.field;
          break;
        }
      }

      // If no exact match, try partial matches
      if (!mappings[header]) {
        for (const rule of columnRules) {
          const partialMatch = rule.patterns.find((pattern) =>
            normalizedHeader.includes(pattern),
          );
          if (partialMatch) {
            mappings[header] = rule.field;
            break;
          }
        }
      }
    });

    return mappings;
  }

  /**
   * Parses Excel data rows into lead objects
   * Handles data validation and type conversion
   * @param dataRows - Array of Excel data rows
   * @param headers - Array of Excel column headers
   * @param columnMappings - Mapping of Excel columns to lead fields
   * @param anchorId - Optional parent lead ID to assign to all leads
   */
  private parseExcelRows(
    dataRows: any[][],
    headers: string[],
    columnMappings: Record<string, string>,
    anchorId?: string,
  ): {
    leads: CreateLeadFlexibleDto[];
    parseErrors: Array<{ row: number; error: string; data: any }>;
  } {
    const leads: CreateLeadFlexibleDto[] = [];
    const parseErrors: Array<{ row: number; error: string; data: any }> = [];

    dataRows.forEach((row, rowIndex) => {
      try {
        const leadData: CreateLeadFlexibleDto = {};
        const rowData: Record<string, any> = {};

        // Map row data to object using headers
        headers.forEach((header, colIndex) => {
          const value = row[colIndex];
          rowData[header] = value;

          const fieldName = columnMappings[header];
          if (
            fieldName &&
            value !== undefined &&
            value !== null &&
            value !== ''
          ) {
            // Clean and convert the value
            const cleanValue = String(value).trim();
            if (cleanValue) {
              (leadData as any)[fieldName] = cleanValue;
            }
          }
        });

        // Validate required fields or set defaults
        if (!leadData.customerName) {
          // Try to find customer name in unmapped columns
          const possibleNameColumns = headers.filter(
            (h) => h.toLowerCase().includes('name') && !columnMappings[h],
          );
          if (possibleNameColumns.length > 0) {
            const nameValue = rowData[possibleNameColumns[0]];
            if (nameValue) {
              leadData.customerName = String(nameValue).trim();
            }
          }
        }

        // Set default values if not provided
        if (!leadData.leadType) {
          leadData.leadType = 'New';
        }
        if (!leadData.leadStatus) {
          leadData.leadStatus = 'Pending';
        }

        // Validate phone number format if provided
        if (leadData.phoneNumber) {
          leadData.phoneNumber = this.normalizePhoneNumber(
            leadData.phoneNumber,
          );
        }

        // Assign anchorId as parent_lead_id if provided
        if (anchorId) {
          leadData.parentLeadId = anchorId;
        }

        leads.push(leadData);
      } catch (error) {
        parseErrors.push({
          row: rowIndex + 2, // +2 because rowIndex is 0-based and we skip header row
          error: `Row parsing error: ${error.message}`,
          data: row,
        });
      }
    });

    return { leads, parseErrors };
  }

  /**
   * Calculates similarity percentage between two strings using multiple algorithms
   * @param str1 - First string to compare
   * @param str2 - Second string to compare
   * @returns Similarity percentage (0-100)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 100;
    if (s1.length === 0 || s2.length === 0) return 0;

    // Check for substring matches (high weight for business names)
    if (s1.includes(s2) || s2.includes(s1)) {
      const shorter = s1.length < s2.length ? s1 : s2;
      const longer = s1.length >= s2.length ? s1 : s2;
      return (shorter.length / longer.length) * 100;
    }

    // Check for word-based similarity (good for business names with different suffixes)
    const words1 = s1.split(/\s+/).filter((w) => w.length > 2); // Ignore short words
    const words2 = s2.split(/\s+/).filter((w) => w.length > 2);

    if (words1.length > 0 && words2.length > 0) {
      let matchingWords = 0;
      for (const word1 of words1) {
        for (const word2 of words2) {
          if (
            word1 === word2 ||
            word1.includes(word2) ||
            word2.includes(word1)
          ) {
            matchingWords++;
            break;
          }
        }
      }
      const wordSimilarity =
        (matchingWords / Math.max(words1.length, words2.length)) * 100;

      // If word similarity is high, return it
      if (wordSimilarity >= 60) {
        return wordSimilarity;
      }
    }

    // Fallback to Levenshtein distance
    const matrix = Array(s2.length + 1)
      .fill(null)
      .map(() => Array(s1.length + 1).fill(null));

    for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= s2.length; j++) {
      for (let i = 1; i <= s1.length; i++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j - 1][i] + 1, // deletion
          matrix[j][i - 1] + 1, // insertion
          matrix[j - 1][i - 1] + cost, // substitution
        );
      }
    }

    const maxLength = Math.max(s1.length, s2.length);
    const distance = matrix[s2.length][s1.length];
    const similarity = ((maxLength - distance) / maxLength) * 100;

    return Math.round(similarity * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Finds the best matching anchor for a given anchor name
   * @param anchorName - Name to search for
   * @param existingAnchors - List of existing anchors
   * @param threshold - Minimum similarity threshold (default: 60%)
   * @returns Best matching anchor or null if no match above threshold
   */
  private findBestAnchorMatch(
    anchorName: string,
    existingAnchors: Array<{
      id: string;
      name: string;
      email: string;
      phone_number: string;
    }>,
    threshold: number = 60,
  ): {
    anchor: { id: string; name: string; email: string; phone_number: string };
    similarity: number;
  } | null {
    if (!anchorName || !anchorName.trim()) return null;

    let bestMatch: {
      anchor: { id: string; name: string; email: string; phone_number: string };
      similarity: number;
    } | null = null;
    let highestSimilarity = 0;

    for (const anchor of existingAnchors) {
      const similarity = this.calculateStringSimilarity(
        anchorName,
        anchor.name,
      );

      if (similarity >= threshold && similarity > highestSimilarity) {
        highestSimilarity = similarity;
        bestMatch = { anchor, similarity };
      }
    }

    return bestMatch;
  }

  /**
   * Creates a new anchor from anchor name
   * @param anchorName - Name of the anchor to create
   * @returns Created anchor
   */
  private async createAnchorFromName(anchorName: string): Promise<{
    id: string;
    name: string;
    email: string;
    phone_number: string;
  }> {
    // Generate a basic email and phone number for the new anchor
    const sanitizedName = anchorName.toLowerCase().replace(/[^a-z0-9]/g, '');
    const email = `contact@${sanitizedName}.com`;
    const phoneNumber = '0700000000'; // Default placeholder phone number

    const newAnchor = await this.prisma.anchor.create({
      data: {
        name: anchorName.trim(),
        email: email,
        phone_number: phoneNumber,
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone_number: true,
      },
    });

    return newAnchor;
  }

  /**
   * Parses Excel data rows into lead objects with anchor name processing
   * Handles anchor name matching and creation
   * @param dataRows - Array of Excel data rows
   * @param headers - Array of Excel column headers
   * @param columnMappings - Mapping of Excel columns to lead fields
   * @param existingAnchors - List of existing anchors for matching
   */
  private async parseExcelRowsWithAnchors(
    dataRows: any[][],
    headers: string[],
    columnMappings: Record<string, string>,
    existingAnchors: Array<{
      id: string;
      name: string;
      email: string;
      phone_number: string;
    }>,
  ): Promise<{
    leads: CreateLeadFlexibleDto[];
    parseErrors: Array<{ row: number; error: string; data: any }>;
    anchorCreationResults: Array<{
      row: number;
      anchorName: string;
      action: 'matched' | 'created' | 'skipped';
      anchorId?: string;
      similarity?: number;
      matchedAnchorName?: string;
    }>;
  }> {
    const leads: CreateLeadFlexibleDto[] = [];
    const parseErrors: Array<{ row: number; error: string; data: any }> = [];
    const anchorCreationResults: Array<{
      row: number;
      anchorName: string;
      action: 'matched' | 'created' | 'skipped';
      anchorId?: string;
      similarity?: number;
      matchedAnchorName?: string;
    }> = [];

    // Keep track of anchors created during this import to avoid duplicates
    const newlyCreatedAnchors = new Map<
      string,
      { id: string; name: string; email: string; phone_number: string }
    >();

    for (let rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
      const row = dataRows[rowIndex];

      try {
        const leadData: CreateLeadFlexibleDto = {};
        const rowData: Record<string, any> = {};
        let anchorName: string | undefined = undefined;
        let rmCode: string | undefined = undefined;

        // Map row data to object using headers
        headers.forEach((header, colIndex) => {
          const value = row[colIndex];
          rowData[header] = value;

          const fieldName = columnMappings[header];
          if (
            fieldName &&
            value !== undefined &&
            value !== null &&
            value !== ''
          ) {
            // Clean and convert the value
            const cleanValue = String(value).trim();
            if (cleanValue) {
              if (fieldName === 'anchorName') {
                anchorName = cleanValue;
              } else if (fieldName === 'rmCode') {
                rmCode = cleanValue;
              } else {
                (leadData as any)[fieldName] = cleanValue;
              }
            }
          }
        });

        // Process anchor name if provided
        if (anchorName && typeof anchorName === 'string') {
          // First check if we already created this anchor in this import
          const normalizedAnchorName = (anchorName as string)
            .toLowerCase()
            .trim();
          let targetAnchor = newlyCreatedAnchors.get(normalizedAnchorName);

          if (targetAnchor) {
            // Use the anchor we created earlier in this import
            leadData.anchorId = targetAnchor.id;
            anchorCreationResults.push({
              row: rowIndex + 2,
              anchorName: anchorName,
              action: 'matched',
              anchorId: targetAnchor.id,
              similarity: 100,
              matchedAnchorName: targetAnchor.name,
            });
          } else {
            // Check for existing anchors with fuzzy matching
            const bestMatch = this.findBestAnchorMatch(
              anchorName,
              existingAnchors,
              60,
            );

            if (bestMatch) {
              // Found a matching existing anchor
              leadData.anchorId = bestMatch.anchor.id;
              anchorCreationResults.push({
                row: rowIndex + 2,
                anchorName: anchorName,
                action: 'matched',
                anchorId: bestMatch.anchor.id,
                similarity: bestMatch.similarity,
                matchedAnchorName: bestMatch.anchor.name,
              });
            } else {
              // No match found, create new anchor
              try {
                const newAnchor = await this.createAnchorFromName(anchorName);
                leadData.anchorId = newAnchor.id;

                // Add to our tracking maps
                newlyCreatedAnchors.set(normalizedAnchorName, newAnchor);
                existingAnchors.push(newAnchor); // Add to existing anchors for future matches

                anchorCreationResults.push({
                  row: rowIndex + 2,
                  anchorName: anchorName,
                  action: 'created',
                  anchorId: newAnchor.id,
                });
              } catch (error) {
                // If anchor creation fails, log it but continue with the lead
                parseErrors.push({
                  row: rowIndex + 2,
                  error: `Failed to create anchor "${anchorName}": ${error.message}`,
                  data: row,
                });

                anchorCreationResults.push({
                  row: rowIndex + 2,
                  anchorName: anchorName,
                  action: 'skipped',
                });
              }
            }
          }
        }

        // Process RM code if provided
        if (rmCode && typeof rmCode === 'string') {
          try {
            const rmUser = await this.prisma.user.findFirst({
              where: {
                rm_code: (rmCode as string).trim(),
              },
            });

            if (rmUser) {
              leadData.rmUserId = rmUser.id;
            } else {
              // Log warning but continue processing
              console.warn(
                `RM code '${rmCode}' not found for row ${rowIndex + 2}`,
              );
            }
          } catch (error) {
            console.error(
              `Error looking up RM code '${rmCode}' for row ${rowIndex + 2}:`,
              error,
            );
          }
        }

        // Process anchor relationship identifier if provided
        if (
          leadData.anchorRelationshipIdentifier &&
          typeof leadData.anchorRelationshipIdentifier === 'string'
        ) {
          try {
            const anchorRelationship =
              await this.prisma.anchorRelationship.findFirst({
                where: {
                  name: {
                    contains: leadData.anchorRelationshipIdentifier.trim(),
                    mode: 'insensitive',
                  },
                },
              });

            if (anchorRelationship) {
              leadData.anchorRelationshipId = anchorRelationship.id;
              // Remove the identifier since we now have the ID
              delete leadData.anchorRelationshipIdentifier;
            } else {
              // Log warning but continue processing
              console.warn(
                `Anchor relationship '${leadData.anchorRelationshipIdentifier}' not found for row ${rowIndex + 2}`,
              );
            }
          } catch (error) {
            console.error(
              `Error looking up anchor relationship '${leadData.anchorRelationshipIdentifier}' for row ${rowIndex + 2}:`,
              error,
            );
          }
        }

        // Process account number assigned date if provided
        if (
          leadData.accountNumberAssignedAt &&
          typeof leadData.accountNumberAssignedAt === 'string'
        ) {
          try {
            // Validate and parse the date
            const parsedDate = new Date(leadData.accountNumberAssignedAt);
            if (isNaN(parsedDate.getTime())) {
              parseErrors.push({
                row: rowIndex + 2,
                error: `Invalid account assigned date format: ${leadData.accountNumberAssignedAt}`,
                data: row,
              });
              delete leadData.accountNumberAssignedAt;
            } else {
              // Keep as ISO string for DTO validation
              leadData.accountNumberAssignedAt = parsedDate.toISOString();
            }
          } catch (error) {
            parseErrors.push({
              row: rowIndex + 2,
              error: `Error parsing account assigned date: ${error.message}`,
              data: row,
            });
            delete leadData.accountNumberAssignedAt;
          }
        }

        // Validate required fields or set defaults (same as original logic)
        if (!leadData.customerName) {
          // Try to find customer name in unmapped columns
          const possibleNameColumns = headers.filter(
            (h) => h.toLowerCase().includes('name') && !columnMappings[h],
          );
          if (possibleNameColumns.length > 0) {
            const nameValue = rowData[possibleNameColumns[0]];
            if (nameValue) {
              leadData.customerName = String(nameValue).trim();
            }
          }
        }

        // Set default values if not provided
        if (!leadData.leadType) {
          leadData.leadType = 'New';
        }
        if (!leadData.leadStatus) {
          leadData.leadStatus = 'Pending';
        }

        // Validate phone number format if provided
        if (leadData.phoneNumber) {
          leadData.phoneNumber = this.normalizePhoneNumber(
            leadData.phoneNumber,
          );
        }

        leads.push(leadData);
      } catch (error) {
        parseErrors.push({
          row: rowIndex + 2, // +2 because rowIndex is 0-based and we skip header row
          error: `Row parsing error: ${error.message}`,
          data: row,
        });
      }
    }

    return { leads, parseErrors, anchorCreationResults };
  }

  /**
   * Normalizes phone number format
   * Handles various input formats and converts to standard format
   */
  private normalizePhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\s+/g, '').replace(/[^\d+]/g, '');

    // Handle different formats
    if (cleaned.startsWith('+254')) {
      return cleaned;
    } else if (cleaned.startsWith('254')) {
      return '+' + cleaned;
    } else if (cleaned.startsWith('07') || cleaned.startsWith('01')) {
      return '+254' + cleaned.substring(1);
    } else if (
      cleaned.length === 9 &&
      (cleaned.startsWith('7') || cleaned.startsWith('1'))
    ) {
      return '+254' + cleaned;
    }

    return phone; // Return original if no pattern matches
  }

  /**
   * Helper method to create a single lead with flexible foreign key handling
   * Resolves foreign keys by UUID or name, creating new records if needed
   */
  private async createSingleLeadWithFlexibleKeys(
    leadData: CreateLeadFlexibleDto,
    authenticatedUser?: {
      id: string;
      name: string;
      email: string;
      rm_code: string;
      role_id: string;
      branch_id: string;
    },
  ): Promise<LeadSummaryResponseDto> {
    const {
      branchIdentifier,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryIdentifier,
      customerName,
      employerIdentifier,
      isicSectorIdentifier,
      leadType,
      phoneNumber,
      parentLeadId,
      leadStatus,
      anchorId,
      rmUserId,
      assignedUser,
      accountNumber,
      accountNumberAssignedAt,
      anchorRelationshipIdentifier,
      anchorRelationshipId,
    } = leadData;

    // Clean up empty strings
    const cleanParentLeadId =
      parentLeadId && parentLeadId.trim() !== '' ? parentLeadId : undefined;

    // Resolve foreign key relationships
    const [
      branchId,
      customerCategoryId,
      employerId,
      isicSectorId,
      parentLead,
      resolvedAnchorRelationshipId,
    ] = await Promise.all([
      this.resolveBranchId(branchIdentifier),
      this.resolveCustomerCategoryId(customerCategoryIdentifier, undefined),
      this.resolveEmployerId(employerIdentifier),
      this.resolveIsicSectorId(isicSectorIdentifier),
      cleanParentLeadId
        ? this.prisma.lead.findUnique({ where: { id: cleanParentLeadId } })
        : null,
      this.resolveAnchorRelationshipId(
        anchorRelationshipIdentifier,
        anchorRelationshipId,
      ),
    ]);

    // Validate parent lead exists if provided
    if (cleanParentLeadId && !parentLead) {
      throw new NotFoundException(
        `Parent lead with ID '${cleanParentLeadId}' not found`,
      );
    }

    // Use authenticated user as RM user if provided, otherwise fallback to branch-based lookup
    let rmUser;
    if (authenticatedUser) {
      // Use the authenticated user directly - no additional database query needed
      rmUser = {
        id: authenticatedUser.id,
        rm_code: authenticatedUser.rm_code,
        name: authenticatedUser.name,
        email: authenticatedUser.email,
        branch_id: authenticatedUser.branch_id,
      };
    } else {
      // Fallback to previous logic if no authenticated user is provided
      if (branchId) {
        rmUser = await this.prisma.user.findFirst({
          where: { branch_id: branchId },
        });
      } else {
        rmUser = await this.prisma.user.findFirst();
      }
    }

    // Create lead with contact person in a transaction
    const lead = await this.prisma.$transaction(async (tx) => {
      const newLead = await tx.lead.create({
        data: {
          customer_name: customerName || 'Unknown Customer',
          customer_category_id: customerCategoryId || undefined,
          isic_sector_id: isicSectorId || undefined,
          phone_number: phoneNumber || undefined,
          type_of_lead: leadType || 'New',
          lead_status: leadStatus || 'Pending',
          branch_id: branchId || undefined,
          rm_user_id: rmUserId || rmUser?.id || undefined,
          assigned_user: assignedUser || undefined,
          employer_id: employerId || undefined,
          anchor_id: anchorId || cleanParentLeadId || undefined,
          anchor_relationship_id: resolvedAnchorRelationshipId || undefined,
          account_number: accountNumber || undefined,
          account_number_assigned_at: accountNumberAssignedAt
            ? new Date(accountNumberAssignedAt)
            : undefined,
          contact_persons:
            contactPersonName && contactPersonPhone
              ? {
                  create: [
                    {
                      name: contactPersonName,
                      phone_number: contactPersonPhone,
                    },
                  ],
                }
              : undefined,
        },
      });

      return newLead;
    });

    // Return summary data
    return {
      id: lead.id,
      parent_lead_id: null,
      anchor_relationship_id: null,
      lead_name: customerName || 'Unknown Customer',
      status: leadStatus || 'Pending', // Return actual lead_status from database, default to 'Pending'
      phoneNumber: phoneNumber || '',
      account_number: null, // New leads don't have account numbers
      account_number_assigned_at: null, // New leads haven't been converted
      no_of_visits: 0,
      no_of_calls: 0,
      last_interaction: null,
      officer: null,
      customer_category: null,
      isic_sector: null,
      branch: null,
      employerName: null,
      parent_lead_name: null,
      anchor_relationship_name: null, // New leads don't have parent lead data loaded
      anchor: null, // New leads don't have anchor data loaded in this context
      created_at: lead.created_at.toISOString(),
    };
  }

  /**
   * Retrieves all leads with pagination, search, and filtering
   * Optimized with selective field inclusion and parallel queries
   */
  async findAll(paginationDto: PaginationDto) {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    // Build dynamic where clause for search functionality
    // Only return leads without account numbers (unconverted leads)
    const whereClause: Prisma.LeadWhereInput = {
      account_number: null, // Filter to only show unconverted leads
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            type_of_lead: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            rm_user: {
              name: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
          {
            branch: {
              name: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
        ],
      }),
    };

    // Execute queries in parallel for better performance
    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: this.getLeadIncludeOptions(),
        orderBy: {
          created_at: 'desc' as const,
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    // Format leads with activity counts and summary data
    const formattedLeads = await Promise.all(
      leads.map(async (lead) => await this.formatLeadSummaryResponse(lead)),
    );

    return {
      data: formattedLeads,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Retrieves all leads without pagination
   * Useful for exports, reports, or when you need the complete dataset
   * @param search - Optional search term to filter leads
   */
  async findAllWithoutPagination(search?: string) {
    // Build search conditions (same as paginated version)
    // Only return leads without account numbers (unconverted leads)
    const whereClause = {
      account_number: null, // Filter to only show unconverted leads
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            type_of_lead: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            rm_user: {
              name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          },
          {
            branch: {
              name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          },
        ],
      }),
    };

    // Execute queries in parallel for better performance
    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        include: this.getLeadIncludeOptions(),
        orderBy: {
          created_at: 'desc' as const,
        },
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    // Format leads with activity counts and summary data
    const formattedLeads = await Promise.all(
      leads.map(async (lead) => await this.formatLeadSummaryResponse(lead)),
    );

    return {
      data: formattedLeads,
      total,
      message: `Retrieved all ${total} leads successfully${search ? ` matching "${search}"` : ''}`,
    };
  }

  /**
   * Exports all leads to Excel file
   * Creates a comprehensive Excel file with all lead data and relationships
   * @param search - Optional search term to filter leads
   * @returns Buffer containing the Excel file
   */
  async exportLeadsToExcel(search?: string): Promise<Buffer> {
    // Get all leads data (same as findAllWithoutPagination but with more detailed data)
    // Only export leads without account numbers (unconverted leads)
    const whereClause = {
      account_number: null, // Filter to only export unconverted leads
      ...(search && {
        OR: [
          {
            customer_name: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            type_of_lead: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
          {
            rm_user: {
              name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          },
          {
            branch: {
              name: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          },
        ],
      }),
    };

    // Fetch leads with comprehensive data
    const leads = await this.prisma.lead.findMany({
      where: whereClause,
      include: {
        ...this.getLeadIncludeOptions(),
        activities: {
          orderBy: {
            created_at: 'desc',
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Leads Export');

    // Define columns with proper headers
    worksheet.columns = [
      { header: 'Lead Name', key: 'customerName', width: 25 },
      { header: 'Phone Number', key: 'phoneNumber', width: 20 },
      { header: 'Client ID', key: 'clientId', width: 15 },
      { header: 'Lead Type', key: 'leadType', width: 15 },
      { header: 'Lead Status', key: 'leadStatus', width: 15 },
      { header: 'Parent Lead', key: 'parentLead', width: 25 },
      { header: 'Branch', key: 'branch', width: 20 },
      { header: 'Region', key: 'region', width: 20 },
      { header: 'Customer Category', key: 'customerCategory', width: 20 },
      { header: 'ISIC Sector', key: 'isicSector', width: 25 },
      { header: 'Employer', key: 'employer', width: 25 },
      { header: 'RM User', key: 'rmUser', width: 25 },
      { header: 'Contact Persons', key: 'contactPersons', width: 30 },
      { header: 'No. of Visits', key: 'visitCount', width: 15 },
      { header: 'No. of Calls', key: 'callCount', width: 15 },
      { header: 'Last Interaction', key: 'lastInteraction', width: 20 },
      { header: 'Created Date', key: 'createdDate', width: 20 },
    ];

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '366092' },
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add data rows
    leads.forEach((lead: any) => {
      const contactPersons =
        lead.contact_persons
          ?.map((cp: any) => `${cp.name} (${cp.phone_number || 'No phone'})`)
          .join('; ') || '';

      const lastActivity = lead.activities?.[0];
      const lastInteraction = lastActivity
        ? lastActivity.created_at?.toISOString().split('T')[0]
        : null;

      // Count visits and calls from activities
      const visitCount =
        lead.activities?.filter((a: any) => a.activity_type === 'visit')
          .length || 0;
      const callCount =
        lead.activities?.filter((a: any) => a.activity_type === 'call')
          .length || 0;

      worksheet.addRow({
        customerName: lead.customer_name || 'Unknown Customer',
        phoneNumber: lead.phone_number || '',
        leadType: lead.type_of_lead || '',
        leadStatus: lead.lead_status || '',
        parentLead: lead.leads?.customer_name || '',
        branch: lead.branch?.name || '',
        region: lead.branch?.region?.name || '',
        customerCategory: lead.customer_category?.name || '',
        isicSector: lead.isic_sector?.name || '',
        employer: lead.employer?.name || '',
        rmUser: lead.rm_user?.name || '',
        contactPersons: contactPersons,
        visitCount: visitCount,
        callCount: callCount,
        lastInteraction: lastInteraction,
        createdDate: '', // Lead model doesn't have created_at field
      });
    });

    // Auto-fit columns and add borders
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        // Alternate row colors for better readability
        if (rowNumber > 1 && rowNumber % 2 === 0) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' },
          };
        }
      });
    });

    // Add summary information at the bottom
    const summaryStartRow = leads.length + 3;
    worksheet.getCell(`A${summaryStartRow}`).value = 'Export Summary:';
    worksheet.getCell(`A${summaryStartRow}`).font = { bold: true };
    worksheet.getCell(`A${summaryStartRow + 1}`).value =
      `Total Leads: ${leads.length}`;
    worksheet.getCell(`A${summaryStartRow + 2}`).value =
      `Export Date: ${new Date().toISOString().split('T')[0]}`;
    worksheet.getCell(`A${summaryStartRow + 3}`).value =
      `Search Filter: ${search || 'None'}`;

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Retrieves a single lead by ID with all related data
   * Includes comprehensive relationship data and activity counts
   */
  async findOne(id: string) {
    const lead = await this.prisma.lead.findUnique({
      where: { id },
      include: {
        ...this.getLeadIncludeOptions(),
        // Include counts for additional insights
      },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    return this.formatLeadResponse(lead, true);
  }

  /**
   * Updates a lead with validation of foreign key relationships
   * Handles partial updates and maintains data integrity
   * Returns summary data format consistent with create and list endpoints
   */
  async update(
    id: string,
    updateLeadDto: UpdateLeadDto,
  ): Promise<LeadSummaryResponseDto> {
    const existingLead = await this.prisma.lead.findUnique({
      where: { id },
    });

    if (!existingLead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    const {
      branchId,
      contactPersonName,
      contactPersonPhone,
      createdDate,
      customerCategoryId,
      customerName,
      employerId,
      employerName,
      isicSectorId,
      leadType,
      leadStatus,
      phoneNumber,
      parentLeadId,
      anchorRelationshipId,
      anchorId,
    } = updateLeadDto;

    // Convert empty strings to undefined
    const cleanBranchId =
      branchId && branchId.trim() !== '' ? branchId : undefined;
    const cleanCustomerCategoryId =
      customerCategoryId && customerCategoryId.trim() !== ''
        ? customerCategoryId
        : undefined;
    const cleanEmployerId =
      employerId && employerId.trim() !== '' ? employerId : undefined;
    const cleanIsicSectorId =
      isicSectorId && isicSectorId.trim() !== '' ? isicSectorId : undefined;
    const cleanParentLeadId =
      parentLeadId && parentLeadId.trim() !== '' ? parentLeadId : undefined;

    // Handle anchor relationship ID and anchor ID mapping
    const cleanAnchorRelationshipId =
      anchorRelationshipId && anchorRelationshipId.trim() !== ''
        ? anchorRelationshipId
        : undefined;

    const cleanAnchorId =
      anchorId && anchorId.trim() !== '' ? anchorId : undefined;

    // Validate foreign key relationships in parallel (only if IDs are provided)
    const [
      customerCategory,
      isicSector,
      branch,
      employer,
      parentLead,
      anchorRelationship,
      anchor,
    ] = await Promise.all([
      cleanCustomerCategoryId
        ? this.prisma.customerCategory.findUnique({
            where: { id: cleanCustomerCategoryId },
          })
        : null,
      cleanIsicSectorId
        ? this.prisma.iSICSector.findUnique({
            where: { id: cleanIsicSectorId },
          })
        : null,
      cleanBranchId
        ? this.prisma.branch.findUnique({ where: { id: cleanBranchId } })
        : null,
      cleanEmployerId
        ? this.prisma.employer.findUnique({ where: { id: cleanEmployerId } })
        : null,
      cleanParentLeadId
        ? this.prisma.lead.findUnique({ where: { id: cleanParentLeadId } })
        : null,
      cleanAnchorRelationshipId
        ? this.prisma.anchorRelationship.findUnique({
            where: { id: cleanAnchorRelationshipId },
          })
        : null,
      cleanAnchorId
        ? this.prisma.anchor.findUnique({ where: { id: cleanAnchorId } })
        : null,
    ]);

    // Validate relationships only if IDs are provided
    if (cleanCustomerCategoryId && !customerCategory) {
      throw new NotFoundException(
        `Customer category with ID '${cleanCustomerCategoryId}' not found`,
      );
    }
    if (cleanIsicSectorId && !isicSector) {
      throw new NotFoundException(
        `ISIC sector with ID '${cleanIsicSectorId}' not found`,
      );
    }
    if (cleanBranchId && !branch) {
      throw new NotFoundException(
        `Branch with ID '${cleanBranchId}' not found`,
      );
    }
    if (cleanEmployerId && !employer) {
      throw new NotFoundException(
        `Employer with ID '${cleanEmployerId}' not found`,
      );
    }
    if (cleanParentLeadId && !parentLead) {
      throw new NotFoundException(
        `Parent lead with ID '${cleanParentLeadId}' not found`,
      );
    }
    if (cleanAnchorRelationshipId && !anchorRelationship) {
      throw new NotFoundException(
        `Anchor relationship with ID '${cleanAnchorRelationshipId}' not found`,
      );
    }
    if (cleanAnchorId && !anchor) {
      throw new NotFoundException(
        `Anchor with ID '${cleanAnchorId}' not found`,
      );
    }

    // Handle employer name - create employer if name is provided but no ID
    let finalEmployerId = cleanEmployerId;
    if (!cleanEmployerId && employerName && employerName.trim() !== '') {
      // Check if employer with this name already exists
      const existingEmployer = await this.prisma.employer.findFirst({
        where: {
          name: {
            equals: employerName.trim(),
            mode: 'insensitive',
          },
        },
      });

      if (existingEmployer) {
        finalEmployerId = existingEmployer.id;
      } else {
        // Create new employer
        const newEmployer = await this.prisma.employer.create({
          data: {
            name: employerName.trim(),
          },
        });
        finalEmployerId = newEmployer.id;
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (customerName !== undefined) updateData.customer_name = customerName;
    if (cleanCustomerCategoryId !== undefined)
      updateData.customer_category_id = cleanCustomerCategoryId;
    if (cleanIsicSectorId !== undefined)
      updateData.isic_sector_id = cleanIsicSectorId;
    if (phoneNumber !== undefined) updateData.phone_number = phoneNumber;

    // leadType maps to type_of_lead field (existing field)
    if (leadType !== undefined) updateData.type_of_lead = leadType;

    // leadStatus maps to lead_status field (new field)
    if (leadStatus !== undefined) updateData.lead_status = leadStatus;

    if (cleanBranchId !== undefined) updateData.branch_id = cleanBranchId;
    if (finalEmployerId !== undefined) updateData.employer_id = finalEmployerId;
    if (cleanAnchorRelationshipId !== undefined)
      updateData.anchor_relationship_id = cleanAnchorRelationshipId;
    if (cleanAnchorId !== undefined) updateData.anchor_id = cleanAnchorId;
    if (cleanParentLeadId !== undefined)
      updateData.parent_lead_id = cleanParentLeadId;

    // Update the lead
    await this.prisma.lead.update({
      where: { id },
      data: updateData,
    });

    // Fetch the updated lead with all related data
    const updatedLead = await this.prisma.lead.findUnique({
      where: { id },
      include: this.getLeadIncludeOptions(),
    });

    // Handle contact person updates if provided
    if (contactPersonName !== undefined || contactPersonPhone !== undefined) {
      // For simplicity, we'll update the first contact person or create one if none exists
      const existingContactPerson =
        await this.prisma.leadContactPerson.findFirst({
          where: { lead_id: id },
        });

      if (existingContactPerson) {
        // Update existing contact person
        const contactUpdateData: any = {};
        if (contactPersonName !== undefined)
          contactUpdateData.name = contactPersonName;
        if (contactPersonPhone !== undefined)
          contactUpdateData.phone_number = contactPersonPhone;

        if (Object.keys(contactUpdateData).length > 0) {
          await this.prisma.leadContactPerson.update({
            where: { id: existingContactPerson.id },
            data: contactUpdateData,
          });
        }
      } else if (contactPersonName && contactPersonPhone) {
        // Create new contact person if both name and phone are provided
        await this.prisma.leadContactPerson.create({
          data: {
            lead_id: id,
            name: contactPersonName,
            phone_number: contactPersonPhone,
          },
        });
      }
    }

    // Fetch the updated lead with all relationships to return proper summary data
    const updatedLeadWithRelations = await this.prisma.lead.findUnique({
      where: { id },
      include: this.getLeadIncludeOptions(),
    });

    // Return summary data format
    return this.formatLeadSummaryResponse(updatedLeadWithRelations);
  }

  /**
   * Updates the RM user ID of a lead
   * @param id - UUID of the lead to update
   * @param rmUserId - UUID of the RM user to assign to the lead
   * @returns Promise<LeadSummaryResponseDto> - Updated lead summary data
   */
  async updateLeadRm(id: string, rmUserId: string): Promise<LeadSummaryResponseDto> {
    // Verify the lead exists
    const existingLead = await this.prisma.lead.findUnique({
      where: { id },
    });

    if (!existingLead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    // Verify the RM user exists
    const rmUser = await this.prisma.user.findUnique({
      where: { id: rmUserId },
    });

    if (!rmUser) {
      throw new NotFoundException(`User with ID '${rmUserId}' not found`);
    }

    // Update the lead with the new RM user ID
    const updatedLead = await this.prisma.lead.update({
      where: { id },
      data: {
        rm_user_id: rmUserId,
      },
      include: this.getLeadIncludeOptions(),
    });

    // Return summary data format
    return this.formatLeadSummaryResponse(updatedLead);
  }

  /**
   * Deletes a lead and all associated contact persons
   * Checks for dependencies before deletion
   */
  async remove(id: string): Promise<void> {
    const lead = await this.prisma.lead.findUnique({
      where: { id },
      include: {},
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    // Note: Since we removed the relations, we can proceed with deletion
    // In a production environment, you might want to add specific dependency checks

    // Delete lead and associated contact persons in a transaction
    await this.prisma.$transaction(async (tx) => {
      // Delete contact persons first
      await tx.leadContactPerson.deleteMany({
        where: { lead_id: id },
      });

      // Delete the lead
      await tx.lead.delete({
        where: { id },
      });
    });
  }

  /**
   * Adds a contact person to a lead
   */
  async addContactPerson(createContactPersonDto: CreateLeadContactPersonDto) {
    const { lead_id, name, phone_number } = createContactPersonDto;

    // Verify lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: lead_id },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${lead_id}' not found`);
    }

    const contactPerson = await this.prisma.leadContactPerson.create({
      data: {
        lead_id,
        name,
        phone_number,
      },
    });

    return contactPerson;
  }

  /**
   * Updates a contact person
   */
  async updateContactPerson(
    id: string,
    updateContactPersonDto: UpdateLeadContactPersonDto,
  ) {
    const existingContactPerson =
      await this.prisma.leadContactPerson.findUnique({
        where: { id },
      });

    if (!existingContactPerson) {
      throw new NotFoundException(`Contact person with ID '${id}' not found`);
    }

    const updatedContactPerson = await this.prisma.leadContactPerson.update({
      where: { id },
      data: updateContactPersonDto,
    });

    return updatedContactPerson;
  }

  /**
   * Deletes a contact person
   */
  async removeContactPerson(id: string): Promise<void> {
    const contactPerson = await this.prisma.leadContactPerson.findUnique({
      where: { id },
    });

    if (!contactPerson) {
      throw new NotFoundException(`Contact person with ID '${id}' not found`);
    }

    await this.prisma.leadContactPerson.delete({
      where: { id },
    });
  }

  /**
   * Gets contact persons for a specific lead
   */
  async getLeadContactPersons(leadId: string, paginationDto: PaginationDto) {
    // Verify lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: Prisma.LeadContactPersonWhereInput = {
      lead_id: leadId,
      ...(search && {
        OR: [
          {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            phone_number: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ],
      }),
    };

    const [contactPersons, total] = await Promise.all([
      this.prisma.leadContactPerson.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          name: 'asc' as const,
        },
      }),
      this.prisma.leadContactPerson.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: contactPersons,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Helper method to resolve branch ID from identifier (UUID or name)
   * Creates new branch if name is provided and doesn't exist
   */
  private async resolveBranchId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!branch) {
        throw new NotFoundException(
          `Branch with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let branch = await this.prisma.branch.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new branch (requires a default region)
    if (!branch) {
      // Get the first available region or create a default one
      let defaultRegion = await this.prisma.region.findFirst();
      if (!defaultRegion) {
        defaultRegion = await this.prisma.region.create({
          data: { name: 'Default Region' },
        });
      }

      branch = await this.prisma.branch.create({
        data: {
          name: cleanIdentifier,
          region_id: defaultRegion.id,
        },
      });
    }

    return branch.id;
  }

  /**
   * Helper method to resolve customer category ID from identifier (UUID or name)
   * Creates new customer category if name is provided and doesn't exist
   */
  private async resolveCustomerCategoryId(
    identifier?: string,
    userId?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const category = await this.prisma.customerCategory.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!category) {
        throw new NotFoundException(
          `Customer category with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let category = await this.prisma.customerCategory.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new category (only if userId is provided)
    if (!category) {
      if (!userId) {
        throw new BadRequestException(
          `Customer category '${cleanIdentifier}' not found and cannot create without user context`,
        );
      }
      category = await this.prisma.customerCategory.create({
        data: {
          name: cleanIdentifier,
          added_by: userId,
        },
      });
    }

    return category.id;
  }

  /**
   * Helper method to resolve employer ID from identifier (UUID or name)
   * Creates new employer if name is provided and doesn't exist
   */
  private async resolveEmployerId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const employer = await this.prisma.employer.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!employer) {
        throw new NotFoundException(
          `Employer with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let employer = await this.prisma.employer.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new employer
    if (!employer) {
      employer = await this.prisma.employer.create({
        data: { name: cleanIdentifier },
      });
    }

    return employer.id;
  }

  /**
   * Helper method to resolve ISIC sector ID from identifier (UUID or name)
   * Creates new ISIC sector if name is provided and doesn't exist
   */
  private async resolveIsicSectorId(
    identifier?: string,
  ): Promise<string | undefined> {
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const sector = await this.prisma.iSICSector.findUnique({
        where: { id: cleanIdentifier },
      });
      if (!sector) {
        throw new NotFoundException(
          `ISIC sector with ID '${cleanIdentifier}' not found`,
        );
      }
      return cleanIdentifier;
    }

    // Try to find by name
    let sector = await this.prisma.iSICSector.findFirst({
      where: {
        name: {
          equals: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    // If not found, create new ISIC sector
    if (!sector) {
      sector = await this.prisma.iSICSector.create({
        data: {
          name: cleanIdentifier,
          code: '', // Default empty code
        },
      });
    }

    return sector.id;
  }

  /**
   * Helper method to resolve anchor relationship ID from identifier (UUID or name)
   * Does not create new anchor relationships - only resolves existing ones
   */
  private async resolveAnchorRelationshipId(
    identifier?: string,
    directId?: string,
  ): Promise<string | undefined> {
    // If direct ID is provided, validate and return it
    if (directId && directId.trim() !== '') {
      const cleanId = directId.trim();
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

      if (uuidRegex.test(cleanId)) {
        // Validate UUID exists
        const relationship = await this.prisma.anchorRelationship.findUnique({
          where: { id: cleanId },
        });
        return relationship ? relationship.id : undefined;
      }
    }

    // If identifier is provided, try to find by name
    if (!identifier || identifier.trim() === '') {
      return undefined;
    }

    const cleanIdentifier = identifier.trim();

    // Check if it's a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanIdentifier)) {
      // Validate UUID exists
      const relationship = await this.prisma.anchorRelationship.findUnique({
        where: { id: cleanIdentifier },
      });
      return relationship ? relationship.id : undefined;
    }

    // Try to find by name (case-insensitive)
    const relationship = await this.prisma.anchorRelationship.findFirst({
      where: {
        name: {
          contains: cleanIdentifier,
          mode: 'insensitive',
        },
      },
    });

    return relationship ? relationship.id : undefined;
  }

  /**
   * Private helper method to get standardized include options for lead queries
   * Optimizes database queries by selecting only necessary fields
   */
  private getLeadIncludeOptions() {
    return {
      customer_category: true,
      isic_sector: true,
      branch: {
        include: {
          region: true,
        },
      },
      rm_user: true,
      employer: true,
      anchor_relationship: true,
      anchor: true, // Include the new anchor relationship
      assigned_user_rel: true, // Include the assigned user relationship
      contact_persons: {
        orderBy: {
          name: 'asc' as const,
        },
      },
    } as const;
  }

  /**
   * Private helper method to format lead response data
   * Transforms database result into standardized API response format
   */
  private formatLeadResponse(lead: any, includeCounts = false) {
    const response = {
      id: lead.id,
      customer_name: lead.customer_name,
      parent_lead_id: lead.parent_lead_id,
      anchor_id: lead.anchor_id,
      phone_number: lead.phone_number,
      type_of_lead: lead.type_of_lead,
      account_number: lead.account_number,
      account_number_assigned_at: lead.account_number_assigned_at,
      customer_category: lead.customer_category
        ? {
            id: lead.customer_category.id,
            name: lead.customer_category.name,
          }
        : null,
      isic_sector: lead.isic_sector
        ? {
            id: lead.isic_sector.id,
            name: lead.isic_sector.name,
          }
        : null,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
            region: lead.branch.region
              ? {
                  id: lead.branch.region.id,
                  name: lead.branch.region.name,
                }
              : null,
          }
        : null,
      rm_user: lead.rm_user
        ? {
            id: lead.rm_user.id,
            name: lead.rm_user.name,
            email: lead.rm_user.email,
            rm_code: lead.rm_user.rm_code,
          }
        : null,
      employer: lead.employer
        ? {
            id: lead.employer.id,
            name: lead.employer.name,
          }
        : null,
      anchor: lead.anchor
        ? {
            id: lead.anchor.id,
            name: lead.anchor.name,
            email: lead.anchor.email,
            phone_number: lead.anchor.phone_number,
          }
        : null,
      parent_lead: lead.leads
        ? {
            id: lead.leads.id,
            name: lead.leads.customer_name,
          }
        : null,
      contact_persons: lead.contact_persons || [],
      created_at: lead.created_at.toISOString(),
    };

    // Add counts if requested (for detailed view)
    if (includeCounts && lead._count) {
      return {
        ...response,
        referredLeadsCount: lead._count.other_leads,
        activitiesCount: lead._count.general_activities,
      };
    }

    return response;
  }

  /**
   * Formats a lead for summary response with activity counts
   */
  private async formatLeadSummaryResponse(
    lead: any,
  ): Promise<LeadSummaryResponseDto> {
    // Get activity counts for this lead
    const [visitCount, callCount, lastActivity, latestActivityWithOfficer] =
      await Promise.all([
        this.prisma.activity.count({
          where: {
            lead_id: lead.id,
            interaction_type: 'visit',
          },
        }),
        this.prisma.activity.count({
          where: {
            lead_id: lead.id,
            interaction_type: 'call',
          },
        }),
        this.prisma.activity.findFirst({
          where: {
            lead_id: lead.id,
          },
          orderBy: {
            created_at: 'desc',
          },
          select: {
            created_at: true,
            interaction_type: true,
          },
        }),
        this.prisma.activity.findFirst({
          where: {
            lead_id: lead.id,
          },
          orderBy: {
            created_at: 'desc',
          },
          select: {
            performed_by: {
              select: {
                name: true,
              },
            },
          },
        }),
      ]);

    return {
      id: lead.id,
      parent_lead_id: lead.parent_lead_id,
      anchor_relationship_id: lead.anchor_relationship_id,
      lead_name: lead.customer_name || 'Unknown Customer',
      status: lead.lead_status || null, // Return actual lead_status from database as 'status'
      phoneNumber: lead.phone_number || '',
      account_number: lead.account_number,
      account_number_assigned_at: lead.account_number_assigned_at,
      no_of_visits: visitCount,
      no_of_calls: callCount,
      last_interaction: lastActivity
        ? {
            interaction_type: lastActivity.interaction_type || 'unknown',
            date: lastActivity.created_at.toISOString(),
          }
        : null,
      officer: latestActivityWithOfficer?.performed_by?.name || null,
      customer_category: lead.customer_category
        ? {
            id: lead.customer_category.id,
            name: lead.customer_category.name,
          }
        : null,
      isic_sector: lead.isic_sector
        ? {
            id: lead.isic_sector.id,
            name: lead.isic_sector.name,
          }
        : null,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
            region: {
              id: lead.branch.region.id,
              name: lead.branch.region.name,
            },
          }
        : null,
      employerName: lead.employer?.name || null,
      parent_lead_name: lead.leads?.customer_name || null,
      anchor_relationship_name: lead.anchor_relationship?.name || null,
      anchor: lead.anchor
        ? {
            id: lead.anchor.id,
            name: lead.anchor.name,
            email: lead.anchor.email,
            phone_number: lead.anchor.phone_number,
          }
        : null,
      created_at: lead.created_at.toISOString(),
    };
  }

  /**
   * Converts a lead by assigning an account number and generating a client ID
   * @param id - UUID of the lead to convert
   * @param convertLeadDto - Data containing the account number
   * @returns Promise<ConvertLeadResponseDto> - The lead ID
   */
  async convertLead(
    id: string,
    convertLeadDto: ConvertLeadDto,
  ): Promise<ConvertLeadResponseDto> {
    const { account_number } = convertLeadDto;

    // Check if lead exists
    const existingLead = await this.prisma.lead.findUnique({
      where: { id },
    });

    if (!existingLead) {
      throw new NotFoundException(`Lead with ID '${id}' not found`);
    }

    // Check if account number is already in use
    const existingAccountNumber = await this.prisma.lead.findFirst({
      where: {
        account_number: account_number,
        id: { not: id }, // Exclude current lead
      },
    });

    if (existingAccountNumber) {
      throw new ConflictException(
        `Account number '${account_number}' is already in use`,
      );
    }

    // Update the lead with account number and timestamp
    await this.prisma.lead.update({
      where: { id },
      data: {
        account_number: account_number,
        account_number_assigned_at: new Date(),
      },
    });

    return {
      lead_id: id,
    };
  }

  /**
   * Gets all attachments for a specific lead
   * Includes attachments from regular activities (calls and visits)
   * @param leadId - The ID of the lead to get attachments for
   * @returns Promise<LeadAttachmentsResponseDto> - All attachments with creation timestamps
   */
  async getLeadAttachments(
    leadId: string,
  ): Promise<LeadAttachmentsResponseDto> {
    // First verify the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
      select: { id: true, customer_name: true },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Get all attachments from regular activities (calls/visits)
    const activityAttachments = await this.prisma.activityAttachment.findMany({
      where: {
        activity: {
          lead_id: leadId,
        },
      },
      include: {
        activity: {
          select: {
            id: true,
            activity_type: true,
            interaction_type: true,
            notes: true,
            created_at: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    // Transform attachments to response format
    const attachments: AttachmentInfoDto[] = [];

    // Add regular activity attachments
    activityAttachments.forEach((attachment) => {
      attachments.push({
        id: attachment.id,
        file_url: attachment.file_url || '',
        created_at:
          attachment.created_at?.toISOString() || new Date().toISOString(),
        updated_at:
          attachment.updated_at?.toISOString() || new Date().toISOString(),
        activity: attachment.activity
          ? {
              id: attachment.activity.id,
              activity_type: attachment.activity.activity_type,
              interaction_type: attachment.activity.interaction_type || '',
              notes: attachment.activity.notes,
              created_at: attachment.activity.created_at.toISOString(),
            }
          : undefined,
      });
    });

    // Sort all attachments by creation date (newest first)
    attachments.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    );

    // Calculate summary statistics
    const callActivities = attachments.filter(
      (att) => att.activity?.interaction_type === 'call',
    ).length;
    const visitActivities = attachments.filter(
      (att) => att.activity?.interaction_type === 'visit',
    ).length;

    // Calculate date range
    const dates = attachments.map((att) => new Date(att.created_at));
    const earliest =
      dates.length > 0
        ? new Date(Math.min(...dates.map((d) => d.getTime()))).toISOString()
        : null;
    const latest =
      dates.length > 0
        ? new Date(Math.max(...dates.map((d) => d.getTime()))).toISOString()
        : null;

    return {
      lead_id: leadId,
      lead_name: lead.customer_name || 'Unknown Customer',
      total_attachments: attachments.length,
      attachments,
      summary: {
        call_activities: callActivities,
        visit_activities: visitActivities,
      },
      date_range: {
        earliest,
        latest,
      },
    };
  }

  /**
   * Get RBAC-enabled lead analytics based on user permissions
   * @param user - Authenticated user with permissions
   * @param filters - Optional filters for branch and date range
   * @returns Promise<RbacLeadAnalyticsResponse> - Analytics data based on permissions
   */
  async getRbacLeadAnalytics(
    user: {
      id: string;
      permissions: string[];
      [key: string]: any;
    },
    filters: {
      branchId?: string;
      startDate?: string;
      endDate?: string;
    } = {},
  ) {
    try {
      // Validate user object
      if (!user || !user.id) {
        throw new BadRequestException('Invalid user data: User ID is required');
      }

      if (!user.permissions || !Array.isArray(user.permissions)) {
        throw new BadRequestException(
          'Invalid user data: User permissions are required',
        );
      }

      // Determine access level based on permissions
      const canViewAllLeads = user.permissions.includes('view.all.leads');
      const canViewMyLeads = user.permissions.includes('view.my.leads');

      if (!canViewAllLeads && !canViewMyLeads) {
        throw new BadRequestException(
          'Insufficient permissions to view lead analytics. Required: view.all.leads or view.my.leads',
        );
      }

      // Build base where clause based on permissions
      const baseWhereClause: any = {};

      if (!canViewAllLeads && canViewMyLeads) {
        // User can only see their own leads
        baseWhereClause.rm_user_id = user.id;
      }
      // If canViewAllLeads is true, no additional filter is needed (see all leads)

      // Add branch filter if provided
      if (filters.branchId) {
        baseWhereClause.branch_id = filters.branchId;
      }

      // Add date range filter if provided
      if (filters.startDate || filters.endDate) {
        baseWhereClause.created_at = {};
        if (filters.startDate) {
          baseWhereClause.created_at.gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          baseWhereClause.created_at.lte = new Date(filters.endDate);
        }
      }

      // Calculate MTD (Month-to-Date) range
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(
        now.getFullYear(),
        now.getMonth() + 1,
        0,
        23,
        59,
        59,
        999,
      );

      // Execute all queries in parallel for optimal performance
      const [
        totalLeads,
        contactedLeads,
        pendingLeads,
        warmLeads,
        hotLeads,
        coldLeads,
        totalVisitsMTD,
        totalCallsMTD,
      ] = await Promise.all([
        // Total leads count
        this.prisma.lead.count({
          where: baseWhereClause,
        }),

        // Contacted leads count (leads that have activities with call or visit)
        this.prisma.lead.count({
          where: {
            ...baseWhereClause,
            activities: {
              some: {
                OR: [
                  { interaction_type: 'call' },
                  { interaction_type: 'visit' },
                ],
              },
            },
          },
        }),

        // Leads by status - Pending (case-insensitive)
        this.prisma.lead.count({
          where: {
            ...baseWhereClause,
            lead_status: {
              equals: 'pending',
              mode: 'insensitive',
            },
          },
        }),

        // Leads by status - Warm (case-insensitive)
        this.prisma.lead.count({
          where: {
            ...baseWhereClause,
            lead_status: {
              equals: 'warm',
              mode: 'insensitive',
            },
          },
        }),

        // Leads by status - Hot (case-insensitive)
        this.prisma.lead.count({
          where: {
            ...baseWhereClause,
            lead_status: {
              equals: 'hot',
              mode: 'insensitive',
            },
          },
        }),

        // Leads by status - Cold (case-insensitive)
        this.prisma.lead.count({
          where: {
            ...baseWhereClause,
            lead_status: {
              equals: 'cold',
              mode: 'insensitive',
            },
          },
        }),

        // Total visits made MTD by the logged-in user
        this.prisma.activity.count({
          where: {
            performed_by_user_id: user.id,
            interaction_type: 'visit',
            created_at: {
              gte: startOfMonth,
              lte: endOfMonth,
            },
          },
        }),

        // Total calls made MTD by the logged-in user
        this.prisma.activity.count({
          where: {
            performed_by_user_id: user.id,
            interaction_type: 'call',
            created_at: {
              gte: startOfMonth,
              lte: endOfMonth,
            },
          },
        }),
      ]);

      return {
        total_leads: totalLeads,
        contacted_leads: contactedLeads,
        leads_by_status: {
          pending: pendingLeads,
          warm: warmLeads,
          hot: hotLeads,
          cold: coldLeads,
        },
        user_activity_mtd: {
          total_visits: totalVisitsMTD,
          total_calls: totalCallsMTD,
          month_year: `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`,
        },
        user_permissions: {
          can_view_all_leads: canViewAllLeads,
          applied_filter: canViewAllLeads ? 'all_leads' : 'my_leads_only',
        },
        filters_applied: {
          branch_id: filters.branchId || null,
          start_date: filters.startDate || null,
          end_date: filters.endDate || null,
        },
      };
    } catch (error) {
      // Handle database connection errors
      if (
        error.code === 'P1001' ||
        error.message?.includes("Can't reach database server")
      ) {
        throw new BadRequestException(
          'Database connection failed. Please try again later.',
        );
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException(
          'Database connection failed. Please try again later.',
        );
      }

      // Re-throw BadRequestException (our custom validation errors)
      if (error instanceof BadRequestException) {
        throw error;
      }

      console.error('Error in getRbacLeadAnalytics:', error);
      throw new BadRequestException('Failed to retrieve lead analytics');
    }
  }

  /**
   * Get RBAC-enabled leads list based on user permissions
   * @param user - Authenticated user with permissions
   * @param options - Pagination and filter options
   * @returns Promise<RbacLeadsResponse> - Paginated leads data based on permissions
   */
  async getRbacLeads(
    user: {
      id: string;
      permissions: string[];
      [key: string]: any;
    },
    options: {
      page: number;
      limit: number;
      branchId?: string;
      status?: string;
      startDate?: string;
      endDate?: string;
    },
  ) {
    const {
      page = 1,
      limit = 10,
      branchId,
      status,
      startDate,
      endDate,
    } = options;

    // Determine access level based on permissions
    const canViewAllLeads = user.permissions.includes('view.all.leads');
    const canViewMyLeads = user.permissions.includes('view.my.leads');

    if (!canViewAllLeads && !canViewMyLeads) {
      throw new BadRequestException('Insufficient permissions to view leads');
    }

    // Validate and sanitize pagination parameters
    const validatedPage = Math.max(1, page);
    const validatedLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page
    const skip = (validatedPage - 1) * validatedLimit;

    // Build where clause based on permissions
    const whereClause: any = {};

    if (!canViewAllLeads && canViewMyLeads) {
      // User can only see their own leads
      whereClause.rm_user_id = user.id;
    }
    // If canViewAllLeads is true, no additional filter is needed (see all leads)

    // Add additional filters
    if (branchId) {
      whereClause.branch_id = branchId;
    }

    if (status) {
      whereClause.lead_status = status.toLowerCase();
    }

    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) {
        whereClause.created_at.gte = new Date(startDate);
      }
      if (endDate) {
        whereClause.created_at.lte = new Date(endDate);
      }
    }

    // Execute queries in parallel for better performance
    const [leads, total] = await Promise.all([
      this.prisma.lead.findMany({
        where: whereClause,
        include: this.getLeadIncludeOptions(),
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: validatedLimit,
      }),
      this.prisma.lead.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(total / validatedLimit);

    // Format leads with activity counts
    const formattedLeads = await Promise.all(
      leads.map(async (lead) => await this.formatLeadSummaryResponse(lead)),
    );

    return {
      data: formattedLeads,
      meta: {
        total,
        page: validatedPage,
        limit: validatedLimit,
        totalPages,
        hasNextPage: validatedPage < totalPages,
        hasPreviousPage: validatedPage > 1,
        user_permissions: {
          can_view_all_leads: canViewAllLeads,
          applied_filter: canViewAllLeads ? 'all_leads' : 'my_leads_only',
        },
      },
    };
  }
}
