import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsOptional,
  <PERSON><PERSON>ength,
  IsDateString,
  ValidateIf,
  IsIn,
  IsArray,
  ValidateNested,
  ArrayNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LeadSummaryResponseDto } from './lead-summary-response.dto';

/**
 * Data Transfer Object for creating a new lead with the new format
 * Validates input data and provides API documentation
 */
export class CreateLeadNewDto {
  @ApiPropertyOptional({
    description: 'UUID of the branch',
    example: '523b569f-30ae-46f4-bccb-f48c368b8a81',
  })
  @IsOptional()
  @ValidateIf((o) => o.branchId && o.branchId.trim() !== '')
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branchId?: string;



  @ApiPropertyOptional({
    description: 'Name of the contact person',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  @MaxLength(255, {
    message: 'Contact person name cannot exceed 255 characters',
  })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Phone number of the contact person',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  @MaxLength(20, {
    message: 'Contact person phone cannot exceed 20 characters',
  })
  contactPersonPhone?: string;

  @ApiPropertyOptional({
    description: 'Date when the lead was created',
    example: '2025-07-28T12:15:01.512Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created date must be a valid ISO date string' })
  createdDate?: string;

  @ApiPropertyOptional({
    description: 'UUID of the customer category',
    example: '5315bbfb-0215-415c-9ef0-9ecbe91248f2',
  })
  @IsOptional()
  @ValidateIf((o) => o.customerCategoryId && o.customerCategoryId.trim() !== '')
  @IsUUID(4, { message: 'Customer category ID must be a valid UUID' })
  customerCategoryId?: string;

  @ApiPropertyOptional({
    description: 'Name of the customer',
    example: 'Cole Palmer',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Customer name must be a string' })
  @MaxLength(255, { message: 'Customer name cannot exceed 255 characters' })
  customerName?: string;

  @ApiPropertyOptional({
    description: 'UUID of the employer',
    example: 'b7728663-86d8-41a9-9238-8c3533a114a9',
  })
  @IsOptional()
  @ValidateIf((o) => o.employerId && o.employerId.trim() !== '')
  @IsUUID(4, { message: 'Employer ID must be a valid UUID' })
  employerId?: string;

  @ApiPropertyOptional({
    description: 'Name of the employer (alternative to employerId)',
    example: 'ABC Corporation',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Employer name must be a string' })
  @MaxLength(255, { message: 'Employer name cannot exceed 255 characters' })
  employerName?: string;

  @ApiPropertyOptional({
    description: 'UUID of the ISIC sector',
    example: 'f71002e5-1eef-4c53-9096-e421c0d40bad',
  })
  @IsOptional()
  @ValidateIf((o) => o.isicSectorId && o.isicSectorId.trim() !== '')
  @IsUUID(4, { message: 'ISIC sector ID must be a valid UUID' })
  isicSectorId?: string;

  @ApiPropertyOptional({
    description: 'Type of lead - indicates if customer is new or existing',
    example: 'New',
    enum: ['New', 'Existing'],
  })
  @IsOptional()
  @IsString({ message: 'Lead type must be a string' })
  @IsIn(['New', 'Existing'], {
    message: 'Lead type must be either "New" or "Existing"',
  })
  leadType?: string;

  @ApiPropertyOptional({
    description: 'Primary phone number of the customer',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  @MaxLength(20, { message: 'Phone number cannot exceed 20 characters' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'UUID of the parent lead (for child leads)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.parentLeadId && o.parentLeadId.trim() !== '')
  @IsUUID(4, { message: 'Parent lead ID must be a valid UUID' })
  parentLeadId?: string;

  @ApiPropertyOptional({
    description:
      'Status of the lead - indicates lead temperature/qualification level',
    example: 'Pending',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  @MaxLength(50, { message: 'Lead status cannot exceed 50 characters' })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'UUID of the anchor relationship',
    example: 'c7e41fac-ac4a-4659-9d80-9d23cd653a76',
  })
  @IsOptional()
  @ValidateIf(
    (o) => o.anchorRelationshipId && o.anchorRelationshipId.trim() !== '',
  )
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;

  @ApiPropertyOptional({
    description: 'UUID of the anchor relationship (alternative field name)',
    example: 'c7e41fac-ac4a-4659-9d80-9d23cd653a76',
  })
  @IsOptional()
  @ValidateIf((o) => o.anchorId && o.anchorId.trim() !== '')
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;
}

/**
 * Data Transfer Object for creating a lead with flexible foreign key handling
 * Allows both UUIDs and names for foreign key relationships
 */
export class CreateLeadFlexibleDto {
  @ApiPropertyOptional({
    description: 'Branch ID (UUID) or branch name',
    example: '523b569f-30ae-46f4-bccb-f48c368b8a81',
  })
  @IsOptional()
  @IsString({ message: 'Branch identifier must be a string' })
  @MaxLength(255, { message: 'Branch identifier cannot exceed 255 characters' })
  branchIdentifier?: string;



  @ApiPropertyOptional({
    description: 'Contact person name',
    example: 'John Doe',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Contact person name must be a string' })
  @MaxLength(255, { message: 'Contact person name cannot exceed 255 characters' })
  contactPersonName?: string;

  @ApiPropertyOptional({
    description: 'Contact person phone number',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Contact person phone must be a string' })
  @MaxLength(20, { message: 'Contact person phone cannot exceed 20 characters' })
  contactPersonPhone?: string;

  @ApiPropertyOptional({
    description: 'Date when the lead was created',
    example: '2025-07-28T12:15:01.512Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created date must be a valid ISO date string' })
  createdDate?: string;

  @ApiPropertyOptional({
    description: 'Customer category ID (UUID) or customer category name',
    example: '5315bbfb-0215-415c-9ef0-9ecbe91248f2',
  })
  @IsOptional()
  @IsString({ message: 'Customer category identifier must be a string' })
  @MaxLength(255, { message: 'Customer category identifier cannot exceed 255 characters' })
  customerCategoryIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Name of the customer',
    example: 'Cole Palmer',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Customer name must be a string' })
  @MaxLength(255, { message: 'Customer name cannot exceed 255 characters' })
  customerName?: string;

  @ApiPropertyOptional({
    description: 'Employer ID (UUID) or employer name',
    example: 'b7728663-86d8-41a9-9238-8c3533a114a9',
  })
  @IsOptional()
  @IsString({ message: 'Employer identifier must be a string' })
  @MaxLength(255, { message: 'Employer identifier cannot exceed 255 characters' })
  employerIdentifier?: string;

  @ApiPropertyOptional({
    description: 'ISIC sector ID (UUID) or ISIC sector name',
    example: 'f71002e5-1eef-4c53-9096-e421c0d40bad',
  })
  @IsOptional()
  @IsString({ message: 'ISIC sector identifier must be a string' })
  @MaxLength(255, { message: 'ISIC sector identifier cannot exceed 255 characters' })
  isicSectorIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Type of lead - indicates if customer is new or existing',
    example: 'New',
    enum: ['New', 'Existing'],
  })
  @IsOptional()
  @IsString({ message: 'Lead type must be a string' })
  @IsIn(['New', 'Existing'], {
    message: 'Lead type must be either "New" or "Existing"',
  })
  leadType?: string;

  @ApiPropertyOptional({
    description: 'Primary phone number of the customer',
    example: '+************',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  @MaxLength(20, { message: 'Phone number cannot exceed 20 characters' })
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Parent lead ID (UUID) - for child leads',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.parentLeadId && o.parentLeadId.trim() !== '')
  @IsUUID(4, { message: 'Parent lead ID must be a valid UUID' })
  parentLeadId?: string;

  @ApiPropertyOptional({
    description: 'Status of the lead - indicates lead temperature/qualification level',
    example: 'Pending',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Lead status must be a string' })
  @MaxLength(50, { message: 'Lead status cannot exceed 50 characters' })
  leadStatus?: string;

  @ApiPropertyOptional({
    description: 'Anchor name for fuzzy matching (used in Excel import)',
    example: 'ABC Corporation',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Anchor name must be a string' })
  @MaxLength(100, { message: 'Anchor name cannot exceed 100 characters' })
  anchorName?: string;

  @ApiPropertyOptional({
    description: 'Anchor ID (UUID) - resolved from anchor name during processing',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.anchorId && o.anchorId.trim() !== '')
  @IsUUID(4, { message: 'Anchor ID must be a valid UUID' })
  anchorId?: string;

  @ApiPropertyOptional({
    description: 'RM code for user assignment (used in Excel import)',
    example: 'RM001',
    maxLength: 20,
  })
  @IsOptional()
  @IsString({ message: 'RM code must be a string' })
  @MaxLength(20, { message: 'RM code cannot exceed 20 characters' })
  rmCode?: string;

  @ApiPropertyOptional({
    description: 'RM User ID (UUID) - resolved from RM code during processing',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.rmUserId && o.rmUserId.trim() !== '')
  @IsUUID(4, { message: 'RM User ID must be a valid UUID' })
  rmUserId?: string;

  @ApiPropertyOptional({
    description: 'Assigned user ID (UUID) - additional user assignment',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.assignedUser && o.assignedUser.trim() !== '')
  @IsUUID(4, { message: 'Assigned user ID must be a valid UUID' })
  assignedUser?: string;

  @ApiPropertyOptional({
    description: 'Account number (when assigned to customer)',
    example: 'ACC-2024-001234',
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Account number must be a string' })
  @MaxLength(50, { message: 'Account number cannot exceed 50 characters' })
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Date when account number was assigned',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Account number assigned date must be a valid ISO date string' })
  accountNumberAssignedAt?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship identifier (name) for relationship type',
    example: 'Employee',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Anchor relationship identifier must be a string' })
  @MaxLength(100, { message: 'Anchor relationship identifier cannot exceed 100 characters' })
  anchorRelationshipIdentifier?: string;

  @ApiPropertyOptional({
    description: 'Anchor relationship ID (UUID) - resolved from identifier during processing',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.anchorRelationshipId && o.anchorRelationshipId.trim() !== '')
  @IsUUID(4, { message: 'Anchor relationship ID must be a valid UUID' })
  anchorRelationshipId?: string;
}

/**
 * Data Transfer Object for bulk lead creation
 * Allows creating multiple leads at once with flexible foreign key handling
 */
export class BulkCreateLeadsDto {
  @ApiProperty({
    description: 'Array of leads to create',
    type: [CreateLeadFlexibleDto],
    example: [
      {
        customerName: 'John Doe',
        phoneNumber: '+************',
        branchIdentifier: 'Nairobi Branch',
        customerCategoryIdentifier: 'Individual',
        employerIdentifier: 'ABC Company',
        isicSectorIdentifier: 'Manufacturing',
        leadType: 'New',
        leadStatus: 'Pending'
      }
    ],
  })
  @IsArray({ message: 'Leads must be an array' })
  @ArrayNotEmpty({ message: 'At least one lead is required' })
  @ValidateNested({ each: true })
  @Type(() => CreateLeadFlexibleDto)
  leads: CreateLeadFlexibleDto[];
}

/**
 * Data Transfer Object for Excel file upload
 * No longer requires anchor ID as anchors are processed from Excel data
 */
export class ExcelUploadDto {
  // No additional fields needed - anchor information comes from Excel file
}

/**
 * Data Transfer Object for Excel file upload response
 * Contains parsing results and creation status
 */
export class ExcelUploadResponseDto {
  @ApiProperty({
    description: 'Whether the upload was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Summary message of the upload operation',
    example: 'Successfully processed 50 rows from Excel file',
  })
  message: string;

  @ApiProperty({
    description: 'Total number of rows found in Excel file',
    example: 52,
  })
  totalRows: number;

  @ApiProperty({
    description: 'Number of data rows processed (excluding headers)',
    example: 50,
  })
  processedRows: number;

  @ApiProperty({
    description: 'Number of leads successfully created',
    example: 45,
  })
  successfulCreations: number;

  @ApiProperty({
    description: 'Number of leads that failed to create',
    example: 5,
  })
  failedCreations: number;

  @ApiProperty({
    description: 'Array of successfully created leads',
    type: [LeadSummaryResponseDto],
  })
  createdLeads: any[];

  @ApiProperty({
    description: 'Array of parsing and creation errors',
    example: [
      {
        row: 3,
        error: 'Customer name is required',
        data: {}
      }
    ],
  })
  errors: Array<{
    row: number;
    error: string;
    data: any;
  }>;

  @ApiProperty({
    description: 'Detected column mappings from Excel headers',
    example: {
      'Customer Name': 'customerName',
      'Phone Number': 'phoneNumber',
      'Branch': 'branchIdentifier',
      'Anchor': 'anchorName',
    },
  })
  columnMappings: Record<string, string>;

  @ApiProperty({
    description: 'Results of anchor processing during import',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        row: { type: 'number', example: 2, description: 'Excel row number' },
        anchorName: { type: 'string', example: 'ABC Corporation', description: 'Anchor name from Excel' },
        action: { type: 'string', enum: ['matched', 'created', 'skipped'], example: 'matched', description: 'Action taken for the anchor' },
        anchorId: { type: 'string', example: '550e8400-e29b-41d4-a716-************', description: 'ID of the matched or created anchor' },
        similarity: { type: 'number', example: 85.5, description: 'Similarity percentage for matched anchors' },
        matchedAnchorName: { type: 'string', example: 'ABC Corp', description: 'Name of the matched existing anchor' },
      },
    },
  })
  anchorCreationResults?: Array<{
    row: number;
    anchorName: string;
    action: 'matched' | 'created' | 'skipped';
    anchorId?: string;
    similarity?: number;
    matchedAnchorName?: string;
  }>;
}
