import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for lead summary response
 * Returns essential lead information with activity counts and last interaction
 */
export class LeadSummaryResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the lead',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Parent lead ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  parent_lead_id: string | null;

  @ApiPropertyOptional({
    description: 'Anchor relationship ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  anchor_relationship_id: string | null;

  @ApiProperty({
    description: 'Name of the lead (same as customer name)',
    example: 'Cole Palmer',
  })
  lead_name: string;

  @ApiProperty({
    description: 'Status of the lead from database',
    example: 'Hot',
    nullable: true,
  })
  status: string | null;

  @ApiProperty({
    description: 'Primary phone number of the customer',
    example: '+************',
  })
  phoneNumber: string;

  @ApiPropertyOptional({
    description: 'Account number of the lead (assigned when converted to client)',
    example: 'ACC123456789',
    nullable: true,
  })
  account_number: string | null;

  @ApiPropertyOptional({
    description: 'Date and time when the account number was assigned (lead converted to client)',
    example: '2025-08-04T22:15:30.000Z',
    nullable: true,
  })
  account_number_assigned_at: Date | null;

  @ApiProperty({
    description: 'Number of visits recorded for this lead',
    example: 3,
  })
  no_of_visits: number;

  @ApiProperty({
    description: 'Number of calls recorded for this lead',
    example: 5,
  })
  no_of_calls: number;

  @ApiPropertyOptional({
    description: 'Last interaction information with type and date',
    example: {
      interaction_type: 'call',
      date: '2025-07-28T14:30:00.000Z',
    },
    nullable: true,
  })
  last_interaction: {
    interaction_type: string;
    date: string;
  } | null;

  @ApiPropertyOptional({
    description: 'Name of the officer who performed the latest activity',
    example: 'John Doe',
    nullable: true,
  })
  officer: string | null;

  @ApiPropertyOptional({
    description: 'Customer category information',
    nullable: true,
  })
  customer_category: {
    id: string;
    name: string;
  } | null;

  @ApiPropertyOptional({
    description: 'ISIC sector information',
    nullable: true,
  })
  isic_sector: {
    id: string;
    name: string;
  } | null;

  @ApiPropertyOptional({
    description: 'Branch information with region',
    nullable: true,
  })
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  } | null;

  @ApiPropertyOptional({
    description: 'Name of the employer',
    example: 'ABC Corporation',
    nullable: true,
  })
  employerName: string | null;

  @ApiPropertyOptional({
    description: 'Name of the parent lead',
    example: 'Parent Lead Customer',
    nullable: true,
  })
  parent_lead_name: string | null;

  @ApiPropertyOptional({
    description: 'Name of the anchor relationship',
    example: 'Strategic Partnership',
    nullable: true,
  })
  anchor_relationship_name: string | null;

  @ApiPropertyOptional({
    description: 'Anchor information (organization that referred this lead)',
    nullable: true,
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
      name: { type: 'string', example: 'ABC Corporation' },
      email: { type: 'string', example: '<EMAIL>' },
      phone_number: { type: 'string', example: '0712345678' },
    },
  })
  anchor?: {
    id: string;
    name: string;
    email: string;
    phone_number: string;
  } | null;

  @ApiProperty({
    description: 'Date and time when the lead was created',
    example: '2025-08-05T10:30:00.000Z',
  })
  created_at: string;
}
