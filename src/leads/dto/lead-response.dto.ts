import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LeadContactPersonResponseDto } from './lead-contact-person.dto';
/**
 * Data Transfer Object for lead contact person response
 */

/**
 * Data Transfer Object for related entity responses
 */
export class RelatedEntityDto {
  @ApiProperty({
    description: 'Unique identifier',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the entity',
    example: 'Entity Name',
  })
  name: string;
}

/**
 * Data Transfer Object for branch with region response
 */
export class BranchWithRegionDto {
  @ApiProperty({
    description: 'Unique identifier for the branch',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the branch',
    example: 'Central Branch',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Region information',
    type: RelatedEntityDto,
    nullable: true,
  })
  region: RelatedEntityDto | null;
}

/**
 * Data Transfer Object for user response
 */
export class UserResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the user',
    example: 'John Smith',
  })
  name: string;

  @ApiProperty({
    description: 'Email of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'RM code of the user',
    example: 'RM001',
  })
  rm_code: string;
}

/**
 * Data Transfer Object for lead response
 * Defines the structure of lead data returned by the API
 */
export class LeadResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the lead',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the customer',
    example: 'ABC Manufacturing Ltd',
  })
  customer_name: string;

  @ApiPropertyOptional({
    description: 'UUID of the parent lead (for referrals)',
    example: '550e8400-e29b-41d4-a716-************',
    nullable: true,
  })
  parent_lead_id: string | null;

  @ApiProperty({
    description: 'Primary phone number of the customer',
    example: '+************',
  })
  phone_number: string;

  @ApiProperty({
    description: 'Type of lead',
    example: 'Warm',
  })
  type_of_lead: string;

  @ApiPropertyOptional({
    description: 'Account number of the lead (assigned when converted to client)',
    example: 'ACC123456789',
    nullable: true,
  })
  account_number: string | null;

  @ApiPropertyOptional({
    description: 'Date and time when the account number was assigned (lead converted to client)',
    example: '2025-08-04T22:15:30.000Z',
    nullable: true,
  })
  account_number_assigned_at: Date | null;



  @ApiPropertyOptional({
    description: 'Customer category information',
    type: RelatedEntityDto,
    nullable: true,
  })
  customer_category: RelatedEntityDto | null;

  @ApiPropertyOptional({
    description: 'ISIC sector information',
    type: RelatedEntityDto,
    nullable: true,
  })
  isic_sector: RelatedEntityDto | null;

  @ApiPropertyOptional({
    description: 'Branch information with region',
    type: BranchWithRegionDto,
    nullable: true,
  })
  branch: BranchWithRegionDto | null;

  @ApiPropertyOptional({
    description: 'Relationship manager information',
    type: UserResponseDto,
    nullable: true,
  })
  rm_user: UserResponseDto | null;

  @ApiPropertyOptional({
    description: 'Employer information',
    type: RelatedEntityDto,
    nullable: true,
  })
  employer: RelatedEntityDto | null;

  @ApiPropertyOptional({
    description: 'Parent lead information (for referrals)',
    type: RelatedEntityDto,
    nullable: true,
  })
  parent_lead?: RelatedEntityDto | null;

  @ApiProperty({
    description: 'Contact persons for this lead',
    type: [LeadContactPersonResponseDto],
  })
  contact_persons: LeadContactPersonResponseDto[];

  @ApiPropertyOptional({
    description: 'Number of referred leads from this lead',
    example: 3,
  })
  referredLeadsCount?: number;

  @ApiPropertyOptional({
    description: 'Number of general activities for this lead',
    example: 5,
  })
  activitiesCount?: number;
}
