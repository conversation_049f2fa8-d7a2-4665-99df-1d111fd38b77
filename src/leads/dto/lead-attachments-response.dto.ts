import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AttachmentInfoDto {
  @ApiProperty({
    description: 'Unique identifier of the attachment',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'URL to access the attachment file',
    example: 'http://localhost:3000/uploads/activities/activity-uuid/document.pdf',
  })
  file_url: string;

  @ApiProperty({
    description: 'Date and time when the attachment was created',
    example: '2025-08-05T10:30:00.000Z',
  })
  created_at: string;

  @ApiProperty({
    description: 'Date and time when the attachment was last updated',
    example: '2025-08-05T10:30:00.000Z',
  })
  updated_at: string;

  @ApiPropertyOptional({
    description: 'Activity information associated with this attachment',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
      activity_type: { type: 'string', example: 'First Contact' },
      interaction_type: { type: 'string', example: 'call' },
      notes: { type: 'string', example: 'Customer showed interest' },
      created_at: { type: 'string', example: '2025-08-05T10:30:00.000Z' },
    },
  })
  activity?: {
    id: string;
    activity_type: string;
    interaction_type: string;
    notes: string | null;
    created_at: string;
  };


}

export class LeadAttachmentsResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the lead',
    example: '550e8400-e29b-41d4-a716-************',
  })
  lead_id: string;

  @ApiProperty({
    description: 'Name of the lead/customer',
    example: 'John Doe',
  })
  lead_name: string;

  @ApiProperty({
    description: 'Total number of attachments for this lead',
    example: 5,
  })
  total_attachments: number;

  @ApiProperty({
    description: 'Array of attachment information',
    type: [AttachmentInfoDto],
  })
  attachments: AttachmentInfoDto[];

  @ApiProperty({
    description: 'Summary of attachments by activity type',
    type: 'object',
    properties: {
      call_activities: { type: 'number', example: 3 },
      visit_activities: { type: 'number', example: 2 },
    },
  })
  summary: {
    call_activities: number;
    visit_activities: number;
  };

  @ApiProperty({
    description: 'Date range of attachments',
    type: 'object',
    properties: {
      earliest: { type: 'string', example: '2025-08-01T10:30:00.000Z' },
      latest: { type: 'string', example: '2025-08-05T10:30:00.000Z' },
    },
  })
  date_range: {
    earliest: string | null;
    latest: string | null;
  };
}
