import { IsOptional, IsUUID, <PERSON>DateString, ValidateIf } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for RBAC analytics query parameters
 */
export class RbacAnalyticsQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by specific branch ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @ValidateIf((o) => o.branch_id && o.branch_id.trim() !== '')
  @IsUUID(4, { message: 'Branch ID must be a valid UUID' })
  branch_id?: string;

  @ApiPropertyOptional({
    description: 'Filter analytics from this date (ISO format)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  start_date?: string;

  @ApiPropertyOptional({
    description: 'Filter analytics to this date (ISO format)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  end_date?: string;
}
