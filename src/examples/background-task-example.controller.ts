import { <PERSON>, Post, Get, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { BackgroundTaskExampleService } from './background-task-example.service';

@ApiTags('Background Task Examples')
@Controller('examples/background-tasks')
export class BackgroundTaskExampleController {
  constructor(
    private readonly backgroundTaskExampleService: BackgroundTaskExampleService,
  ) {}

  @Post('immediate-console-log')
  @ApiOperation({ summary: 'Trigger an immediate console log task' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Hello from immediate task!' }
      },
      required: ['message']
    }
  })
  @ApiResponse({ status: 201, description: 'Task created and queued for immediate execution' })
  async triggerImmediateConsoleLog(@Body('message') message: string) {
    return await this.backgroundTaskExampleService.triggerImmediateConsoleLog(message);
  }

  @Post('scheduled-console-log')
  @ApiOperation({ summary: 'Schedule a console log task for later' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Hello from scheduled task!' },
        delayMinutes: { type: 'number', example: 5, description: 'Delay in minutes' }
      },
      required: ['message']
    }
  })
  @ApiResponse({ status: 201, description: 'Task scheduled successfully' })
  async scheduleConsoleLogForLater(
    @Body('message') message: string,
    @Body('delayMinutes') delayMinutes?: number
  ) {
    return await this.backgroundTaskExampleService.scheduleConsoleLogForLater(message, delayMinutes);
  }

  @Post('recurring-nairobi-task')
  @ApiOperation({ summary: 'Schedule a recurring daily Nairobi task' })
  @ApiResponse({ status: 201, description: 'Recurring task scheduled successfully' })
  async scheduleRecurringNairobiTask() {
    return await this.backgroundTaskExampleService.scheduleRecurringNairobiTask();
  }

  @Post('interval-task')
  @ApiOperation({ summary: 'Schedule an interval-based recurring task' })
  @ApiResponse({ status: 201, description: 'Interval task scheduled successfully' })
  async scheduleIntervalTask() {
    return await this.backgroundTaskExampleService.scheduleIntervalTask();
  }

  @Post('demonstrate-management')
  @ApiOperation({ summary: 'Demonstrate task creation and cancellation' })
  @ApiResponse({ status: 201, description: 'Task management demonstration completed' })
  async demonstrateTaskManagement() {
    return await this.backgroundTaskExampleService.demonstrateTaskManagement();
  }

  @Get('task-status/:taskId')
  @ApiOperation({ summary: 'Check the status of a specific task' })
  @ApiParam({ name: 'taskId', description: 'The ID of the task to check' })
  @ApiResponse({ status: 200, description: 'Task status retrieved successfully' })
  async checkTaskStatus(@Param('taskId') taskId: string) {
    return await this.backgroundTaskExampleService.checkTaskStatus(taskId);
  }

  @Get('my-tasks')
  @ApiOperation({ summary: 'Get all tasks created by the example service' })
  @ApiQuery({ name: 'limit', required: false, type: 'number', description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, type: 'number', description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved successfully' })
  async getAllMyTasks(
    @Query('limit') limit?: number,
    @Query('offset') offset?: number
  ) {
    return await this.backgroundTaskExampleService.getAllMyTasks();
  }

  @Post('user-action')
  @ApiOperation({ summary: 'Simulate a user action that triggers background tasks' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        userId: { type: 'string', example: 'user-123' },
        action: { type: 'string', example: 'important-action' }
      },
      required: ['userId', 'action']
    }
  })
  @ApiResponse({ status: 201, description: 'User action processed and background tasks triggered' })
  async handleUserAction(
    @Body('userId') userId: string,
    @Body('action') action: string
  ) {
    return await this.backgroundTaskExampleService.handleUserAction(userId, action);
  }
}
