import { Injectable, Logger } from '@nestjs/common';
import { BackgroundTaskService } from '../scheduled-tasks/background-task.service';

/**
 * Example service demonstrating how to use BackgroundTaskService
 * to trigger tasks from anywhere in your code
 */
@Injectable()
export class BackgroundTaskExampleService {
  private readonly logger = new Logger(BackgroundTaskExampleService.name);

  constructor(
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  /**
   * Example: Trigger an immediate console log task
   */
  async triggerImmediateConsoleLog(message: string) {
    this.logger.log('Triggering immediate console log task...');
    
    const task = await this.backgroundTaskService.runNow('console-log', {
      message,
      level: 'info',
      source: 'BackgroundTaskExampleService',
      timestamp: new Date().toISOString(),
    }, {
      name: 'Immediate Console Log',
      description: 'Console log triggered from example service',
      priority: 8,
    });

    this.logger.log(`Console log task created with ID: ${task.id}`);
    return task;
  }

  /**
   * Example: Schedule a console log task for the future
   */
  async scheduleConsoleLogForLater(message: string, delayMinutes: number = 5) {
    this.logger.log(`Scheduling console log task for ${delayMinutes} minutes from now...`);
    
    const runAt = new Date(Date.now() + delayMinutes * 60 * 1000);
    
    const task = await this.backgroundTaskService.schedule('console-log', {
      message,
      level: 'info',
      source: 'BackgroundTaskExampleService',
      scheduledFor: runAt.toISOString(),
    }, runAt, {
      name: 'Scheduled Console Log',
      description: `Console log scheduled for ${delayMinutes} minutes later`,
      priority: 6,
    });

    this.logger.log(`Console log task scheduled with ID: ${task.id} for ${runAt.toISOString()}`);
    return task;
  }

  /**
   * Example: Schedule a recurring daily Nairobi task
   */
  async scheduleRecurringNairobiTask() {
    this.logger.log('Scheduling recurring daily Nairobi task...');
    
    // Run every day at 11:00 PM Nairobi time (8:00 PM UTC)
    const cronExpression = '0 20 * * *'; // 8 PM UTC = 11 PM EAT
    
    const task = await this.backgroundTaskService.scheduleRecurring('daily-nairobi-task', {
      message: 'Daily recurring task from example service',
      timezone: 'Africa/Nairobi',
      taskType: 'recurring-example',
      metadata: {
        createdBy: 'BackgroundTaskExampleService',
        purpose: 'Demonstration of recurring tasks',
      },
    }, cronExpression, {
      name: 'Recurring Daily Nairobi Task',
      description: 'Daily task that runs at 11 PM Nairobi time',
      priority: 7,
    });

    this.logger.log(`Recurring Nairobi task created with ID: ${task.id}`);
    return task;
  }

  /**
   * Example: Schedule a task with interval-based recurrence
   */
  async scheduleIntervalTask() {
    this.logger.log('Scheduling interval-based task...');
    
    const task = await this.backgroundTaskService.scheduleInterval('console-log', {
      message: 'This is an interval-based recurring task',
      level: 'info',
      source: 'BackgroundTaskExampleService',
      intervalType: 'MINUTES',
      intervalValue: 30,
    }, 'MINUTES', 30, {
      name: 'Every 30 Minutes Console Log',
      description: 'Console log that runs every 30 minutes',
      priority: 5,
    });

    this.logger.log(`Interval task created with ID: ${task.id}`);
    return task;
  }

  /**
   * Example: Demonstrate task management
   */
  async demonstrateTaskManagement() {
    this.logger.log('Demonstrating task management...');
    
    // Create a task
    const task = await this.backgroundTaskService.schedule('console-log', {
      message: 'This task will be cancelled',
      level: 'warn',
    }, new Date(Date.now() + 10 * 60 * 1000), { // 10 minutes from now
      name: 'Task to be Cancelled',
    });

    this.logger.log(`Created task ${task.id}, now cancelling it...`);
    
    // Cancel the task
    const cancelledTask = await this.backgroundTaskService.cancelTask(task.id);
    this.logger.log(`Task ${task.id} cancelled. Status: ${cancelledTask.status}`);
    
    return { task, cancelledTask };
  }

  /**
   * Example: Get task status and history
   */
  async checkTaskStatus(taskId: string) {
    this.logger.log(`Checking status of task ${taskId}...`);
    
    const task = await this.backgroundTaskService.getTaskStatus(taskId);
    
    this.logger.log(`Task ${taskId} status:`, {
      id: task.id,
      type: task.type,
      name: task.name,
      status: task.status,
      attempts: task.attempts,
      maxAttempts: task.max_attempts,
      runAt: task.run_at,
      lastRunAt: task.last_run_at,
      nextRunAt: task.next_run_at,
      completedAt: task.completed_at,
      failedAt: task.failed_at,
      errorMessage: task.error_message,
    });
    
    return task;
  }

  /**
   * Example: Get all tasks created by this service
   */
  async getAllMyTasks() {
    this.logger.log('Getting all tasks created by this service...');
    
    const result = await this.backgroundTaskService.getTasks();
    
    this.logger.log(`Found ${result.tasks.length} tasks, pagination:`, result.pagination);
    
    return result;
  }

  /**
   * Example method that could be called from a controller or another service
   * to demonstrate real-world usage
   */
  async handleUserAction(userId: string, action: string) {
    this.logger.log(`User ${userId} performed action: ${action}`);
    
    // Trigger immediate logging
    await this.triggerImmediateConsoleLog(`User ${userId} performed ${action}`);
    
    // Schedule a follow-up task for later
    if (action === 'important-action') {
      await this.scheduleConsoleLogForLater(
        `Follow-up for user ${userId} important action`,
        5 // 5 minutes later
      );
    }
    
    return { success: true, message: 'Background tasks triggered successfully' };
  }
}
