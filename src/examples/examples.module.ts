import { Module } from '@nestjs/common';
import { BackgroundTaskExampleService } from './background-task-example.service';
import { BackgroundTaskExampleController } from './background-task-example.controller';
import { SimpleTaskDemoService } from './simple-task-demo.service';
import { ScheduledTaskModule } from '../scheduled-tasks/scheduled-task.module';

@Module({
  imports: [ScheduledTaskModule],
  controllers: [BackgroundTaskExampleController],
  providers: [BackgroundTaskExampleService, SimpleTaskDemoService],
  exports: [BackgroundTaskExampleService, SimpleTaskDemoService],
})
export class ExamplesModule {}
