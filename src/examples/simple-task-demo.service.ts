import { Injectable, Logger } from '@nestjs/common';
import { BackgroundTaskService } from '../scheduled-tasks/background-task.service';

/**
 * Simple demonstration service showing how easy it is to use background tasks
 * from anywhere in your codebase
 */
@Injectable()
export class SimpleTaskDemoService {
  private readonly logger = new Logger(SimpleTaskDemoService.name);

  constructor(
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  /**
   * Example: User registration workflow
   */
  async handleUserRegistration(userData: { email: string; name: string }) {
    this.logger.log(`Processing registration for ${userData.email}`);

    // 1. Send welcome email immediately
    await this.backgroundTaskService.runNow('console-log', {
      message: `Welcome ${userData.name}! Your account has been created.`,
      level: 'info'
    });

    // 2. Schedule onboarding email for tomorrow at 9 AM
    const tomorrow9AM = new Date();
    tomorrow9AM.setDate(tomorrow9AM.getDate() + 1);
    tomorrow9AM.setHours(9, 0, 0, 0);

    await this.backgroundTaskService.schedule('console-log', {
      message: `Onboarding reminder for ${userData.name}`,
      level: 'info'
    }, tomorrow9AM, {
      name: 'Onboarding Email',
      description: `Onboarding email for ${userData.email}`
    });

    // 3. Set up weekly newsletter (every Friday at 10 AM)
    await this.backgroundTaskService.scheduleRecurring('console-log', {
      message: `Weekly newsletter for ${userData.name}`,
      level: 'info'
    }, '0 10 * * 5', {
      name: 'Weekly Newsletter',
      description: `Weekly newsletter for ${userData.email}`
    });

    return { success: true, message: 'User registration workflow initiated' };
  }

  /**
   * Example: Data processing workflow
   */
  async processDataBatch(batchId: string) {
    this.logger.log(`Starting data processing for batch ${batchId}`);

    // 1. Start processing immediately
    const processTask = await this.backgroundTaskService.runNow('console-log', {
      message: `Processing data batch ${batchId}`,
      level: 'info'
    });

    // 2. Schedule validation task 5 minutes later
    const validationTime = new Date(Date.now() + 5 * 60 * 1000);
    await this.backgroundTaskService.schedule('console-log', {
      message: `Validating processed batch ${batchId}`,
      level: 'info'
    }, validationTime);

    // 3. Schedule cleanup task 1 hour later
    const cleanupTime = new Date(Date.now() + 60 * 60 * 1000);
    await this.backgroundTaskService.schedule('console-log', {
      message: `Cleaning up temporary files for batch ${batchId}`,
      level: 'info'
    }, cleanupTime);

    return { 
      processTaskId: processTask.id,
      message: 'Data processing workflow initiated'
    };
  }

  /**
   * Example: System maintenance tasks
   */
  async setupMaintenanceTasks() {
    this.logger.log('Setting up system maintenance tasks');

    // Daily database cleanup at 2 AM
    await this.backgroundTaskService.scheduleRecurring('console-log', {
      message: 'Running daily database cleanup',
      level: 'info'
    }, '0 2 * * *', {
      name: 'Daily DB Cleanup',
      description: 'Clean up old records and optimize database'
    });

    // Weekly backup every Sunday at 3 AM
    await this.backgroundTaskService.scheduleRecurring('console-log', {
      message: 'Creating weekly system backup',
      level: 'info'
    }, '0 3 * * 0', {
      name: 'Weekly Backup',
      description: 'Create full system backup'
    });

    // Monthly report generation on 1st of each month at 8 AM
    await this.backgroundTaskService.scheduleRecurring('console-log', {
      message: 'Generating monthly system report',
      level: 'info'
    }, '0 8 1 * *', {
      name: 'Monthly Report',
      description: 'Generate comprehensive monthly report'
    });

    // Health check every 15 minutes
    await this.backgroundTaskService.scheduleInterval('console-log', {
      message: 'Running system health check',
      level: 'info'
    }, 'MINUTES', 15, {
      name: 'Health Check',
      description: 'Monitor system health and performance'
    });

    return { success: true, message: 'Maintenance tasks configured' };
  }

  /**
   * Example: Event-driven task creation
   */
  async handleOrderPlaced(orderId: string, customerEmail: string) {
    this.logger.log(`Processing order ${orderId} for ${customerEmail}`);

    // 1. Send order confirmation immediately
    await this.backgroundTaskService.runNow('console-log', {
      message: `Order confirmation sent for order ${orderId}`,
      level: 'info'
    });

    // 2. Schedule shipping notification for tomorrow
    const shippingDate = new Date(Date.now() + 24 * 60 * 60 * 1000);
    await this.backgroundTaskService.schedule('console-log', {
      message: `Shipping notification for order ${orderId}`,
      level: 'info'
    }, shippingDate);

    // 3. Schedule follow-up survey 1 week after delivery
    const surveyDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    await this.backgroundTaskService.schedule('console-log', {
      message: `Customer satisfaction survey for order ${orderId}`,
      level: 'info'
    }, surveyDate);

    return { success: true, message: 'Order processing workflow initiated' };
  }

  /**
   * Example: Task management operations
   */
  async demonstrateTaskManagement() {
    this.logger.log('Demonstrating task management capabilities');

    // Create a task
    const task = await this.backgroundTaskService.schedule('console-log', {
      message: 'This task will be cancelled',
      level: 'warn'
    }, new Date(Date.now() + 10 * 60 * 1000)); // 10 minutes from now

    this.logger.log(`Created task ${task.id}`);

    // Check task status
    const status = await this.backgroundTaskService.getTaskStatus(task.id);
    this.logger.log(`Task status: ${status.status}`);

    // Cancel the task
    const cancelledTask = await this.backgroundTaskService.cancelTask(task.id);
    this.logger.log(`Task cancelled. New status: ${cancelledTask.status}`);

    // Get all tasks
    const allTasksResult = await this.backgroundTaskService.getTasks();
    this.logger.log(`Total tasks in system: ${allTasksResult.tasks.length}`);

    return {
      taskId: task.id,
      finalStatus: cancelledTask.status,
      totalTasks: allTasksResult.tasks.length
    };
  }
}
