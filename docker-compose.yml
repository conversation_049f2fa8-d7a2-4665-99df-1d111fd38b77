# version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: kb-tracker-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-kb_tracker}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - '5432:5432'
    networks:
      - kb-tracker-network
    healthcheck:
      test:
        [
          'CMD-SHELL',
          'pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-kb_tracker}',
        ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for BullMQ
  redis:
    image: redis:7-alpine
    container_name: kb-tracker-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - '6379:6379'
    networks:
      - kb-tracker-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  # Main NestJS API Service
  nest-api:
    image: kb-tracker-backend-prod:latest
    # container_name: kb-tracker-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
      REDIS_URL: redis://redis:6379
      PORT: 3000
      TZ: Africa/Nairobi
    env_file:
      - .env
    ports:
      - '3000:3000'
    volumes:
      - ./uploads:/usr/src/app/uploads
      - ./reports:/usr/src/app/reports
    networks:
      - kb-tracker-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'node', 'dist/src/health-check.js']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # BullMQ Worker Service (uses same image as API)
  nest-worker:
    image: kb-tracker-backend-prod:latest
    # container_name: kb-tracker-worker
    restart: unless-stopped
    command: ['node', 'dist/src/worker.js']
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
      REDIS_URL: redis://redis:6379
      WORKER_CONCURRENCY: ${WORKER_CONCURRENCY:-5}
      WORKER_MAX_STALLED_COUNT: ${WORKER_MAX_STALLED_COUNT:-3}
      WORKER_STALLED_INTERVAL: ${WORKER_STALLED_INTERVAL:-30000}
      TZ: Africa/Nairobi
    env_file:
      - .env
    volumes:
      - ./uploads:/usr/src/app/uploads
      - ./reports:/usr/src/app/reports
    networks:
      - kb-tracker-network
    depends_on:
      nest-api:
        condition: service_started
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Scheduler Service (uses same image as API)
  nest-scheduler:
    image: kb-tracker-backend-prod:latest
    container_name: kb-tracker-scheduler
    restart: unless-stopped
    command: ['node', 'dist/src/scheduler.js']
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
      REDIS_URL: redis://redis:6379
      SCHEDULER_ENABLED: ${SCHEDULER_ENABLED:-true}
      SCHEDULER_TIMEZONE: ${SCHEDULER_TIMEZONE:-Africa/Nairobi}
      TZ: Africa/Nairobi
    env_file:
      - .env
    volumes:
      - ./uploads:/usr/src/app/uploads
      - ./reports:/usr/src/app/reports
    networks:
      - kb-tracker-network
    depends_on:
      nest-api:
        condition: service_started
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Redis Commander (Optional - for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: kb-tracker-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: ${REDIS_COMMANDER_USER:-admin}
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-admin}
    ports:
      - '8081:8081'
    networks:
      - kb-tracker-network
    depends_on:
      - redis
    profiles:
      - tools

  # pgAdmin (Optional - for PostgreSQL management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: kb-tracker-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - '8080:80'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - kb-tracker-network
    depends_on:
      - postgres
    profiles:
      - tools

# Networks
networks:
  kb-tracker-network:
    driver: bridge
    name: kb-tracker-network

# Volumes
volumes:
  postgres_data:
    driver: local
    name: kb-tracker-postgres-data
  redis_data:
    driver: local
    name: kb-tracker-redis-data
  pgadmin_data:
    driver: local
    name: kb-tracker-pgadmin-data
