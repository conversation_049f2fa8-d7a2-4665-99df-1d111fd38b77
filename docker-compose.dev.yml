# version: '3.8'

# Development override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # Development API with hot reload
  nest-api:
    image: kb-tracker-backend-dev:latest
    command: npm run start:dev
    environment:
      NODE_ENV: development
      LOG_LEVEL: debug
      TZ: Africa/Nairobi
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
      - ./uploads:/usr/src/app/uploads
    ports:
      - '3000:3000'
      - '9229:9229' # Debug port

  # Development Worker (uses same image as API)
  nest-worker:
    image: kb-tracker-backend-dev:latest
    command: npm run start:worker:dev
    depends_on:
      nest-api:
        condition: service_started
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    environment:
      NODE_ENV: development
      LOG_LEVEL: debug
      TZ: Africa/Nairobi
    deploy:
      replicas: 1

  # Development Scheduler (uses same image as API)
  nest-scheduler:
    image: kb-tracker-backend-dev:latest
    command: npm run start:scheduler:dev
    depends_on:
      nest-api:
        condition: service_started
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    environment:
      NODE_ENV: development
      LOG_LEVEL: debug
      TZ: Africa/Nairobi
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Enable tools by default in development
  redis-commander:
    profiles: []

  pgadmin:
    profiles: []
