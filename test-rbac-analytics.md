# Testing RBAC Analytics Endpoint

## Issue Analysis

The 400 error you're seeing is because the endpoint requires proper JWT authentication, but the request in the log shows no Authorization header:

```
"headers":{"host":"localhost:3000","user-agent":"Mozilla/5.0..."}
```

The endpoint is protected by:
1. `@UseGuards(JwtAuthGuard, PermissionsGuard)`
2. `@RequirePermissions('view.all.leads', 'view.my.leads')`

## Required Authentication

### Step 1: Login to get JWT token

```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

This will return:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "...",
  "user": { ... }
}
```

### Step 2: Use the access_token for analytics endpoint

```bash
curl -X GET "http://localhost:3000/api/v1/leads/rbac-analytics" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

## Correct Request Examples

### Basic Request
```bash
curl -X GET "http://localhost:3000/api/v1/leads/rbac-analytics" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"
```

### With Filters
```bash
curl -X GET "http://localhost:3000/api/v1/leads/rbac-analytics?branch_id=550e8400-e29b-41d4-a716-446655440000&start_date=2024-01-01T00:00:00.000Z&end_date=2024-12-31T23:59:59.999Z" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

### JavaScript/Fetch Example
```javascript
// First login
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'your-password'
  })
});

const { access_token } = await loginResponse.json();

// Then call analytics
const analyticsResponse = await fetch('/api/v1/leads/rbac-analytics', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  }
});

const analytics = await analyticsResponse.json();
console.log('Analytics with MTD data:', analytics);
```

## Expected Response Structure

```json
{
  "total_leads": 150,
  "contacted_leads": 85,
  "leads_by_status": {
    "pending": 45,
    "warm": 30,
    "hot": 25,
    "cold": 50
  },
  "user_activity_mtd": {
    "total_visits": 12,
    "total_calls": 25,
    "month_year": "2025-08"
  },
  "user_permissions": {
    "can_view_all_leads": true,
    "applied_filter": "all_leads"
  },
  "filters_applied": {
    "branch_id": null,
    "start_date": null,
    "end_date": null
  }
}
```

## Common Error Scenarios

### 1. Missing Authorization Header (400/401)
**Problem**: No `Authorization: Bearer <token>` header
**Solution**: Include JWT token in Authorization header

### 2. Invalid/Expired Token (401)
**Problem**: Token is expired or malformed
**Solution**: Login again to get a fresh token

### 3. Insufficient Permissions (403)
**Problem**: User doesn't have `view.all.leads` or `view.my.leads` permission
**Solution**: Ensure user has proper role with required permissions

### 4. Invalid Query Parameters (400)
**Problem**: Invalid date format or UUID format for branch_id
**Solution**: Use proper ISO date format and valid UUIDs

## Permission Requirements

The user must have at least one of these permissions:
- `view.all.leads` - Can see analytics for all leads
- `view.my.leads` - Can see analytics for only their assigned leads

## MTD Data Explanation

The new `user_activity_mtd` object contains:
- `total_visits`: Count of activities with `interaction_type: 'visit'` made by the logged-in user in the current month
- `total_calls`: Count of activities with `interaction_type: 'call'` made by the logged-in user in the current month
- `month_year`: Current month in YYYY-MM format for reference

## Troubleshooting

1. **Check if server is running**: `curl http://localhost:3000/health` (if health endpoint exists)
2. **Verify login works**: Test login endpoint first
3. **Check token validity**: Decode JWT token to verify it's not expired
4. **Verify user permissions**: Check user's role and permissions in database
5. **Check database connection**: Ensure PostgreSQL is running and accessible

## Browser Testing

If testing in browser, you need to:
1. Login through the login endpoint or UI
2. Store the access_token (in localStorage, sessionStorage, or cookie)
3. Include it in the Authorization header for subsequent requests

**Note**: The query parameters `user_id`, `id`, and `userId` in your original request are not needed - the user information is extracted from the JWT token automatically.
