import { PrismaClient, MfaType } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedMfaMethods() {
  const methods = [
    {
      method: MfaType.EMAIL,
      description: 'Secure verification via email',
      active: true,
    },
    {
      method: MfaType.SMS,
      description: 'Secure verification via text message',
      active: true,
    },
  ];

  for (const method of methods) {
    await prisma.mfaMethod.upsert({
      where: {
        id: `default-${method.method.toLowerCase()}`, // Consistent ID for upsert
      },
      update: method,
      create: {
        ...method,
        id: `default-${method.method.toLowerCase()}`,
      },
    });
  }

  console.log('✅ MFA methods seeded');
}
