import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Anchor Relationships Seeder
 * 
 * Seeds the anchor_relationships table with predefined relationship types
 * that define how leads are connected to anchor organizations.
 */

const anchorRelationships = [
  {
    name: 'Director',
  },
  {
    name: 'Supplier',
  },
  {
    name: 'Employee',
  },
];

export async function seedAnchorRelationships() {
  console.log('🌱 Seeding anchor relationships...');

  try {
    // Check if anchor relationships already exist
    const existingCount = await prisma.anchorRelationship.count();
    
    if (existingCount > 0) {
      console.log(`ℹ️  Found ${existingCount} existing anchor relationships. Checking for missing ones...`);
      
      // Check which relationships are missing and add only those
      const existingRelationships = await prisma.anchorRelationship.findMany({
        select: { name: true }
      });
      
      const existingNames = existingRelationships.map(rel => rel.name);
      const missingRelationships = anchorRelationships.filter(
        rel => !existingNames.includes(rel.name)
      );
      
      if (missingRelationships.length === 0) {
        console.log('✅ All anchor relationships already exist. Skipping seeding.');
        return;
      }
      
      console.log(`📝 Adding ${missingRelationships.length} missing anchor relationships...`);
      
      // Add missing relationships
      for (const relationship of missingRelationships) {
        await prisma.anchorRelationship.create({
          data: relationship,
        });
        console.log(`   ✅ Created: ${relationship.name}`);
      }
      
      console.log(`✅ Successfully added ${missingRelationships.length} missing anchor relationships.`);
    } else {
      console.log('📝 No existing anchor relationships found. Creating all...');
      
      // Create all anchor relationships
      for (const relationship of anchorRelationships) {
        await prisma.anchorRelationship.create({
          data: relationship,
        });
        console.log(`   ✅ Created: ${relationship.name}`);
      }
      
      console.log(`✅ Successfully created ${anchorRelationships.length} anchor relationships.`);
    }

    // Display final summary
    const finalCount = await prisma.anchorRelationship.count();
    console.log(`📊 Total anchor relationships in database: ${finalCount}`);
    
    // List all relationships
    const allRelationships = await prisma.anchorRelationship.findMany({
      orderBy: { name: 'asc' }
    });
    
    console.log('📋 Available anchor relationships:');
    allRelationships.forEach((rel, index) => {
      console.log(`   ${index + 1}. ${rel.name} (ID: ${rel.id})`);
    });

  } catch (error) {
    console.error('❌ Error seeding anchor relationships:', error);
    throw error;
  }
}

/**
 * Standalone execution
 * Run this file directly to seed anchor relationships
 */
if (require.main === module) {
  seedAnchorRelationships()
    .then(() => {
      console.log('🎉 Anchor relationships seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Anchor relationships seeding failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
