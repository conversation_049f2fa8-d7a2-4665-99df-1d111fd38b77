/*
  Warnings:

  - You are about to drop the column `client_id` on the `general_activities` table. All the data in the column will be lost.
  - You are about to drop the column `client_id` on the `hitlist_entries` table. All the data in the column will be lost.
  - You are about to drop the column `client_id` on the `leads` table. All the data in the column will be lost.
  - You are about to drop the column `parent_lead_id` on the `leads` table. All the data in the column will be lost.
  - You are about to drop the column `client_id` on the `loan_activities` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[rm_code]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `updated_at` to the `leads` table without a default value. This is not possible if the table is not empty.
  - Added the required column `purpose_category_id` to the `purpose_of_activities` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "general_activities" DROP CONSTRAINT "general_activities_client_id_fkey";

-- DropForeignKey
ALTER TABLE "hitlist_entries" DROP CONSTRAINT "hitlist_entries_client_id_fkey";

-- DropForeignKey
ALTER TABLE "leads" DROP CONSTRAINT "leads_parent_lead_id_fkey";

-- DropForeignKey
ALTER TABLE "loan_activities" DROP CONSTRAINT "loan_activities_client_id_fkey";

-- DropIndex
DROP INDEX "leads_client_id_key";

-- AlterTable
ALTER TABLE "branches" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "general_activities" DROP COLUMN "client_id";

-- AlterTable
ALTER TABLE "hitlist_entries" DROP COLUMN "client_id";

-- AlterTable
ALTER TABLE "leads" DROP COLUMN "client_id",
DROP COLUMN "parent_lead_id",
ADD COLUMN     "account_number_assigned_at" TIMESTAMP(3),
ADD COLUMN     "anchor_id" TEXT,
ADD COLUMN     "assigned_user" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "loan_activities" DROP COLUMN "client_id";

-- AlterTable
ALTER TABLE "purpose_of_activities" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "purpose_category_id" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "anchors" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "account_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "anchors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "purpose_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "purpose_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "exempted_target_users" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "target_id" TEXT NOT NULL,
    "exempted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "exempted_target_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "anchors_account_id_key" ON "anchors"("account_id");

-- CreateIndex
CREATE UNIQUE INDEX "exempted_target_users_user_id_target_id_key" ON "exempted_target_users"("user_id", "target_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_rm_code_key" ON "users"("rm_code");

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_anchor_id_fkey" FOREIGN KEY ("anchor_id") REFERENCES "anchors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_assigned_user_fkey" FOREIGN KEY ("assigned_user") REFERENCES "users"("rm_code") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "purpose_of_activities" ADD CONSTRAINT "purpose_of_activities_purpose_category_id_fkey" FOREIGN KEY ("purpose_category_id") REFERENCES "purpose_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "exempted_target_users" ADD CONSTRAINT "exempted_target_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "exempted_target_users" ADD CONSTRAINT "exempted_target_users_target_id_fkey" FOREIGN KEY ("target_id") REFERENCES "targets"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
