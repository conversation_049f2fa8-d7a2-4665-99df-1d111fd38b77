-- Create<PERSON>num
CREATE TYPE "ScheduledTaskStatus" AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'PAUSED');

-- CreateEnum
CREATE TYPE "ScheduledTaskInterval" AS ENUM ('MINUTES', 'HOURS', 'DAYS', 'WEEKS', 'MONTHS');

-- <PERSON><PERSON>Enum
CREATE TYPE "TaskExecutionStatus" AS ENUM ('RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateTable
CREATE TABLE "scheduled_tasks" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,
    "payload" JSONB NOT NULL,
    "run_at" TIMESTAMP(3) NOT NULL,
    "interval_type" "ScheduledTaskInterval",
    "interval_value" INTEGER,
    "cron_expression" TEXT,
    "max_attempts" INTEGER NOT NULL DEFAULT 3,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "status" "ScheduledTaskStatus" NOT NULL DEFAULT 'PENDING',
    "priority" INTEGER NOT NULL DEFAULT 0,
    "queue_name" TEXT NOT NULL DEFAULT 'default',
    "last_run_at" TIMESTAMP(3),
    "next_run_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "failed_at" TIMESTAMP(3),
    "error_message" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "scheduled_tasks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduled_task_executions" (
    "id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "started_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),
    "status" "TaskExecutionStatus" NOT NULL DEFAULT 'RUNNING',
    "result" JSONB,
    "error_message" TEXT,
    "duration_ms" INTEGER,
    "worker_id" TEXT,
    "attempt_number" INTEGER NOT NULL DEFAULT 1,

    CONSTRAINT "scheduled_task_executions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "scheduled_tasks_status_run_at_idx" ON "scheduled_tasks"("status", "run_at");

-- CreateIndex
CREATE INDEX "scheduled_tasks_next_run_at_idx" ON "scheduled_tasks"("next_run_at");

-- CreateIndex
CREATE INDEX "scheduled_tasks_type_status_idx" ON "scheduled_tasks"("type", "status");

-- CreateIndex
CREATE INDEX "scheduled_tasks_queue_name_status_idx" ON "scheduled_tasks"("queue_name", "status");

-- CreateIndex
CREATE INDEX "scheduled_task_executions_task_id_started_at_idx" ON "scheduled_task_executions"("task_id", "started_at");

-- CreateIndex
CREATE INDEX "scheduled_task_executions_status_started_at_idx" ON "scheduled_task_executions"("status", "started_at");

-- AddForeignKey
ALTER TABLE "scheduled_tasks" ADD CONSTRAINT "scheduled_tasks_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_task_executions" ADD CONSTRAINT "scheduled_task_executions_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "scheduled_tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;
