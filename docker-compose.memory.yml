services:
  nest-scheduler:
    environment:
      - NODE_OPTIONS=--max-old-space-size=2048 --expose-gc
    deploy:
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 1G

  nest-worker:
    environment:
      - NODE_OPTIONS=--max-old-space-size=2048 --expose-gc
    deploy:
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 1G

  nest-api:
    environment:
      - NODE_OPTIONS=--max-old-space-size=2048 --expose-gc
    deploy:
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 1G

  # Redis is already optimized in the base docker-compose.yml
