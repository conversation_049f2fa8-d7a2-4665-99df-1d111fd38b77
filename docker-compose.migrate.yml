# version: '3.8'

# Migration override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.migrate.yml run --rm migrate

services:
  # Migration service
  migrate:
    build:
      context: .
      dockerfile: Dockerfile
      target: dependencies
    container_name: kb-tracker-migrate
    command: npm run db:migrate:deploy
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
    env_file:
      - .env
    networks:
      - kb-tracker-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - migration

  # Seed service
  seed:
    build:
      context: .
      dockerfile: Dockerfile
      target: dependencies
    container_name: kb-tracker-seed
    command: npm run db:seed
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
    env_file:
      - .env
    networks:
      - kb-tracker-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - migration

  # Database reset service (development only)
  db-reset:
    build:
      context: .
      dockerfile: Dockerfile
      target: dependencies
    container_name: kb-tracker-db-reset
    command: npm run db:reset
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
    env_file:
      - .env
    networks:
      - kb-tracker-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - migration

  # Prisma Studio service
  studio:
    build:
      context: .
      dockerfile: Dockerfile
      target: dependencies
    container_name: kb-tracker-studio
    command: npx prisma studio --port 5555 --hostname 0.0.0.0
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-kb_tracker}
    env_file:
      - .env
    ports:
      - '5555:5555'
    networks:
      - kb-tracker-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - tools
