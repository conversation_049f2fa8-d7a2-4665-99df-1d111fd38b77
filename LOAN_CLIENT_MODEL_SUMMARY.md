# LoanClient Model Summary

## 📋 **LoanClient Model Structure**

The `LoanClient` model has been created with the same structure as the `Lead` model but with activities linked to `LoanActivity` instead of `Activity`.

### **Core LoanClient Fields:**
- `id` - Primary key (UUID)
- `customer_name` - Customer's name
- `phone_number` - Contact number
- `type_of_lead` - Lead classification
- `lead_status` - Current status of the loan client
- `account_number` - Unique account identifier (when assigned)
- `account_number_assigned_at` - Timestamp of account assignment
- `created_at` / `updated_at` - Audit timestamps

### **Foreign Key Relationships:**
- `anchor_id` → Links to **Anchor** (company/organization)
- `customer_category_id` → Links to **CustomerCategory**
- `isic_sector_id` → Links to **ISICSector** (industry classification)
- `branch_id` → Links to **Branch** (bank branch)
- `rm_user_id` → Links to **User** (Relationship Manager)
- `assigned_user` → String field for user assignment
- `employer_id` → Links to **Employer**
- `anchor_relationship_id` → Links to **AnchorRelationship**

## 🔗 **Key Relationships**

### **1. User Relationships:**
- **RM User** (`rm_user_id` → User): The assigned Relationship Manager
- **Assigned User** (`assigned_user`): Additional user assignment (string field)
- **Reverse relations**: Users can have many loan clients through different relationships

### **2. Anchor Ecosystem:**
- **Anchor** (`anchor_id`): The company/organization associated with the loan client
- **AnchorRelationship** (`anchor_relationship_id`): Defines the relationship type with the anchor

### **3. Categorization:**
- **CustomerCategory**: Classifies the type of customer
- **ISICSector**: Industry sector classification
- **Branch**: Bank branch handling the loan client
- **Employer**: Customer's employer information

### **4. Activity Tracking:**
- **LoanActivities[]**: All loan-related activities for this client (linked to LoanActivity model)

### **5. Contact Management:**
- **LoanClientContactPersons[]**: Multiple contact persons for the loan client

## 🎯 **Key Differences from Lead Model**

### **Activities Field:**
- **Lead Model**: `activities: Activity[]` - Links to general activities (calls/visits)
- **LoanClient Model**: `loan_activities: LoanActivity[]` - Links to loan-specific activities

### **Contact Persons:**
- **Lead Model**: `contact_persons: LeadContactPerson[]`
- **LoanClient Model**: `contact_persons: LoanClientContactPerson[]`

### **User Relations:**
- **Lead Model**: Uses `"LeadAssignedUser"` relation name
- **LoanClient Model**: Uses `"LoanClientAssignedUser"` and `"LoanClientRmUser"` relation names

## 📊 **Database Schema Changes Applied**

### **New Tables Created:**
1. **`loan_clients`** - Main LoanClient table
2. **`loan_client_contact_persons`** - Contact persons for loan clients

### **Updated Tables:**
1. **`loan_activities`** - Added `loan_client_id` field and relationship
2. **`users`** - Added reverse relationships for loan clients
3. **`anchors`** - Added reverse relationship for loan clients
4. **`anchor_relationships`** - Added reverse relationship for loan clients
5. **`branches`** - Added reverse relationship for loan clients
6. **`customer_categories`** - Added reverse relationship for loan clients
7. **`isic_sectors`** - Added reverse relationship for loan clients
8. **`employers`** - Added reverse relationship for loan clients

## 🔄 **Relationship Mapping**

```
LoanClient
├── User (RM) - Relationship Manager assignment
├── User (Assigned) - Additional user assignment
├── Anchor - Company/organization association  
├── AnchorRelationship - Type of relationship with anchor
├── Branch - Bank branch handling the loan client
├── CustomerCategory - Customer classification
├── ISICSector - Industry sector
├── Employer - Customer's employer
├── LoanActivities[] - All loan-related activities
└── LoanClientContactPersons[] - Contact information
```

## 🎯 **Business Use Cases**

### **Loan Management:**
- Track existing loan clients separately from leads
- Manage loan-specific activities and interactions
- Maintain relationship with original anchor/company
- Track loan performance and client interactions

### **Activity Tracking:**
- **LoanActivity**: Loan-specific activities (payments, reviews, collections)
- **Activity**: General lead activities (calls, visits, follow-ups)
- Separate tracking allows for different workflows and reporting

### **Reporting & Analytics:**
- Separate loan client metrics from lead metrics
- Loan portfolio analysis
- Client relationship management for existing loans
- Performance tracking for loan officers

## ✅ **Implementation Status**

- ✅ **Database Schema**: Created and updated successfully
- ✅ **Prisma Models**: All relationships configured
- ✅ **Build Status**: No compilation errors
- ⏳ **Database Sync**: Schema ready for push (pending database connection)

## 🔄 **Recent Updates to LoanActivity Model**

### **Field Renames and Enhancements:**
1. **`activity_date` → `created_at`**: Now uses standard timestamp with @default(now())
2. **`purpose` → `purpose_id`**: Now references PurposeOfActivity model
3. **`attachment` → `attachments`**: Now references ActivityAttachment model (one-to-many)

### **Updated LoanActivity Model:**
```prisma
model LoanActivity {
  id                  String               @id @default(uuid())
  loan_client_id      String?
  loan_account_number String?
  purpose_id          String?              // ✅ Changed from purpose (String)
  loan_balance        Decimal?
  arrears_days        Int?
  comment             String?
  rm_user_id          String
  created_at          DateTime             @default(now())  // ✅ Changed from activity_date
  via_api             Boolean?
  api_call_reference  String?

  rm_user             User                 @relation(fields: [rm_user_id], references: [id])
  loan_client         LoanClient?          @relation(fields: [loan_client_id], references: [id])
  purpose             PurposeOfActivity?   @relation(fields: [purpose_id], references: [id])  // ✅ Added
  attachments         ActivityAttachment[] // ✅ Changed from attachment (String)
}
```

### **Related Model Updates:**

#### **PurposeOfActivity Model:**
```prisma
model PurposeOfActivity {
  // ... existing fields
  general_activities    GeneralActivity[]
  activities            Activity[]
  loan_activities       LoanActivity[]     // ✅ Added reverse relationship
}
```

#### **ActivityAttachment Model:**
```prisma
model ActivityAttachment {
  id                  String           @id @default(uuid())
  general_activity_id String?
  activity_id         String?
  loan_activity_id    String?          // ✅ Added for LoanActivity relationship
  file_url            String?
  created_at          DateTime         @default(now())
  updated_at          DateTime         @updatedAt
  general_activity    GeneralActivity? @relation(fields: [general_activity_id], references: [id])
  activity            Activity?        @relation(fields: [activity_id], references: [id])
  loan_activity       LoanActivity?    @relation(fields: [loan_activity_id], references: [id])  // ✅ Added
}
```

## 🚀 **Next Steps**

To fully implement the LoanClient functionality, you would need to:

1. **Create LoanClient Service** - Similar to LeadsService
2. **Create LoanClient Controller** - API endpoints for CRUD operations
3. **Create LoanClient DTOs** - Request/response data transfer objects
4. **Create LoanActivity Service** - Manage loan-specific activities
5. **Frontend Integration** - UI components for loan client management

The database foundation is now ready to support a complete loan client management system parallel to the existing leads system.
