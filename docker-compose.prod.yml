# version: '3.8'

# Production override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

services:
  # Production API with optimizations
  nest-api:
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
    healthcheck:
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

  # Production Worker with scaling
  nest-worker:
    deploy:
      replicas: 4
      resources:
        limits:
          memory: 768M
          cpus: '0.8'
        reservations:
          memory: 384M
          cpus: '0.4'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Production Scheduler
  nest-scheduler:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s

  # Production PostgreSQL with performance tuning
  postgres:
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Production Redis with persistence
  redis:
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    deploy:
      resources:
        limits:
          memory: 1.2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Disable tools in production
  redis-commander:
    profiles:
      - disabled

  pgadmin:
    profiles:
      - disabled
