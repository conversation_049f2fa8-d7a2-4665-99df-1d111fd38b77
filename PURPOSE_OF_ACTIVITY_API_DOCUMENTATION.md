# Purpose of Activity CRUD API Documentation

## Overview

The Purpose of Activity API provides comprehensive CRUD operations for managing activity purposes in the KB Tracker system. This API follows REST principles, implements optimized database queries, and includes extensive validation and error handling.

## Database Model Analysis

### PurposeOfActivity Schema
```prisma
model PurposeOfActivity {
  id                 String            @id @default(uuid())
  name               String?
  description        String?
  general_activities GeneralActivity[]
  activities         Activity[]

  @@map("purpose_of_activities")
}
```

### Key Relationships
- **One-to-Many with GeneralActivity**: A purpose can be used by multiple general activities
- **One-to-Many with Activity**: A purpose can be used by multiple specific activities
- **Cascade Protection**: Prevents deletion when purpose is in use

## API Endpoints

### Base URL
```
/api/v1/purpose-of-activities
```

### Available Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/purpose-of-activities` | Create new purpose |
| GET | `/purpose-of-activities` | Get all purposes with optional search |
| GET | `/purpose-of-activities/statistics` | Get usage statistics |
| GET | `/purpose-of-activities/:id` | Get specific purpose by ID |
| PATCH | `/purpose-of-activities/:id` | Update existing purpose |
| DELETE | `/purpose-of-activities/:id` | Delete purpose (if not in use) |

## Detailed API Reference

### 1. Create Purpose of Activity

**POST** `/purpose-of-activities`

Creates a new purpose with validation and duplicate prevention.

**Request Body:**
```json
{
  "name": "Product Demo",
  "description": "Demonstrate product features and capabilities"
}
```

**Validation Rules:**
- `name`: Required, 2-100 characters, must be unique (case-insensitive)
- `description`: Optional, max 500 characters

**Response (201 Created):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Product Demo",
  "description": "Demonstrate product features and capabilities",
  "general_activities_count": 0,
  "activities_count": 0,
  "total_activities_count": 0,
  "is_in_use": false
}
```

**Error Responses:**
- `400 Bad Request`: Invalid input data
- `409 Conflict`: Name already exists

### 2. Get All Purposes

**GET** `/purpose-of-activities?search=demo`

Retrieves all purposes with optional search filtering.

**Query Parameters:**
- `search` (optional): Search term for name/description filtering

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Product Demo",
      "description": "Demonstrate product features",
      "general_activities_count": 5,
      "activities_count": 10,
      "total_activities_count": 15,
      "is_in_use": true
    }
  ],
  "total": 25,
  "count": 1,
  "message": "Retrieved 1 purposes of activity successfully matching \"demo\""
}
```

### 3. Get Usage Statistics

**GET** `/purpose-of-activities/statistics`

Provides comprehensive analytics about purpose usage.

**Response (200 OK):**
```json
{
  "total_purposes": 25,
  "used_purposes": 18,
  "unused_purposes": 7,
  "total_general_activities": 150,
  "total_activities": 300,
  "most_used_purposes": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Follow Up",
      "general_activities_count": 45,
      "activities_count": 67,
      "total_usage": 112
    }
  ],
  "unused_purpose_list": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Unused Purpose",
      "description": "This purpose is not being used"
    }
  ]
}
```

### 4. Get Purpose by ID

**GET** `/purpose-of-activities/:id`

Retrieves a specific purpose with full details.

**Parameters:**
- `id`: UUID of the purpose

**Response (200 OK):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Product Demo",
  "description": "Demonstrate product features",
  "general_activities_count": 5,
  "activities_count": 10,
  "total_activities_count": 15,
  "is_in_use": true
}
```

**Error Responses:**
- `400 Bad Request`: Invalid UUID format
- `404 Not Found`: Purpose not found

### 5. Update Purpose

**PATCH** `/purpose-of-activities/:id`

Updates an existing purpose with partial data support.

**Request Body (partial update):**
```json
{
  "name": "Updated Product Demo",
  "description": "Updated description"
}
```

**Response (200 OK):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Updated Product Demo",
  "description": "Updated description",
  "general_activities_count": 5,
  "activities_count": 10,
  "total_activities_count": 15,
  "is_in_use": true
}
```

**Error Responses:**
- `400 Bad Request`: Invalid input or UUID
- `404 Not Found`: Purpose not found
- `409 Conflict`: Name conflicts with existing purpose

### 6. Delete Purpose

**DELETE** `/purpose-of-activities/:id`

Deletes a purpose if it's not currently in use.

**Response (200 OK):**
```json
{
  "message": "Purpose of activity \"Product Demo\" deleted successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid UUID format
- `404 Not Found`: Purpose not found
- `409 Conflict`: Purpose is in use by activities

## Key Features

### 🔍 **Optimized Database Queries**

1. **Selective Field Inclusion**: Only fetches required fields for performance
2. **Parallel Queries**: Uses `Promise.all()` for concurrent operations
3. **Relationship Counting**: Efficient `_count` aggregations
4. **Case-Insensitive Search**: Proper text search across fields
5. **Indexed Lookups**: Optimized UUID and name-based queries

### 🛡️ **Data Integrity Protection**

1. **Duplicate Prevention**: Case-insensitive name uniqueness
2. **Cascade Protection**: Prevents deletion of purposes in use
3. **Input Sanitization**: Trims whitespace and validates data
4. **Relationship Validation**: Ensures data consistency

### 📊 **Usage Analytics**

1. **Activity Counting**: Tracks usage across both activity types
2. **Usage Statistics**: Comprehensive analytics endpoint
3. **Most Used Tracking**: Identifies popular purposes
4. **Unused Detection**: Finds purposes not in use

### ✅ **Comprehensive Validation**

1. **Input Validation**: Class-validator decorators
2. **UUID Validation**: Proper UUID format checking
3. **Length Constraints**: Appropriate field length limits
4. **Required Field Enforcement**: Ensures data completeness

### 🔧 **Error Handling**

1. **Specific Error Types**: Meaningful HTTP status codes
2. **Detailed Messages**: Clear error descriptions
3. **Graceful Degradation**: Handles database errors properly
4. **Validation Feedback**: Specific validation error messages

## Implementation Highlights

### Service Layer Best Practices

```typescript
// Optimized query with selective fields
const existingPurpose = await this.prisma.purposeOfActivity.findFirst({
  where: {
    name: {
      equals: createDto.name,
      mode: 'insensitive', // Case-insensitive comparison
    },
  },
  select: { id: true, name: true }, // Only select needed fields
});

// Parallel queries for better performance
const [purposes, total] = await Promise.all([
  this.prisma.purposeOfActivity.findMany({
    where: whereClause,
    include: {
      _count: {
        select: {
          general_activities: true,
          activities: true,
        },
      },
    },
    orderBy: { name: 'asc' },
  }),
  this.prisma.purposeOfActivity.count({ where: whereClause }),
]);
```

### Controller Layer Features

```typescript
// Comprehensive API documentation
@ApiOperation({
  summary: 'Create a new purpose of activity',
  description: 'Creates a new purpose with validation and duplicate prevention',
})
@ApiCreatedResponse({
  description: 'Purpose created successfully',
  type: PurposeOfActivityResponseDto,
})
@ApiBadRequestResponse({ description: 'Invalid input data' })
@ApiConflictResponse({ description: 'Name already exists' })

// Proper validation and parsing
async create(
  @Body(ValidationPipe) createDto: CreatePurposeOfActivityDto,
): Promise<PurposeOfActivityResponseDto> {
  return this.service.create(createDto);
}
```

## Testing Results

### ✅ **Comprehensive Test Coverage**

The API has been thoroughly tested with the following scenarios:

1. **Create Operations**:
   - ✅ Successful creation with valid data
   - ✅ Duplicate name prevention (409 Conflict)
   - ✅ Input validation errors (400 Bad Request)

2. **Read Operations**:
   - ✅ Get all purposes with metadata
   - ✅ Search functionality with filtering
   - ✅ Get by ID with full details
   - ✅ Usage statistics endpoint

3. **Update Operations**:
   - ✅ Full updates with validation
   - ✅ Partial updates (only provided fields)
   - ✅ Name conflict prevention
   - ✅ Not found handling (404)

4. **Delete Operations**:
   - ✅ Successful deletion when not in use
   - ✅ Cascade protection (409 Conflict)
   - ✅ Not found handling (404)

5. **Error Handling**:
   - ✅ Invalid UUID format (400 Bad Request)
   - ✅ Non-existent resources (404 Not Found)
   - ✅ Validation errors with clear messages
   - ✅ Database error handling

## Usage Examples

### Frontend Integration

```javascript
// Create a new purpose
const createPurpose = async (purposeData) => {
  try {
    const response = await fetch('/api/v1/purpose-of-activities', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(purposeData),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to create purpose:', error.message);
    throw error;
  }
};

// Get all purposes with search
const getPurposes = async (searchTerm = '') => {
  const url = searchTerm 
    ? `/api/v1/purpose-of-activities?search=${encodeURIComponent(searchTerm)}`
    : '/api/v1/purpose-of-activities';
    
  const response = await fetch(url);
  return await response.json();
};

// Update purpose
const updatePurpose = async (id, updateData) => {
  const response = await fetch(`/api/v1/purpose-of-activities/${id}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updateData),
  });
  
  return await response.json();
};
```

## Performance Considerations

### Database Optimization
- **Indexed Fields**: UUID primary key and name field indexing
- **Selective Queries**: Only fetch required fields
- **Aggregation Efficiency**: Use `_count` for relationship counting
- **Query Batching**: Parallel execution where possible

### Caching Strategy
- **Response Caching**: Consider caching frequently accessed purposes
- **Statistics Caching**: Cache usage statistics for better performance
- **Invalidation**: Clear cache on create/update/delete operations

## Security Considerations

### Input Validation
- **SQL Injection Prevention**: Prisma ORM provides protection
- **XSS Prevention**: Input sanitization and validation
- **Data Length Limits**: Prevent buffer overflow attacks
- **UUID Validation**: Ensure proper format for IDs

### Access Control
- **Authentication**: Integrate with existing auth system
- **Authorization**: Role-based access control
- **Rate Limiting**: Prevent API abuse
- **Audit Logging**: Track all CRUD operations

## Swagger Documentation

The API is fully documented in Swagger UI at:
- **URL**: `http://localhost:3000/api/docs`
- **Tag**: "Purpose of Activities"
- **Interactive Testing**: Try all endpoints directly from Swagger UI

## Conclusion

The Purpose of Activity CRUD API provides a robust, well-documented, and thoroughly tested solution for managing activity purposes. It follows best practices for:

- **Database Query Optimization**
- **Comprehensive Error Handling**
- **Input Validation and Sanitization**
- **RESTful API Design**
- **Extensive Documentation**
- **Test Coverage**

The implementation ensures data integrity, provides excellent performance, and offers a great developer experience through comprehensive documentation and error handling.
