# Permissions Seeder Documentation

## Overview

The permissions seeder automatically populates the database with essential permissions for user and role management if the permissions table is empty. This ensures that the application has the necessary permissions for administration functionality.

## Seeded Permissions

The seeder creates the following 8 permissions:

### User Management Permissions
| Permission ID | Name | Description |
|---------------|------|-------------|
| `users.create` | Create Users | administration |
| `users.read` | View Users | administration |
| `users.update` | Update Users | administration |
| `users.delete` | Delete Users | administration |

### Role Management Permissions
| Permission ID | Name | Description |
|---------------|------|-------------|
| `roles.create` | Create Roles | administration |
| `roles.read` | View Roles | administration |
| `roles.update` | Update Roles | administration |
| `roles.delete` | Delete Roles | administration |

## How It Works

### 1. **Conditional Seeding**
The seeder only creates permissions if the permissions table is completely empty:

```typescript
const existingPermissions = await prisma.permission.findMany();

if (existingPermissions.length === 0) {
  // Create permissions
} else {
  // Skip creation
}
```

### 2. **Bulk Creation**
Uses Prisma's `createMany()` for efficient bulk insertion:

```typescript
await prisma.permission.createMany({
  data: permissionsData,
  skipDuplicates: true,
});
```

### 3. **Duplicate Protection**
- Uses `skipDuplicates: true` to prevent errors if permissions somehow exist
- Checks table emptiness before attempting creation
- Safe to run multiple times

## Usage

### Running the Seeder

```bash
# Run the complete database seeder (includes permissions)
npm run db:seed
```

### Expected Output

**When permissions don't exist:**
```
🌱 Starting database seeding...
🔐 Creating permissions...
✅ Created 8 permissions: Create Users, View Users, Update Users, Delete Users, Create Roles, View Roles, Update Roles, Delete Roles
```

**When permissions already exist:**
```
🌱 Starting database seeding...
🔐 Permissions already exist (8 found)
```

## Database Schema

The permissions are stored in the `permissions` table with the following structure:

```sql
CREATE TABLE permissions (
  id VARCHAR PRIMARY KEY,           -- e.g., 'users.create'
  name VARCHAR NOT NULL,            -- e.g., 'Create Users'
  description VARCHAR               -- e.g., 'administration'
);
```

## Integration with Roles

These permissions are designed to be assigned to roles through the `role_permissions` junction table:

```typescript
// Example: Assign permissions to a role
await prisma.rolePermission.createMany({
  data: [
    { role_id: adminRoleId, permission_id: 'users.create' },
    { role_id: adminRoleId, permission_id: 'users.read' },
    { role_id: adminRoleId, permission_id: 'users.update' },
    { role_id: adminRoleId, permission_id: 'users.delete' },
    // ... more permissions
  ]
});
```

## Permission Naming Convention

The permissions follow a consistent naming pattern:

- **Format**: `{resource}.{action}`
- **Resources**: `users`, `roles`
- **Actions**: `create`, `read`, `update`, `delete` (CRUD operations)

This pattern makes it easy to:
- Understand what each permission controls
- Add new permissions for other resources
- Implement permission checking in the application

## Use Cases

### 1. **Initial Setup**
When setting up a new environment, the seeder ensures all necessary permissions exist.

### 2. **Development**
Developers can reset their database and run the seeder to get a consistent set of permissions.

### 3. **Production Deployment**
Safe to run during deployment as it won't create duplicates.

### 4. **Role-Based Access Control (RBAC)**
These permissions form the foundation for implementing RBAC in the application.

## Verification

To verify that permissions were seeded correctly:

```sql
-- Check all permissions
SELECT * FROM permissions ORDER BY id;

-- Count permissions
SELECT COUNT(*) as permission_count FROM permissions;

-- Check specific permission
SELECT * FROM permissions WHERE id = 'users.create';
```

Expected result: 8 permissions with the exact IDs, names, and descriptions as specified.

## Extending the Seeder

To add more permissions, update the `permissionsData` array in `prisma/seed.ts`:

```typescript
const permissionsData = [
  // Existing permissions...
  {
    id: "leads.create",
    name: "Create Leads",
    description: "lead management"
  },
  {
    id: "leads.read", 
    name: "View Leads",
    description: "lead management"
  },
  // ... more permissions
];
```

## Best Practices

### 1. **Consistent Naming**
- Use lowercase resource names
- Use standard CRUD action names
- Follow the `{resource}.{action}` pattern

### 2. **Meaningful Descriptions**
- Group related permissions with same description
- Use clear, concise descriptions
- Consider using categories like "administration", "lead management", etc.

### 3. **Safe Operations**
- Always check for existing data before seeding
- Use `skipDuplicates: true` for safety
- Make seeders idempotent (safe to run multiple times)

## Troubleshooting

### Permission Creation Fails
```
Error: Unique constraint failed on the fields: (`id`)
```
**Solution**: Permissions already exist. The seeder should skip creation automatically.

### Missing Permissions
If some permissions are missing, you can:
1. Delete all permissions: `DELETE FROM permissions;`
2. Run seeder again: `npm run db:seed`

### Seeder Doesn't Run
```
Error: Cannot find module 'prisma/seed.ts'
```
**Solution**: Ensure you're in the project root directory and the file exists.

## Security Considerations

1. **Permission IDs**: Use descriptive but not overly detailed IDs
2. **Descriptions**: Avoid exposing internal system details
3. **Access Control**: Ensure only authorized users can manage permissions
4. **Audit Trail**: Consider logging permission changes in production

## Related Files

- **Seeder**: `prisma/seed.ts`
- **Schema**: `prisma/schema.prisma` (Permission model)
- **Package Script**: `package.json` (`db:seed` command)

The permissions seeder provides a solid foundation for implementing role-based access control in your application, ensuring that essential administrative permissions are always available.
