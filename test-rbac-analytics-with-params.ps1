# Test with valid UUID
$response1 = Invoke-WebRequest -Uri 'http://localhost:3000/api/v1/leads/rbac-analytics?branch_id=550e8400-e29b-41d4-a716-************&start_date=2024-01-01&end_date=2024-12-31' -Headers @{'Authorization'='Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************.bJI5C0e5T5rM8vRRsAhXAjgr0KAecVjCGdEXA03u6EU'; 'Content-Type'='application/json'} -Method GET
Write-Host "Test 1 - With valid UUID and date filters:"
Write-Host "Status Code: $($response1.StatusCode)"
Write-Host "Content: $($response1.Content)"
Write-Host ""

# Test with invalid UUID (should fail)
try {
    $response2 = Invoke-WebRequest -Uri 'http://localhost:3000/api/v1/leads/rbac-analytics?branch_id=invalid-uuid' -Headers @{'Authorization'='Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************.bJI5C0e5T5rM8vRRsAhXAjgr0KAecVjCGdEXA03u6EU'; 'Content-Type'='application/json'} -Method GET
    Write-Host "Test 2 - With invalid UUID:"
    Write-Host "Status Code: $($response2.StatusCode)"
    Write-Host "Content: $($response2.Content)"
} catch {
    Write-Host "Test 2 - With invalid UUID (Expected to fail):"
    Write-Host "Error: $($_.Exception.Message)"
}
