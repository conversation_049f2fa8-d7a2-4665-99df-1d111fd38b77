# Scheduled Tasks - Complete Testing Guide

This guide provides comprehensive examples for testing all aspects of the scheduled task system.

## 🚀 Quick Start

### 1. Start the Services
```bash
# Start all services
docker compose up -d

# Check service status
docker compose ps

# View logs
docker compose logs -f nest-scheduler
docker compose logs -f nest-worker
```

### 2. Run Database Migration
```bash
# Run the migration to create scheduled task tables
docker compose -f docker-compose.yml -f docker-compose.migrate.yml run --rm migrate
```

## 📝 API Testing Examples

### Authentication
First, get an authentication token:
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

Save the token for subsequent requests:
```bash
export TOKEN="your-jwt-token-here"
```

## 🎯 Task Creation Examples

### Example 1: Console Log Every 5 Seconds
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "console-log",
    "name": "Test Console Log",
    "description": "Logs a message every 5 seconds",
    "payload": {
      "message": "Hi I work",
      "level": "info"
    },
    "runAt": "'$(date -u -d '+10 seconds' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "intervalType": "MINUTES",
    "intervalValue": 1,
    "priority": 5,
    "maxAttempts": 3
  }'
```

### Example 2: Daily Task at 10:45 PM Nairobi Time
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "daily-reminder",
    "name": "Daily 10:45 PM Task",
    "description": "Runs every day at 10:45 PM Nairobi time",
    "payload": {
      "message": "Daily task executed at 10:45 PM Nairobi time",
      "timezone": "Africa/Nairobi"
    },
    "runAt": "'$(date -u -d 'today 19:45' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "cronExpression": "45 19 * * *",
    "priority": 7,
    "maxAttempts": 2
  }'
```

### Example 3: Send Email Task
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "send-email",
    "name": "Welcome Email",
    "description": "Send welcome email to new user",
    "payload": {
      "to": "<EMAIL>",
      "subject": "Welcome to KB Tracker!",
      "template": "welcome",
      "context": {
        "name": "John Doe",
        "loginUrl": "https://app.example.com/login"
      }
    },
    "runAt": "'$(date -u -d '+1 minute' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "priority": 8,
    "maxAttempts": 3
  }'
```

### Example 4: Generate Weekly Report
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "generate-report",
    "name": "Weekly Sales Report",
    "description": "Generate weekly sales report every Monday",
    "payload": {
      "reportType": "sales",
      "userId": "admin-123",
      "filters": {
        "dateRange": "last-week",
        "includeCharts": true
      },
      "format": "excel",
      "recipients": ["<EMAIL>"]
    },
    "runAt": "'$(date -u -d 'next monday 09:00' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "cronExpression": "0 9 * * 1",
    "priority": 6,
    "maxAttempts": 2
  }'
```

### Example 5: Data Processing Task
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "data-processing",
    "name": "Import Customer Data",
    "description": "Process uploaded customer data file",
    "payload": {
      "type": "excel-import",
      "fileUrl": "/uploads/customers.xlsx",
      "userId": "admin-123",
      "metadata": {
        "importType": "customers",
        "validateData": true,
        "sendNotification": true
      }
    },
    "runAt": "'$(date -u -d '+30 seconds' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "priority": 9,
    "maxAttempts": 1
  }'
```

### Example 6: System Cleanup Task
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "cleanup",
    "name": "Weekly System Cleanup",
    "description": "Clean up old logs and temporary files",
    "payload": {
      "cleanupType": "system-maintenance",
      "targets": ["old-logs", "temp-files", "expired-tokens"],
      "olderThanDays": 30
    },
    "runAt": "'$(date -u -d 'next sunday 02:00' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "cronExpression": "0 2 * * 0",
    "priority": 3,
    "maxAttempts": 2
  }'
```

### Example 7: Notification Task
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "notification",
    "name": "Daily Reminder",
    "description": "Send daily reminder to users",
    "payload": {
      "notificationType": "reminder",
      "message": "Don'\''t forget to update your daily activities!",
      "recipients": ["all-users"],
      "channels": ["email", "in-app"]
    },
    "runAt": "'$(date -u -d 'tomorrow 08:00' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "intervalType": "DAYS",
    "intervalValue": 1,
    "priority": 4,
    "maxAttempts": 3
  }'
```

## 📋 Task Management Examples

### List All Tasks
```bash
curl -X GET "http://localhost:3000/api/scheduled-tasks?page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

### Filter Tasks by Status
```bash
curl -X GET "http://localhost:3000/api/scheduled-tasks?status=PENDING&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

### Filter Tasks by Type
```bash
curl -X GET "http://localhost:3000/api/scheduled-tasks?type=send-email&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

### Get Specific Task
```bash
curl -X GET "http://localhost:3000/api/scheduled-tasks/{task-id}" \
  -H "Authorization: Bearer $TOKEN"
```

### Update Task
```bash
curl -X PATCH "http://localhost:3000/api/scheduled-tasks/{task-id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "priority": 10,
    "runAt": "'$(date -u -d '+1 hour' +%Y-%m-%dT%H:%M:%S.000Z)'"
  }'
```

### Pause Task
```bash
curl -X POST "http://localhost:3000/api/scheduled-tasks/{task-id}/pause" \
  -H "Authorization: Bearer $TOKEN"
```

### Resume Task
```bash
curl -X POST "http://localhost:3000/api/scheduled-tasks/{task-id}/resume" \
  -H "Authorization: Bearer $TOKEN"
```

### Cancel Task
```bash
curl -X POST "http://localhost:3000/api/scheduled-tasks/{task-id}/cancel" \
  -H "Authorization: Bearer $TOKEN"
```

### Delete Task
```bash
curl -X DELETE "http://localhost:3000/api/scheduled-tasks/{task-id}" \
  -H "Authorization: Bearer $TOKEN"
```

### Get Task Execution History
```bash
curl -X GET "http://localhost:3000/api/scheduled-tasks/{task-id}/executions" \
  -H "Authorization: Bearer $TOKEN"
```

## 🕐 Scheduling Patterns

### One-Time Tasks
```json
{
  "runAt": "2024-01-01T10:00:00.000Z"
}
```

### Every N Minutes
```json
{
  "runAt": "2024-01-01T10:00:00.000Z",
  "intervalType": "MINUTES",
  "intervalValue": 30
}
```

### Every N Hours
```json
{
  "runAt": "2024-01-01T10:00:00.000Z",
  "intervalType": "HOURS",
  "intervalValue": 6
}
```

### Daily Tasks
```json
{
  "runAt": "2024-01-01T09:00:00.000Z",
  "intervalType": "DAYS",
  "intervalValue": 1
}
```

### Weekly Tasks
```json
{
  "runAt": "2024-01-01T09:00:00.000Z",
  "intervalType": "WEEKS",
  "intervalValue": 1
}
```

### Monthly Tasks
```json
{
  "runAt": "2024-01-01T09:00:00.000Z",
  "intervalType": "MONTHS",
  "intervalValue": 1
}
```

### Cron Expressions
```json
{
  "runAt": "2024-01-01T09:00:00.000Z",
  "cronExpression": "0 9 * * 1-5"
}
```

Common cron patterns:
- `"0 9 * * 1-5"` - Every weekday at 9 AM
- `"0 0 * * 0"` - Every Sunday at midnight
- `"*/15 * * * *"` - Every 15 minutes
- `"0 */2 * * *"` - Every 2 hours
- `"0 9 1 * *"` - First day of every month at 9 AM

## 🔍 Monitoring Examples

### Check Queue Stats
```bash
curl -X GET "http://localhost:3000/api/queue/stats" \
  -H "Authorization: Bearer $TOKEN"
```

### View Service Logs
```bash
# Scheduler logs
docker compose logs -f nest-scheduler

# Worker logs
docker compose logs -f nest-worker

# API logs
docker compose logs -f nest-api
```

### Database Queries
```sql
-- Check all scheduled tasks
SELECT id, type, name, status, run_at, next_run_at, priority 
FROM scheduled_tasks 
ORDER BY priority DESC, run_at ASC;

-- Check recent executions
SELECT st.type, st.name, ste.status, ste.started_at, ste.duration_ms, ste.error_message
FROM scheduled_task_executions ste
JOIN scheduled_tasks st ON ste.task_id = st.id
ORDER BY ste.started_at DESC
LIMIT 20;

-- Check failed tasks
SELECT st.type, st.name, st.error_message, st.attempts, st.max_attempts
FROM scheduled_tasks st
WHERE st.status = 'FAILED'
ORDER BY st.updated_at DESC;
```

## 🧪 Testing Scenarios

### Test 1: Immediate Execution
Create a task that runs immediately:
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "console-log",
    "name": "Immediate Test",
    "payload": {"message": "Testing immediate execution"},
    "runAt": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
    "priority": 10
  }'
```

### Test 2: Recurring Task
Create a task that runs every 2 minutes:
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "console-log",
    "name": "Recurring Test",
    "payload": {"message": "Recurring task execution"},
    "runAt": "'$(date -u -d '+1 minute' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "intervalType": "MINUTES",
    "intervalValue": 2,
    "priority": 5
  }'
```

### Test 3: Failed Task Retry
Create a task that will fail and retry:
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "test-failure",
    "name": "Failure Test",
    "payload": {"shouldFail": true},
    "runAt": "'$(date -u -d '+30 seconds' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "maxAttempts": 3,
    "priority": 1
  }'
```

## 🎯 Your Specific Examples

### Console Log Every 5 Seconds (Actually every minute for testing)
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "console-log",
    "name": "Hi I Work Logger",
    "description": "Logs Hi I work every minute",
    "payload": {
      "message": "Hi I work",
      "level": "info"
    },
    "runAt": "'$(date -u -d '+10 seconds' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "intervalType": "MINUTES",
    "intervalValue": 1,
    "priority": 5
  }'
```

### Daily 10:45 PM Nairobi Time Task
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "daily-nairobi-task",
    "name": "Daily 10:45 PM Nairobi Task",
    "description": "Runs daily at 10:45 PM Africa/Nairobi timezone",
    "payload": {
      "message": "Daily task executed at 10:45 PM Nairobi time",
      "timezone": "Africa/Nairobi",
      "localTime": "22:45"
    },
    "runAt": "'$(TZ=Africa/Nairobi date -d 'today 22:45' -u +%Y-%m-%dT%H:%M:%S.000Z)'",
    "cronExpression": "45 19 * * *",
    "priority": 7
  }'
```

Note: The cron expression "45 19 * * *" represents 19:45 UTC, which is 22:45 in Africa/Nairobi (UTC+3).

## 🎯 Type-to-Function Mapping

The system now supports a flexible type-to-function mapping through the TaskHandlerRegistry. Here's how it works:

### Built-in Task Handlers

#### 1. Console Log Handler (`console-log`)
**Purpose**: Logs messages to console with different log levels
**Payload Structure**:
```json
{
  "message": "Hi I work",
  "level": "info",
  "metadata": {
    "source": "scheduled-task",
    "environment": "production"
  }
}
```

#### 2. Daily Nairobi Task Handler (`daily-nairobi-task`)
**Purpose**: Executes tasks at specific times in Africa/Nairobi timezone
**Payload Structure**:
```json
{
  "message": "Daily task executed at 10:45 PM Nairobi time",
  "timezone": "Africa/Nairobi",
  "localTime": "22:45",
  "metadata": {
    "taskType": "daily-reminder",
    "location": "Nairobi"
  }
}
```

### Legacy Task Types (Backward Compatible)

#### 3. Send Email (`send-email`)
**Purpose**: Sends emails via the email queue
**Payload Structure**:
```json
{
  "to": "<EMAIL>",
  "subject": "Email Subject",
  "template": "template-name",
  "context": { "name": "John" }
}
```

#### 4. Generate Report (`generate-report`)
**Purpose**: Generates reports via the reports queue
**Payload Structure**:
```json
{
  "userId": "user-123",
  "reportType": "sales",
  "filters": { "dateFrom": "2024-01-01" },
  "format": "excel"
}
```

#### 5. Data Processing (`data-processing`)
**Purpose**: Processes data via the data processing queue
**Payload Structure**:
```json
{
  "type": "excel-import",
  "fileUrl": "/uploads/data.xlsx",
  "userId": "user-123",
  "metadata": { "importType": "customers" }
}
```

#### 6. Cleanup (`cleanup`)
**Purpose**: Performs system cleanup tasks
**Payload Structure**:
```json
{
  "cleanupType": "system-maintenance",
  "targets": ["old-logs", "temp-files", "expired-tokens"],
  "olderThanDays": 30
}
```

#### 7. Notification (`notification`)
**Purpose**: Sends various types of notifications
**Payload Structure**:
```json
{
  "notificationType": "reminder",
  "message": "Don't forget to update your activities!",
  "recipients": ["user-123"],
  "channels": ["email", "in-app"]
}
```

### Adding Custom Task Handlers

To add a new task type, create a handler that implements the `TaskHandler` interface:

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';

@Injectable()
export class CustomTaskHandler implements TaskHandler {
  private readonly logger = new Logger(CustomTaskHandler.name);

  getTaskType(): string {
    return 'custom-task';
  }

  getDescription(): string {
    return 'Description of what this task does';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Add validation logic here
    if (!payload.requiredField) {
      errors.push('requiredField is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: any, job: Job): Promise<any> {
    // Implement your task logic here
    await job.updateProgress(50);

    // Do the actual work
    const result = await this.doWork(payload);

    await job.updateProgress(100);

    return result;
  }

  private async doWork(payload: any): Promise<any> {
    // Your custom logic here
    return { success: true, data: payload };
  }
}
```

Then register it in the `TaskHandlerRegistry`:

```typescript
// In task-handler.registry.ts
constructor(
  // ... other dependencies
  private readonly customTaskHandler: CustomTaskHandler,
) {
  this.registerHandlers();
}

private registerHandlers() {
  // ... existing handlers
  this.registerHandler(this.customTaskHandler);
}
```

## 🧪 Complete Testing Workflow

### 1. Setup and Start Services
```bash
# Start all services
docker compose up -d

# Run database migration
docker compose -f docker-compose.yml -f docker-compose.migrate.yml run --rm migrate

# Check service status
docker compose ps
```

### 2. Run the Automated Test Script
```bash
# Make the script executable
chmod +x scripts/test-scheduled-tasks.sh

# Run the complete test suite
./scripts/test-scheduled-tasks.sh
```

### 3. Manual Testing Examples

#### Get Available Task Types
```bash
curl -X GET "http://localhost:3000/api/scheduled-tasks/task-types" \
  -H "Authorization: Bearer $TOKEN" | jq
```

#### Create Your Specific Tasks

**Console Log Every Minute (for testing - change to 5 seconds in production)**:
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "console-log",
    "name": "Hi I Work Logger",
    "description": "Logs Hi I work every minute",
    "payload": {
      "message": "Hi I work",
      "level": "info"
    },
    "runAt": "'$(date -u -d '+10 seconds' +%Y-%m-%dT%H:%M:%S.000Z)'",
    "intervalType": "MINUTES",
    "intervalValue": 1,
    "priority": 5
  }'
```

**Daily 10:45 PM Nairobi Time**:
```bash
curl -X POST http://localhost:3000/api/scheduled-tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "type": "daily-nairobi-task",
    "name": "Daily 10:45 PM Nairobi Task",
    "description": "Runs daily at 10:45 PM Africa/Nairobi timezone",
    "payload": {
      "message": "Daily task executed at 10:45 PM Nairobi time",
      "timezone": "Africa/Nairobi",
      "localTime": "22:45"
    },
    "runAt": "'$(TZ=Africa/Nairobi date -d 'today 22:45' -u +%Y-%m-%dT%H:%M:%S.000Z)'",
    "cronExpression": "45 19 * * *",
    "priority": 7
  }'
```

### 4. Monitor Execution
```bash
# Watch worker logs
docker compose logs -f nest-worker

# Watch scheduler logs
docker compose logs -f nest-scheduler

# Check database
docker compose exec postgres psql -U postgres -d kb_tracker -c "
SELECT id, type, name, status, run_at, next_run_at, attempts
FROM scheduled_tasks
ORDER BY created_at DESC
LIMIT 10;"

# Check executions
docker compose exec postgres psql -U postgres -d kb_tracker -c "
SELECT st.type, st.name, ste.status, ste.started_at, ste.duration_ms
FROM scheduled_task_executions ste
JOIN scheduled_tasks st ON ste.task_id = st.id
ORDER BY ste.started_at DESC
LIMIT 10;"
```

## 🎯 Expected Results

### Console Log Task
- **Frequency**: Every minute
- **Log Output**: You should see "Hi I work" in the worker logs
- **Database**: Task status should cycle between PENDING → RUNNING → COMPLETED
- **Next Run**: Should automatically reschedule for next minute

### Daily Nairobi Task
- **Frequency**: Daily at 10:45 PM Nairobi time (7:45 PM UTC)
- **Log Output**: Detailed execution info with Nairobi timezone
- **Database**: Shows cron expression and next run time
- **Features**: Detects weekends vs weekdays, logs system info

### Task Management
- **Pause/Resume**: Tasks can be paused and resumed
- **Cancellation**: Tasks can be cancelled
- **History**: Complete execution history is maintained
- **Retry Logic**: Failed tasks retry with exponential backoff

## 🔍 Troubleshooting

### Common Issues

1. **Tasks not executing**
   - Check scheduler service is running
   - Verify database connectivity
   - Ensure run_at time is in the future

2. **Authentication errors**
   - Update credentials in test script
   - Check JWT token validity
   - Verify user permissions

3. **Timezone issues**
   - Verify system timezone settings
   - Check cron expression calculation
   - Ensure date-fns-tz is properly installed

### Debug Commands
```bash
# Check service health
docker compose exec nest-api curl http://localhost:3000/api/v1/health

# Check queue stats
curl -H "Authorization: Bearer $TOKEN" http://localhost:3000/api/queue/stats

# List all tasks
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/api/scheduled-tasks?limit=100"
```

This comprehensive system provides a robust, database-driven scheduled task solution with full persistence, monitoring, and extensibility! 🚀
