# KB Tracker Backend - Docker Management Makefile
# Usage: make [target]

.PHONY: help setup build start stop restart logs clean migrate seed reset studio status dev prod tools

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

# Docker Compose files
COMPOSE_FILE := docker-compose.yml
COMPOSE_DEV := docker-compose.dev.yml
COMPOSE_PROD := docker-compose.prod.yml
COMPOSE_MIGRATE := docker-compose.migrate.yml

help: ## Show this help message
	@echo "$(BLUE)KB Tracker Backend - Docker Management$(NC)"
	@echo ""
	@echo "$(GREEN)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)Examples:$(NC)"
	@echo "  make setup     # Complete setup"
	@echo "  make dev       # Start in development mode"
	@echo "  make prod      # Start in production mode"
	@echo "  make logs      # View logs"
	@echo "  make migrate   # Run database migrations"

setup: ## Complete setup (build, start, migrate)
	@echo "$(BLUE)Starting complete setup...$(NC)"
	@./scripts/docker-setup.sh full

build: ## Build all Docker images
	@echo "$(BLUE)Building Docker images...$(NC)"
	@./scripts/docker-build.sh prod

start: ## Start all services
	@echo "$(BLUE)Starting services...$(NC)"
	@docker compose up -d || docker-compose up -d

stop: ## Stop all services
	@echo "$(BLUE)Stopping services...$(NC)"
	@docker compose down

restart: ## Restart all services
	@echo "$(BLUE)Restarting services...$(NC)"
	@docker compose restart

logs: ## View logs from all services
	@docker compose logs -f

logs-api: ## View API logs only
	@docker compose logs -f nest-api

logs-worker: ## View worker logs only
	@docker compose logs -f nest-worker

logs-scheduler: ## View scheduler logs only
	@docker compose logs -f nest-scheduler

logs-db: ## View database logs only
	@docker compose logs -f postgres

status: ## Show service status
	@echo "$(BLUE)Service Status:$(NC)"
	@docker compose ps
	@echo ""
	@echo "$(GREEN)Available endpoints:$(NC)"
	@echo "  API: http://localhost:3000"
	@echo "  Docs: http://localhost:3000/api/docs"
	@echo "  Health: http://localhost:3000/api/v1/health"

clean: ## Stop services and remove containers, networks, volumes
	@echo "$(RED)Warning: This will remove all containers, networks, and volumes!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker compose down -v --remove-orphans
	@docker system prune -f

clean-images: ## Remove all Docker images
	@echo "$(RED)Warning: This will remove all Docker images!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker compose down --rmi all

# Database Management
migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	@./scripts/docker-migrate.sh migrate

seed: ## Run database seeds
	@echo "$(BLUE)Running database seeds...$(NC)"
	@./scripts/docker-migrate.sh seed

reset: ## Reset database (development only)
	@echo "$(RED)Warning: This will reset the database!$(NC)"
	@./scripts/docker-migrate.sh reset

studio: ## Start Prisma Studio
	@echo "$(BLUE)Starting Prisma Studio...$(NC)"
	@./scripts/docker-migrate.sh studio

# Environment-specific targets
dev: ## Start in development mode with hot reload
	@./scripts/docker-run.sh dev start

dev-build: ## Build and start in development mode
	@./scripts/docker-run.sh dev build-and-start

prod: ## Start in production mode
	@./scripts/docker-run.sh prod start

prod-build: ## Build and start in production mode
	@./scripts/docker-run.sh prod build-and-start

# Tools and utilities
tools: ## Start with management tools (pgAdmin, Redis Commander)
	@echo "$(BLUE)Starting with management tools...$(NC)"
	@docker compose --profile tools up -d

shell-api: ## Open shell in API container
	@docker compose exec nest-api sh

shell-db: ## Open PostgreSQL shell
	@docker compose exec postgres psql -U postgres -d kb_tracker

shell-redis: ## Open Redis CLI
	@docker compose exec redis redis-cli

# Scaling
scale-workers: ## Scale worker services (usage: make scale-workers WORKERS=3)
	@echo "$(BLUE)Scaling workers to $(WORKERS) instances...$(NC)"
	@docker compose up -d --scale nest-worker=$(WORKERS)

scale-api: ## Scale API services (usage: make scale-api REPLICAS=3)
	@echo "$(BLUE)Scaling API to $(REPLICAS) instances...$(NC)"
	@docker compose up -d --scale nest-api=$(REPLICAS)

# Health checks
health: ## Check service health
	@echo "$(BLUE)Checking service health...$(NC)"
	@curl -s http://localhost:3000/api/v1/health || echo "$(RED)API not responding$(NC)"
	@docker compose exec -T postgres pg_isready -U postgres || echo "$(RED)PostgreSQL not ready$(NC)"
	@docker compose exec -T redis redis-cli ping || echo "$(RED)Redis not responding$(NC)"

# Backup and restore
backup-db: ## Backup database
	@echo "$(BLUE)Creating database backup...$(NC)"
	@mkdir -p backups
	@docker compose exec -T postgres pg_dump -U postgres kb_tracker > backups/kb_tracker_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Database backup created in backups/$(NC)"

restore-db: ## Restore database from backup (usage: make restore-db BACKUP=filename.sql)
	@echo "$(RED)Warning: This will replace the current database!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker compose exec -T postgres psql -U postgres -d kb_tracker < backups/$(BACKUP)
	@echo "$(GREEN)Database restored from $(BACKUP)$(NC)"

# Monitoring
stats: ## Show Docker stats
	@docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

top: ## Show running processes in containers
	@docker compose top

# Development helpers
test: ## Run tests in API container
	@docker compose exec nest-api npm test

test-e2e: ## Run e2e tests in API container
	@docker compose exec nest-api npm run test:e2e

lint: ## Run linter in API container
	@docker compose exec nest-api npm run lint

format: ## Format code in API container
	@docker compose exec nest-api npm run format

# Quick commands
up: start ## Alias for start
down: stop ## Alias for stop
ps: status ## Alias for status
