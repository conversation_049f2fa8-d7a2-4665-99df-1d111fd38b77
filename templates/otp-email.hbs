<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your One-Time Password (OTP) for {{ app_name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #1c5b41;
        }
        .logo {
            color: #1c5b41;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #1c5b41;
        }
        .content {
            margin-bottom: 30px;
        }
        .otp-container {
            background-color: #f8f9fa;
            border: 2px solid #1c5b41;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            margin: 25px 0;
        }
        .otp-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .otp-code {
            font-size: 32px;
            font-weight: bold;
            color: #1c5b41;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
        }
        .validity {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .validity-text {
            color: #856404;
            font-weight: bold;
        }
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .timestamp {
            font-size: 12px;
            color: #999;
            margin-top: 15px;
        }
        .brand-color {
            color: #1c5b41;
        }
        .support-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{ app_name }}</div>
            <div class="subtitle">Secure Banking Solutions</div>
        </div>

        <div class="greeting">
            Hi {{ name }},
        </div>

        <div class="content">
            <p>Your One-Time Password (OTP) for accessing your <strong class="brand-color">{{ app_name }}</strong> account is:</p>
            
            <div class="otp-container">
                <div class="otp-label">Your OTP Code</div>
                <div class="otp-code">{{ otp }}</div>
            </div>

            <div class="validity">
                <div class="validity-text">
                    ⏰ This code is valid for the next <strong>{{ otp_validity_minutes }} minutes</strong>
                </div>
            </div>

            <div class="warning">
                <strong>Security Notice:</strong> If you didn't request this code, please ignore this message and contact our support team immediately.
            </div>
        </div>

        <div class="support-info">
            <strong>Need Help?</strong><br>
            For any concerns or questions, please contact our support team. We're here to help ensure your account remains secure.
        </div>

        <div class="footer">
            <p>Thanks,<br>
            <strong class="brand-color">The {{ app_name }} Team</strong></p>
            
            <div class="timestamp">
                Sent on: {{ timestamp }}
            </div>
        </div>
    </div>
</body>
</html>
