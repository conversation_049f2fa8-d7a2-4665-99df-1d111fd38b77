#!/bin/bash

# Docker development script for KB Tracker Backend
# Usage: ./docker-dev.sh [command]
# Commands: start, stop, restart, logs, shell, build, clean

set -e

COMPOSE_FILE="docker-compose.dev.yml"
SERVICE_NAME="nest-api"
IMAGE_NAME="kb-tracker-backend-dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🐳 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to detect docker compose command
detect_docker_compose() {
    if command -v "docker" >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "docker compose"
    elif command -v "docker-compose" >/dev/null 2>&1; then
        echo "docker-compose"
    else
        print_error "Neither 'docker compose' nor 'docker-compose' is available"
        exit 1
    fi
}

# Function to build development image
build_dev() {
    print_status "Building development image..."
    
    # Use legacy docker build to avoid buildx issues
    docker build --target dependencies -t $IMAGE_NAME:latest .
    
    print_success "Development image built successfully!"
    print_status "Image: $IMAGE_NAME:latest"
}

# Function to start development environment
start_dev() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Starting development environment..."

    # Build the image first if it doesn't exist
    if ! docker image inspect $IMAGE_NAME:latest > /dev/null 2>&1; then
        print_status "Development image not found, building it first..."
        build_dev
    fi

    # Start with pre-built image
    $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE up -d
    print_success "Development environment started!"
    echo ""
    print_status "Application will be available at: http://localhost:3000"
    print_status "API Documentation: http://localhost:3000/api/docs"
    print_status "pgAdmin: http://localhost:8080"
    print_status "Redis Commander: http://localhost:8081"
    print_status "Hot reloading is enabled - changes will reflect automatically"
    echo ""
    print_status "Useful commands:"
    echo "  View logs:    ./docker-dev.sh logs"
    echo "  Stop:         ./docker-dev.sh stop"
    echo "  Shell access: ./docker-dev.sh shell"
}

# Function to stop development environment
stop_dev() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Stopping development environment..."
    $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE down
    print_success "Development environment stopped!"
}

# Function to restart development environment
restart_dev() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Restarting development environment..."
    $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE restart
    print_success "Development environment restarted!"
}

# Function to show logs
show_logs() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Showing development logs (Ctrl+C to exit)..."
    $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE logs -f
}

# Function to access shell
access_shell() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Accessing development container shell..."
    $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE exec $SERVICE_NAME sh
}

# Function to clean up
clean_dev() {
    local compose_cmd=$(detect_docker_compose)
    print_warning "This will remove all containers, images, and volumes for development environment."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up development environment..."
        $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE down -v --rmi all
        # Also remove the specific development image
        docker rmi $IMAGE_NAME:latest 2>/dev/null || true
        print_success "Development environment cleaned up!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show status
show_status() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Development environment status:"
    $compose_cmd -f docker-compose.yml -f $COMPOSE_FILE ps
}

# Function to show help
show_help() {
    echo "KB Tracker Backend - Development Docker Script"
    echo ""
    echo "Usage: ./docker-dev.sh [command]"
    echo ""
    echo "Commands:"
    echo "  start     Start the development environment with hot reloading"
    echo "  stop      Stop the development environment"
    echo "  restart   Restart the development environment"
    echo "  logs      Show and follow development logs"
    echo "  shell     Access the development container shell"
    echo "  build     Build the development image"
    echo "  clean     Clean up all development containers and images"
    echo "  status    Show development environment status"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./docker-dev.sh start    # Start development with hot reloading"
    echo "  ./docker-dev.sh logs     # View development logs"
    echo "  ./docker-dev.sh shell    # Access container for debugging"
}

# Main script logic
check_docker

case "${1:-help}" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart_dev
        ;;
    logs)
        show_logs
        ;;
    shell)
        access_shell
        ;;
    build)
        build_dev
        ;;
    clean)
        clean_dev
        ;;
    status)
        show_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
