#!/bin/bash

# Docker production script for KB Tracker Backend
# Usage: ./docker-prod.sh [command]
# Commands: start, stop, restart, logs, shell, build, clean

set -e

COMPOSE_FILE="docker-compose.yml"
SERVICE_NAME="nest-api"
IMAGE_NAME="kb-tracker-backend-prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🐳 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating a sample .env file..."
        cat > .env << 'EOF'
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=kb_tracker
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/kb_tracker

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=production
PORT=3000
JWT_SECRET=your-jwt-secret-here-change-this-in-production
JWT_EXPIRES_IN=7d

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password

# Admin Tools Configuration (Optional for production)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin
REDIS_COMMANDER_USER=admin
REDIS_COMMANDER_PASSWORD=admin

# Worker Configuration
WORKER_CONCURRENCY=10
WORKER_MAX_STALLED_COUNT=3
WORKER_STALLED_INTERVAL=30000

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Africa/Nairobi
EOF
        print_success "Sample .env file created. Please update it with your actual production values."
        print_error "IMPORTANT: Update JWT_SECRET and other sensitive values before starting production!"
        exit 1
    fi
}

# Function to detect docker compose command
detect_docker_compose() {
    if command -v "docker" >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "docker compose"
    elif command -v "docker-compose" >/dev/null 2>&1; then
        echo "docker-compose"
    else
        print_error "Neither 'docker compose' nor 'docker-compose' is available"
        exit 1
    fi
}

# Function to build production image
build_prod() {
    print_status "Building production image..."

    # Force use of legacy builder and disable buildx
    export DOCKER_BUILDKIT=0
    export COMPOSE_DOCKER_CLI_BUILD=0

    # Clean up any existing intermediate images first
    print_status "Cleaning up intermediate images..."
    docker image prune -f > /dev/null 2>&1 || true

    # Build production image targeting production stage
    print_status "Building production image (this may take a few minutes)..."
    docker build --target production -t $IMAGE_NAME:latest . --no-cache

    if [ $? -eq 0 ]; then
        print_success "Production image built successfully!"
        print_status "Image: $IMAGE_NAME:latest"

        # Clean up intermediate images after successful build
        print_status "Cleaning up intermediate build images..."
        docker image prune -f > /dev/null 2>&1 || true
    else
        print_error "Failed to build production image"
        exit 1
    fi
}

# Function to start production environment
start_prod() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Starting production environment..."

    # Check for .env file
    check_env_file

    # Build the image first if it doesn't exist
    if ! docker image inspect $IMAGE_NAME:latest > /dev/null 2>&1; then
        print_status "Production image not found, building it first..."
        build_prod
    fi

    # Start with pre-built image
    $compose_cmd -f $COMPOSE_FILE up -d
    print_success "Production environment started!"
    echo ""
    print_status "Application is available at: http://localhost:3000"
    print_status "API Documentation: http://localhost:3000/api/docs"
    print_status "Health Check: http://localhost:3000/api/v1/health"
    echo ""
    print_status "Useful commands:"
    echo "  View logs:    ./docker-prod.sh logs"
    echo "  Stop:         ./docker-prod.sh stop"
    echo "  Shell access: ./docker-prod.sh shell"
}

# Function to stop production environment
stop_prod() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Stopping production environment..."
    $compose_cmd -f $COMPOSE_FILE down
    print_success "Production environment stopped!"
}

# Function to restart production environment
restart_prod() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Restarting production environment..."
    $compose_cmd -f $COMPOSE_FILE restart
    print_success "Production environment restarted!"
}

# Function to show logs
show_logs() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Showing production logs (Ctrl+C to exit)..."
    $compose_cmd -f $COMPOSE_FILE logs -f
}

# Function to access shell
access_shell() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Accessing production container shell..."
    $compose_cmd -f $COMPOSE_FILE exec $SERVICE_NAME sh
}

# Function to clean up
clean_prod() {
    local compose_cmd=$(detect_docker_compose)
    print_warning "This will remove all containers, images, and volumes for production environment."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up production environment..."
        $compose_cmd -f $COMPOSE_FILE down -v --rmi all
        # Also remove the specific production image
        docker rmi $IMAGE_NAME:latest 2>/dev/null || true
        print_success "Production environment cleaned up!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show status
show_status() {
    local compose_cmd=$(detect_docker_compose)
    print_status "Production environment status:"
    $compose_cmd -f $COMPOSE_FILE ps
}

# Function to show help
show_help() {
    echo "KB Tracker Backend - Production Docker Script"
    echo ""
    echo "Usage: ./docker-prod.sh [command]"
    echo ""
    echo "Commands:"
    echo "  start     Start the production environment"
    echo "  stop      Stop the production environment"
    echo "  restart   Restart the production environment"
    echo "  logs      Show and follow production logs"
    echo "  shell     Access the production container shell"
    echo "  build     Build the production image"
    echo "  clean     Clean up all production containers and images"
    echo "  status    Show production environment status"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./docker-prod.sh start    # Start production environment"
    echo "  ./docker-prod.sh logs     # View production logs"
    echo "  ./docker-prod.sh shell    # Access container for debugging"
}

# Main script logic
check_docker

case "${1:-help}" in
    start)
        start_prod
        ;;
    stop)
        stop_prod
        ;;
    restart)
        restart_prod
        ;;
    logs)
        show_logs
        ;;
    shell)
        access_shell
        ;;
    build)
        build_prod
        ;;
    clean)
        clean_prod
        ;;
    status)
        show_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
