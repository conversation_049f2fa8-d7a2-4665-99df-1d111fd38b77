// Simple test script to verify the dashboard second section endpoint
const axios = require('axios');

async function testDashboardSecondSection() {
  try {
    console.log('Testing dashboard second section endpoint...');
    
    // Note: This is a basic test - in a real scenario you would need proper authentication
    const response = await axios.get('http://localhost:3000/api/v1/dashboard/branch-manager?section=second', {
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE' // Replace with actual token
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
    // Check if the expected keys are present
    const expectedKeys = [
      'monthly_hitlist_progress',
      'calls_visits_vs_target', 
      'calls_vs_targets_per_officer',
      'visits_vs_targets_per_officer'
    ];
    
    const responseKeys = Object.keys(response.data);
    console.log('\nExpected keys:', expectedKeys);
    console.log('Actual keys:', responseKeys);
    
    const missingKeys = expectedKeys.filter(key => !responseKeys.includes(key));
    if (missingKeys.length === 0) {
      console.log('✅ All expected keys are present!');
    } else {
      console.log('❌ Missing keys:', missingKeys);
    }
    
  } catch (error) {
    if (error.response) {
      console.error('Error response:', error.response.status, error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testDashboardSecondSection();
