# Loan Activities API Documentation

## Overview
This document provides comprehensive documentation for the Loan Activities API endpoints. The API allows you to create, retrieve, and manage loan activities with optional file attachments.

## Base URL
```
http://localhost:3000/api/v1
```

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Data Models

### LoanActivity Model
```typescript
interface LoanActivity {
  id: string;                           // UUID
  loan_client_id?: string;              // UUID of associated loan client
  loan_account_number?: string;         // Loan account number
  purpose_id?: string;                  // UUID of activity purpose
  loan_balance?: string;                // Current loan balance
  arrears_days?: number;                // Days in arrears
  comment?: string;                     // Activity comments
  rm_user_id: string;                   // UUID of RM user
  created_at: string;                   // ISO date string
  updated_at: string;                   // ISO date string
  via_api?: boolean;                    // Whether created via API
  api_call_reference?: string;          // API call reference
  interaction_type?: string;            // Type of interaction (call, visit, email)
  call_status?: string;                 // Status of call (answered, missed, busy)
  visit_status?: string;                // Status of visit (completed, scheduled, cancelled)
  next_followup_date?: string;          // ISO date string for next follow-up
  followup_status?: string;             // Status of follow-up (pending, completed, cancelled)
  call_duration_minutes?: number;       // Duration of call in minutes
  purpose?: PurposeDetails;             // Related purpose details
  rm_user?: UserDetails;                // Related RM user details
  loan_client?: LoanClientDetails;      // Related loan client details
  attachments?: AttachmentDetails[];     // File attachments
}
```

### PurposeDetails
```typescript
interface PurposeDetails {
  id: string;
  name: string;
  description?: string;
}
```

### UserDetails
```typescript
interface UserDetails {
  id: string;
  name: string;
  email: string;
  rm_code: string;
}
```

### LoanClientDetails
```typescript
interface LoanClientDetails {
  id: string;
  customer_name?: string;
  account_number?: string;
}
```

### AttachmentDetails
```typescript
interface AttachmentDetails {
  id: string;
  file_url?: string;
  created_at: string;
}
```

## API Endpoints

### 1. Create Loan Activity
**POST** `/loan-activities`

Creates a new loan activity with optional file attachments.

#### Request
- **Content-Type**: `multipart/form-data`
- **Body**:
  ```typescript
  {
    loan_client_id?: string;              // UUID
    loan_account_number?: string;         // Max 50 chars
    purpose_id?: string;                  // UUID
    loan_balance?: string;                // Decimal string
    arrears_days?: number;                // Integer
    comment?: string;                     // Max 1000 chars
    rm_user_id: string;                   // UUID (required)
    via_api?: boolean;                    // Default: false
    api_call_reference?: string;          // Max 255 chars
    interaction_type?: string;            // Max 50 chars
    call_status?: string;                 // Max 50 chars
    visit_status?: string;                // Max 50 chars
    next_followup_date?: string;         // ISO date string
    followup_status?: string;             // Max 50 chars
    call_duration_minutes?: number;       // Integer
    attachments?: File[];                 // Array of files
  }
  ```

#### Response
- **Status**: `201 Created`
- **Body**: `LoanActivityResponseDto`

#### Example Request
```bash
curl -X POST http://localhost:3000/api/v1/loan-activities \
  -H "Authorization: Bearer <your-token>" \
  -F "rm_user_id=550e8400-e29b-41d4-a716-************" \
  -F "loan_client_id=550e8400-e29b-41d4-a716-************" \
  -F "purpose_id=550e8400-e29b-41d4-a716-************" \
  -F "loan_balance=50000.00" \
  -F "arrears_days=30" \
  -F "comment=Customer requested loan restructuring" \
  -F "interaction_type=call" \
  -F "call_status=answered" \
  -F "next_followup_date=2024-02-15T10:00:00.000Z" \
  -F "followup_status=pending" \
  -F "call_duration_minutes=15" \
  -F "attachments=@document1.pdf" \
  -F "attachments=@document2.pdf"
```

### 2. Get Loan Activity by ID
**GET** `/loan-activities/:id`

Retrieves a specific loan activity by its ID.

#### Parameters
- `id` (string, required): UUID of the loan activity

#### Response
- **Status**: `200 OK`
- **Body**: `LoanActivityResponseDto`

#### Example Request
```bash
curl -X GET http://localhost:3000/api/v1/loan-activities/550e8400-e29b-41d4-a716-************ \
  -H "Authorization: Bearer <your-token>"
```

### 3. Get Loan Activities by Client
**GET** `/loan-activities/client/:loanClientId`

Retrieves all loan activities for a specific loan client with pagination.

#### Parameters
- `loanClientId` (string, required): UUID of the loan client
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10)

#### Response
- **Status**: `200 OK`
- **Body**:
  ```typescript
  {
    data: LoanActivityResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }
  ```

#### Example Request
```bash
curl -X GET "http://localhost:3000/api/v1/loan-activities/client/550e8400-e29b-41d4-a716-************?page=1&limit=10" \
  -H "Authorization: Bearer <your-token>"
```

### 4. Get Loan Activities by RM User
**GET** `/loan-activities/rm-user/:rmUserId`

Retrieves all loan activities performed by a specific RM user with pagination.

#### Parameters
- `rmUserId` (string, required): UUID of the RM user
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10)

#### Response
- **Status**: `200 OK`
- **Body**:
  ```typescript
  {
    data: LoanActivityResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }
  ```

#### Example Request
```bash
curl -X GET "http://localhost:3000/api/v1/loan-activities/rm-user/550e8400-e29b-41d4-a716-************?page=1&limit=10" \
  -H "Authorization: Bearer <your-token>"
```

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": ["Validation error message"],
  "error": "Bad Request"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "Loan activity with ID 'uuid' not found",
  "error": "Not Found"
}
```

### 500 Internal Server Error
```json
{
  "statusCode": 500,
  "message": "Internal server error",
  "error": "Internal Server Error"
}
```

## Field Descriptions

### New Fields Added

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `interaction_type` | string | Type of interaction (call, visit, email) | "call" |
| `call_status` | string | Status of the call (answered, missed, busy) | "answered" |
| `visit_status` | string | Status of the visit (completed, scheduled, cancelled) | "completed" |
| `next_followup_date` | DateTime | Date for next follow-up | "2024-02-15T10:00:00.000Z" |
| `followup_status` | string | Status of follow-up (pending, completed, cancelled) | "pending" |
| `call_duration_minutes` | number | Duration of call in minutes | 15 |
| `updated_at` | DateTime | Last update timestamp | "2024-01-15T10:30:00.000Z" |

### Existing Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | Yes | Unique identifier |
| `loan_client_id` | string | No | Associated loan client UUID |
| `loan_account_number` | string | No | Loan account number |
| `purpose_id` | string | No | Activity purpose UUID |
| `loan_balance` | string | No | Current loan balance |
| `arrears_days` | number | No | Days in arrears |
| `comment` | string | No | Activity comments |
| `rm_user_id` | string | Yes | RM user UUID |
| `created_at` | string | Yes | Creation timestamp |
| `via_api` | boolean | No | API creation flag |
| `api_call_reference` | string | No | API call reference |

## Performance Optimizations

### Database Queries
- **Selective Field Fetching**: Only required fields are fetched from the database
- **Eager Loading**: Related data (purpose, rm_user, loan_client, attachments) is loaded in a single query
- **Parallel Queries**: Count and data queries run in parallel for pagination
- **Indexed Fields**: All UUID fields are indexed for fast lookups

### File Handling
- **Asynchronous Uploads**: File uploads are processed asynchronously
- **Transaction Safety**: File uploads and database operations are wrapped in transactions
- **Error Handling**: Failed uploads are cleaned up automatically

## Security Features

### Input Validation
- **UUID Validation**: All UUID fields are validated
- **String Length Limits**: All string fields have maximum length constraints
- **Type Validation**: All fields are validated for correct data types
- **File Validation**: Uploaded files are validated for type and size

### Error Handling
- **Graceful Degradation**: Partial failures don't affect successful operations
- **Detailed Error Messages**: Clear error messages for debugging
- **Transaction Rollback**: Failed operations are rolled back completely

## Usage Examples

### JavaScript/Node.js
```javascript
const FormData = require('form-data');
const fs = require('fs');

async function createLoanActivity() {
  const form = new FormData();
  
  form.append('rm_user_id', '550e8400-e29b-41d4-a716-************');
  form.append('loan_client_id', '550e8400-e29b-41d4-a716-************');
  form.append('purpose_id', '550e8400-e29b-41d4-a716-************');
  form.append('loan_balance', '50000.00');
  form.append('arrears_days', '30');
  form.append('comment', 'Customer requested loan restructuring');
  form.append('interaction_type', 'call');
  form.append('call_status', 'answered');
  form.append('next_followup_date', '2024-02-15T10:00:00.000Z');
  form.append('followup_status', 'pending');
  form.append('call_duration_minutes', '15');
  
  // Add file attachments
  form.append('attachments', fs.createReadStream('./document1.pdf'));
  form.append('attachments', fs.createReadStream('./document2.pdf'));
  
  const response = await fetch('http://localhost:3000/api/v1/loan-activities', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer <your-token>',
      ...form.getHeaders()
    },
    body: form
  });
  
  const result = await response.json();
  console.log(result);
}
```

### Python
```python
import requests
from datetime import datetime

def create_loan_activity():
    url = 'http://localhost:3000/api/v1/loan-activities'
    headers = {
        'Authorization': 'Bearer <your-token>'
    }
    
    data = {
        'rm_user_id': '550e8400-e29b-41d4-a716-************',
        'loan_client_id': '550e8400-e29b-41d4-a716-************',
        'purpose_id': '550e8400-e29b-41d4-a716-************',
        'loan_balance': '50000.00',
        'arrears_days': '30',
        'comment': 'Customer requested loan restructuring',
        'interaction_type': 'call',
        'call_status': 'answered',
        'next_followup_date': '2024-02-15T10:00:00.000Z',
        'followup_status': 'pending',
        'call_duration_minutes': '15'
    }
    
    files = {
        'attachments': [
            ('document1.pdf', open('./document1.pdf', 'rb')),
            ('document2.pdf', open('./document2.pdf', 'rb'))
        ]
    }
    
    response = requests.post(url, headers=headers, data=data, files=files)
    return response.json()
```

## Testing

### cURL Commands

#### Create Loan Activity
```bash
curl -X POST http://localhost:3000/api/v1/loan-activities \
  -H "Authorization: Bearer <your-token>" \
  -F "rm_user_id=550e8400-e29b-41d4-a716-************" \
  -F "loan_client_id=550e8400-e29b-41d4-a716-************" \
  -F "purpose_id=550e8400-e29b-41d4-a716-************" \
  -F "loan_balance=50000.00" \
  -F "arrears_days=30" \
  -F "comment=Customer requested loan restructuring" \
  -F "interaction_type=call" \
  -F "call_status=answered" \
  -F "next_followup_date=2024-02-15T10:00:00.000Z" \
  -F "followup_status=pending" \
  -F "call_duration_minutes=15"
```

#### Get Loan Activity by ID
```bash
curl -X GET http://localhost:3000/api/v1/loan-activities/550e8400-e29b-41d4-a716-************ \
  -H "Authorization: Bearer <your-token>"
```

#### Get Activities by Client
```bash
curl -X GET "http://localhost:3000/api/v1/loan-activities/client/550e8400-e29b-41d4-a716-************?page=1&limit=10" \
  -H "Authorization: Bearer <your-token>"
```

#### Get Activities by RM User
```bash
curl -X GET "http://localhost:3000/api/v1/loan-activities/rm-user/550e8400-e29b-41d4-a716-************?page=1&limit=10" \
  -H "Authorization: Bearer <your-token>"
```

## Swagger Documentation
Interactive API documentation is available at:
```
http://localhost:3000/api/docs
```

## Notes
- All timestamps are returned in ISO 8601 format
- File uploads support multiple file types (PDF, images, documents)
- Maximum file size is 10MB per file
- All UUIDs must be valid version 4 UUIDs
- Pagination is 1-based (page 1 is the first page)
- The `updated_at` field is automatically managed by the database
