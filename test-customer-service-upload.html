<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Service Hitlist Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .download-link {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .download-link:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <h1>Customer Service Hitlist Upload Test</h1>
    
    <a href="/customer-service/hitlist-template" class="download-link">Download Template</a>
    
    <form id="uploadForm">
        <div class="form-group">
            <label for="file">Select Excel File:</label>
            <input type="file" id="file" name="file" accept=".xlsx" required>
        </div>
        
        <div class="form-group">
            <label for="type">Hitlist Type:</label>
            <input type="text" id="type" name="type" value="customer_service" required>
        </div>
        
        <button type="submit">Upload File</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const typeInput = document.getElementById('type');
            
            formData.append('file', fileInput.files[0]);
            formData.append('type', typeInput.value);
            
            try {
                const response = await fetch('/customer-service/hitlist-template', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `
                    <h3>Upload Result:</h3>
                    <p><strong>Type:</strong> ${result.type}</p>
                    <p><strong>Records Found:</strong> ${result.records.length}</p>
                    <h4>Records:</h4>
                    <pre>${JSON.stringify(result.records, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html> 