# Scheduled Tasks System

A comprehensive database-driven scheduled task system for NestJS with BullMQ integration, supporting both one-time and recurring tasks with full persistence and reliability.

## 🏗️ Architecture Overview

The scheduled task system consists of several components:

- **Database Layer**: PostgreSQL tables for task storage and execution tracking
- **API Layer**: REST endpoints for task management
- **Worker Service**: Processes tasks from the queue
- **Scheduler Service**: Manages recurring tasks and database polling
- **Queue System**: BullMQ for reliable task processing

## 📋 Database Schema

### ScheduledTask Table
- `id`: Unique task identifier
- `type`: Task type (e.g., 'send-email', 'generate-report')
- `name`: Human-readable task name
- `payload`: JSON data for task execution
- `run_at`: When to execute the task
- `interval_type`: For recurring tasks (MINUTES, HOURS, DAYS, WEEKS, MONTHS)
- `interval_value`: Interval value (e.g., 5 for every 5 hours)
- `cron_expression`: Alternative cron-based scheduling
- `status`: PENDING, RUNNING, COMPLETED, <PERSON><PERSON>ED, <PERSON><PERSON><PERSON><PERSON><PERSON>, PAUSED
- `priority`: Task priority (0-10, higher = more important)
- `max_attempts`: Maximum retry attempts
- `attempts`: Current attempt count

### ScheduledTaskExecution Table
- Tracks individual task executions
- Records start/end times, results, errors
- Links to worker instances
- Provides execution history and analytics

## 🚀 API Endpoints

### Create Scheduled Task
```http
POST /api/scheduled-tasks
Content-Type: application/json
Authorization: Bearer <token>

{
  "type": "send-email",
  "name": "Welcome Email",
  "payload": {
    "to": "<EMAIL>",
    "subject": "Welcome!",
    "template": "welcome",
    "context": { "name": "John" }
  },
  "runAt": "2024-01-01T10:00:00Z",
  "intervalType": "HOURS",
  "intervalValue": 24,
  "priority": 5
}
```

### List Scheduled Tasks
```http
GET /api/scheduled-tasks?status=PENDING&type=send-email&page=1&limit=50
```

### Get Task Details
```http
GET /api/scheduled-tasks/{id}
```

### Update Task
```http
PATCH /api/scheduled-tasks/{id}
{
  "runAt": "2024-01-02T10:00:00Z",
  "priority": 8
}
```

### Task Actions
```http
POST /api/scheduled-tasks/{id}/cancel
POST /api/scheduled-tasks/{id}/pause
POST /api/scheduled-tasks/{id}/resume
DELETE /api/scheduled-tasks/{id}
```

### Execution History
```http
GET /api/scheduled-tasks/{id}/executions
```

## 🔄 Task Types

### Built-in Task Types

#### Send Email
```json
{
  "type": "send-email",
  "payload": {
    "to": "<EMAIL>",
    "subject": "Email Subject",
    "template": "template-name",
    "context": { "key": "value" }
  }
}
```

#### Generate Report
```json
{
  "type": "generate-report",
  "payload": {
    "userId": "user-123",
    "reportType": "sales",
    "filters": { "dateFrom": "2024-01-01" },
    "format": "excel"
  }
}
```

#### Data Processing
```json
{
  "type": "data-processing",
  "payload": {
    "type": "excel-import",
    "fileUrl": "/uploads/data.xlsx",
    "userId": "user-123"
  }
}
```

#### Cleanup Tasks
```json
{
  "type": "cleanup",
  "payload": {
    "cleanupType": "old-logs"
  }
}
```

#### Notifications
```json
{
  "type": "notification",
  "payload": {
    "notificationType": "reminder",
    "message": "Don't forget to...",
    "userId": "user-123"
  }
}
```

## ⏰ Scheduling Options

### One-time Tasks
```json
{
  "runAt": "2024-01-01T10:00:00Z"
}
```

### Recurring Tasks with Intervals
```json
{
  "runAt": "2024-01-01T10:00:00Z",
  "intervalType": "HOURS",
  "intervalValue": 6
}
```

### Cron-based Scheduling
```json
{
  "runAt": "2024-01-01T10:00:00Z",
  "cronExpression": "0 9 * * 1-5"
}
```

## 🔧 Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/db

# Redis (for BullMQ)
REDIS_URL=redis://localhost:6379

# Scheduler
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=UTC

# Worker
WORKER_CONCURRENCY=5
WORKER_MAX_STALLED_COUNT=3
```

### Docker Compose
The system is fully integrated with the existing Docker setup:
- Worker containers process tasks
- Scheduler container manages recurring tasks
- All services connect to shared PostgreSQL and Redis

## 🏃‍♂️ How It Works

### Task Creation Flow
1. API receives task creation request
2. Task is validated and saved to database
3. Task status is set to PENDING
4. Next run time is calculated for recurring tasks

### Task Execution Flow
1. Scheduler polls database every minute for ready tasks
2. Ready tasks are pushed to BullMQ queue
3. Worker picks up task from queue
4. Worker creates execution record
5. Worker processes task based on type
6. Worker updates task and execution status
7. For recurring tasks, next run is scheduled

### Recurring Task Flow
1. Scheduler polls for completed recurring tasks
2. Next run time is calculated
3. Task is reset to PENDING status
4. Process repeats

## 🛡️ Reliability Features

### Persistence
- All tasks stored in PostgreSQL
- Survives container restarts
- Redis persistence for queue state

### Retry Logic
- Configurable max attempts per task
- Exponential backoff for retries
- Smart retry decisions based on error types

### Monitoring
- Execution history tracking
- Performance metrics
- Failed task analysis
- Worker health monitoring

### Error Handling
- Detailed error logging
- Execution duration tracking
- Worker identification
- Graceful failure handling

## 📊 Monitoring & Analytics

### Task Statistics
- Success/failure rates
- Average execution times
- Queue depth monitoring
- Worker performance metrics

### Execution History
- Complete audit trail
- Error analysis
- Performance trends
- Resource usage tracking

## 🔍 Troubleshooting

### Common Issues

1. **Tasks not executing**
   - Check scheduler service is running
   - Verify database connectivity
   - Check task status and run_at time

2. **High failure rates**
   - Review error messages in execution history
   - Check worker logs
   - Verify payload validation

3. **Performance issues**
   - Monitor queue depth
   - Check worker concurrency settings
   - Review database indexes

### Debugging Commands
```bash
# Check service status
docker compose ps

# View scheduler logs
docker compose logs -f nest-scheduler

# View worker logs
docker compose logs -f nest-worker

# Check queue stats
curl http://localhost:3000/api/queue/stats
```

## 🚀 Usage Examples

### Schedule Daily Reports
```typescript
await scheduledTaskService.create({
  type: 'generate-report',
  name: 'Daily Sales Report',
  payload: {
    reportType: 'sales',
    userId: 'admin-123',
    format: 'excel'
  },
  runAt: new Date('2024-01-01T09:00:00Z'),
  intervalType: ScheduledTaskInterval.DAYS,
  intervalValue: 1,
  priority: 7
});
```

### Schedule Email Reminders
```typescript
await scheduledTaskService.create({
  type: 'send-email',
  name: 'Weekly Newsletter',
  payload: {
    to: '<EMAIL>',
    subject: 'Weekly Update',
    template: 'newsletter'
  },
  cronExpression: '0 9 * * 1', // Every Monday at 9 AM
  priority: 5
});
```

### One-time Data Processing
```typescript
await scheduledTaskService.create({
  type: 'data-processing',
  name: 'Import Customer Data',
  payload: {
    type: 'excel-import',
    fileUrl: '/uploads/customers.xlsx',
    userId: 'admin-123'
  },
  runAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes from now
  maxAttempts: 1,
  priority: 9
});
```

## 🔮 Future Enhancements

- Web UI for task management
- Advanced cron expression support
- Task dependencies and workflows
- Distributed task execution
- Real-time notifications
- Advanced analytics dashboard
