$response = Invoke-WebRequest -Uri 'http://localhost:3000/api/v1/leads/rbac-analytics' -Headers @{'Authorization'='Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************.bJI5C0e5T5rM8vRRsAhXAjgr0KAecVjCGdEXA03u6EU'; 'Content-Type'='application/json'} -Method GET
$response.Content | Out-File -FilePath "rbac-analytics-response.json" -Encoding UTF8
Write-Host "Response saved to rbac-analytics-response.json"
Write-Host "Status Code: $($response.StatusCode)"
Write-Host "Content:"
$response.Content
