# 🚀 KB Tracker - Scheduled Tasks System Guide

## 📋 Overview

The KB Tracker backend features a comprehensive scheduled tasks system that allows you to create, schedule, and execute background tasks with full database persistence, queue management, and flexible scheduling capabilities.

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │     Redis       │    │   Application   │
│  (PostgreSQL)   │    │   (Queue)       │    │    Services     │
│                 │    │                 │    │                 │
│ ScheduledTask   │    │ BullMQ Queues   │    │ BackgroundTask  │
│ TaskExecution   │    │                 │    │    Service      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduler     │───▶│     Worker      │    │  Task Handlers  │
│   Container     │    │   Container     │    │                 │
│                 │    │                 │    │ ConsoleLog      │
│ Polls Database  │    │ Processes Jobs  │    │ EmailSender     │
│ Enqueues Tasks  │    │ Executes Tasks  │    │ DataProcessor   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔧 Key Components

1. **🗄️ Database Layer**: PostgreSQL with Prisma ORM for task persistence
2. **🔄 Queue System**: BullMQ with Redis for job processing  
3. **⏰ Scheduler Container**: Polls database every minute and enqueues due tasks
4. **⚡ Worker Container**: Processes queued tasks using registered handlers
5. **🎯 Task Handlers**: Modular handlers for different task types
6. **🚀 Background Task Service**: API for triggering tasks from anywhere in code

## 📁 File Structure

```
src/
├── scheduled-tasks/
│   ├── background-task.service.ts      # Main API for creating tasks
│   ├── scheduled-task.service.ts       # Database operations
│   ├── scheduled-task.controller.ts    # REST API endpoints
│   ├── scheduled-task.module.ts        # Module configuration
│   ├── task-handlers/                  # Task handler implementations
│   │   ├── task-handler.interface.ts   # Handler interface
│   │   ├── task-handler.registry.ts    # Type-to-handler mapping
│   │   ├── console-log.handler.ts      # Console logging handler
│   │   └── daily-nairobi-task.handler.ts # Timezone-aware handler
│   └── dto/                           # Data transfer objects
│       ├── create-scheduled-task.dto.ts
│       ├── update-scheduled-task.dto.ts
│       └── query-scheduled-tasks.dto.ts
├── scheduler/
│   ├── scheduler.service.ts           # Database polling service
│   ├── scheduler.module.ts            # Scheduler module
│   └── scheduler.ts                   # Scheduler entry point
├── queue/
│   ├── queue.service.ts               # BullMQ queue management
│   ├── queue.module.ts                # Queue module
│   └── processors/
│       └── scheduled-task.processor.ts # Task execution processor
└── examples/
    ├── background-task-example.service.ts # Usage examples
    └── examples.controller.ts         # Example API endpoints
```

## 🚀 How to Create Background Tasks

### 1. **From Any Service in Your Code**

The `BackgroundTaskService` provides a clean API for creating tasks from anywhere in your application:

```typescript
import { Injectable } from '@nestjs/common';
import { BackgroundTaskService } from '../scheduled-tasks/background-task.service';

@Injectable()
export class YourService {
  constructor(
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  async someMethod() {
    // 🔥 Immediate task execution
    await this.backgroundTaskService.runNow('console-log', {
      message: 'Task executed immediately!',
      level: 'info'
    });

    // ⏰ Schedule task for specific time
    const futureDate = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
    await this.backgroundTaskService.schedule('console-log', {
      message: 'This will run in 1 hour',
      level: 'info'
    }, futureDate);

    // 🔄 Recurring task with cron expression
    await this.backgroundTaskService.scheduleRecurring('console-log', {
      message: 'Daily task at 1 AM',
      level: 'info'
    }, '0 1 * * *'); // Every day at 1:00 AM

    // 📅 Interval-based recurring task
    await this.backgroundTaskService.scheduleInterval('console-log', {
      message: 'Every 30 minutes',
      level: 'info'
    }, 'MINUTES', 30);
  }
}
```

### 2. **Available Methods**

#### `runNow(taskType, payload, options?)`
Executes a task immediately (within 1 second).

#### `schedule(taskType, payload, runAt, options?)`
Schedules a task to run at a specific date/time.

#### `scheduleRecurring(taskType, payload, cronExpression, options?)`
Creates a recurring task using cron expressions.

#### `scheduleInterval(taskType, payload, intervalType, intervalValue, options?)`
Creates a recurring task with simple intervals (MINUTES, HOURS, DAYS, WEEKS, MONTHS).

## ⏰ Scheduling Examples

### **Run Task at Specific Time**
```typescript
// Run tomorrow at 9:00 AM
const tomorrow9AM = new Date();
tomorrow9AM.setDate(tomorrow9AM.getDate() + 1);
tomorrow9AM.setHours(9, 0, 0, 0);

await backgroundTaskService.schedule('send-email', {
  to: '<EMAIL>',
  subject: 'Daily Report',
  template: 'daily-report'
}, tomorrow9AM);
```

### **Daily Task at 1 AM**
```typescript
// Every day at 1:00 AM (Africa/Nairobi timezone)
await backgroundTaskService.scheduleRecurring('daily-cleanup', {
  action: 'cleanup-temp-files',
  maxAge: '7d'
}, '0 1 * * *');
```

### **Weekly Task**
```typescript
// Every Monday at 8:00 AM
await backgroundTaskService.scheduleRecurring('weekly-report', {
  reportType: 'weekly-summary',
  recipients: ['<EMAIL>']
}, '0 8 * * 1');
```

### **Interval-Based Tasks**
```typescript
// Every 15 minutes
await backgroundTaskService.scheduleInterval('health-check', {
  service: 'api',
  endpoint: '/health'
}, 'MINUTES', 15);

// Every 6 hours
await backgroundTaskService.scheduleInterval('data-sync', {
  source: 'external-api',
  destination: 'local-db'
}, 'HOURS', 6);
```

## 🎯 Task Types & Handler Mapping

### **How Types Map to Functions**

The system uses a `TaskHandlerRegistry` to map task types to handler classes:

```typescript
// In src/scheduled-tasks/task-handlers/task-handler.registry.ts
@Injectable()
export class TaskHandlerRegistry {
  private handlers = new Map<string, TaskHandler>();

  constructor(
    private readonly consoleLogHandler: ConsoleLogHandler,
    private readonly dailyNairobiTaskHandler: DailyNairobiTaskHandler,
    // Add your new handlers here
  ) {
    // Register type-to-handler mappings
    this.handlers.set('console-log', this.consoleLogHandler);
    this.handlers.set('daily-nairobi-task', this.dailyNairobiTaskHandler);
    // Register your new types here
  }
}
```

### **Built-in Task Types**

#### 1. **`console-log`** - Console Logging
```typescript
// Handler: ConsoleLogHandler
// File: src/scheduled-tasks/task-handlers/console-log.handler.ts
await backgroundTaskService.runNow('console-log', {
  message: 'Hello World!',
  level: 'info' // 'info', 'warn', 'error', 'debug'
});
```

#### 2. **`daily-nairobi-task`** - Timezone-aware Task
```typescript
// Handler: DailyNairobiTaskHandler  
// File: src/scheduled-tasks/task-handlers/daily-nairobi-task.handler.ts
await backgroundTaskService.scheduleRecurring('daily-nairobi-task', {
  message: 'Daily task in Nairobi timezone',
  timezone: 'Africa/Nairobi'
}, '0 1 * * *'); // 1 AM Nairobi time
```

## ➕ Adding New Task Types

### **Step 1: Create Handler Class**

```typescript
// src/scheduled-tasks/task-handlers/email-sender.handler.ts
import { Injectable, Logger } from '@nestjs/common';
import { TaskHandler } from './task-handler.interface';

export interface EmailPayload {
  to: string;
  subject: string;
  body: string;
  template?: string;
}

@Injectable()
export class EmailSenderHandler implements TaskHandler {
  private readonly logger = new Logger(EmailSenderHandler.name);

  async handle(payload: EmailPayload): Promise<void> {
    this.logger.log(`Sending email to ${payload.to}: ${payload.subject}`);
    
    // Your email sending logic here
    // await this.emailService.send(payload);
    
    this.logger.log(`Email sent successfully to ${payload.to}`);
  }
}
```

### **Step 2: Register Handler**

```typescript
// src/scheduled-tasks/task-handlers/task-handler.registry.ts
import { EmailSenderHandler } from './email-sender.handler';

@Injectable()
export class TaskHandlerRegistry {
  constructor(
    private readonly consoleLogHandler: ConsoleLogHandler,
    private readonly dailyNairobiTaskHandler: DailyNairobiTaskHandler,
    private readonly emailSenderHandler: EmailSenderHandler, // Add this
  ) {
    this.handlers.set('console-log', this.consoleLogHandler);
    this.handlers.set('daily-nairobi-task', this.dailyNairobiTaskHandler);
    this.handlers.set('send-email', this.emailSenderHandler); // Add this
  }
}
```

### **Step 3: Add to Module**

```typescript
// src/scheduled-tasks/scheduled-task.module.ts
import { EmailSenderHandler } from './task-handlers/email-sender.handler';

@Module({
  providers: [
    // ... existing providers
    EmailSenderHandler, // Add this
  ],
})
export class ScheduledTaskModule {}
```

### **Step 4: Use Your New Task Type**

```typescript
// Now you can use it anywhere in your code
await backgroundTaskService.runNow('send-email', {
  to: '<EMAIL>',
  subject: 'Welcome!',
  body: 'Welcome to our platform!',
  template: 'welcome'
});
```

## 🌍 Timezone Configuration

The entire system is configured to use **Africa/Nairobi** timezone:

- All containers have `TZ=Africa/Nairobi` environment variable
- Scheduler service uses `SCHEDULER_TIMEZONE=Africa/Nairobi`
- Cron expressions are evaluated in Nairobi timezone
- Database timestamps are stored in UTC but displayed in local timezone

## 🐳 Container Architecture

### **Separate Containers for Scalability**

1. **API Container** (`nest-api`)
   - Handles HTTP requests
   - Creates and manages tasks
   - Provides REST API endpoints

2. **Scheduler Container** (`nest-scheduler`)
   - Runs `npm run start:scheduler`
   - Polls database every minute
   - Enqueues due tasks to Redis

3. **Worker Container** (`nest-worker`)
   - Runs `npm run start:worker`
   - Processes queued tasks
   - Executes task handlers

All containers use the same codebase but different entry points, ensuring consistency and easy deployment.

## 📊 Monitoring & Management

### **Task Status Tracking**

Tasks go through these states:
- `PENDING` - Waiting to be executed
- `RUNNING` - Currently being processed
- `COMPLETED` - Successfully finished
- `FAILED` - Execution failed
- `CANCELLED` - Manually cancelled
- `PAUSED` - Temporarily paused

### **REST API Endpoints**

```bash
# Get all tasks
GET /api/v1/scheduled-tasks

# Get specific task
GET /api/v1/scheduled-tasks/{id}

# Create task
POST /api/v1/scheduled-tasks

# Update task
PATCH /api/v1/scheduled-tasks/{id}

# Cancel task
DELETE /api/v1/scheduled-tasks/{id}
```

### **Example API Usage**

```bash
# Create immediate task
curl -X POST http://localhost:3000/api/v1/examples/background-tasks/immediate-console-log \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello from API!"}'

# Create scheduled task
curl -X POST http://localhost:3000/api/v1/examples/background-tasks/scheduled-console-log \
  -H "Content-Type: application/json" \
  -d '{"message": "Future task", "delayMinutes": 60}'
```

## 🔧 Configuration

### **Environment Variables**

```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/kb_tracker

# Redis
REDIS_URL=redis://localhost:6379

# Timezone
TZ=Africa/Nairobi
SCHEDULER_TIMEZONE=Africa/Nairobi

# Scheduler
SCHEDULER_ENABLED=true
```

### **Docker Compose**

The system is fully containerized with proper service separation:

```yaml
services:
  nest-api:
    # Main API service
  nest-scheduler:
    # Database polling service
  nest-worker:
    # Task execution service
  postgres:
    # Database
  redis:
    # Queue storage
```

## 🚀 Getting Started

1. **Inject BackgroundTaskService** in your service
2. **Choose appropriate method** (runNow, schedule, scheduleRecurring, scheduleInterval)
3. **Specify task type** and payload
4. **Task gets stored** in database
5. **Scheduler polls** and enqueues due tasks
6. **Worker processes** tasks using registered handlers

The system is production-ready and handles failures, retries, and monitoring automatically!

## 💡 Advanced Usage Examples

### **Real-World Service Integration**

```typescript
// src/users/users.service.ts
@Injectable()
export class UsersService {
  constructor(
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  async createUser(userData: CreateUserDto) {
    // Create user in database
    const user = await this.prisma.user.create({ data: userData });

    // Send welcome email immediately
    await this.backgroundTaskService.runNow('send-email', {
      to: user.email,
      subject: 'Welcome to KB Tracker!',
      template: 'welcome',
      data: { name: user.name }
    });

    // Schedule follow-up email for 3 days later
    const followUpDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000);
    await this.backgroundTaskService.schedule('send-email', {
      to: user.email,
      subject: 'How are you finding KB Tracker?',
      template: 'follow-up',
      data: { name: user.name }
    }, followUpDate);

    // Set up weekly digest emails
    await this.backgroundTaskService.scheduleRecurring('send-email', {
      to: user.email,
      subject: 'Your Weekly Digest',
      template: 'weekly-digest',
      data: { userId: user.id }
    }, '0 9 * * 1'); // Every Monday at 9 AM

    return user;
  }
}
```

### **Complex Task Orchestration**

```typescript
// src/reports/reports.service.ts
@Injectable()
export class ReportsService {
  constructor(
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  async generateMonthlyReport() {
    // Step 1: Data collection (immediate)
    const dataTask = await this.backgroundTaskService.runNow('collect-data', {
      reportType: 'monthly',
      month: new Date().getMonth(),
      year: new Date().getFullYear()
    });

    // Step 2: Report generation (5 minutes later to ensure data is ready)
    const generateDate = new Date(Date.now() + 5 * 60 * 1000);
    const reportTask = await this.backgroundTaskService.schedule('generate-report', {
      dataTaskId: dataTask.id,
      format: 'pdf',
      template: 'monthly-template'
    }, generateDate);

    // Step 3: Email distribution (10 minutes later)
    const emailDate = new Date(Date.now() + 10 * 60 * 1000);
    await this.backgroundTaskService.schedule('distribute-report', {
      reportTaskId: reportTask.id,
      recipients: ['<EMAIL>', '<EMAIL>']
    }, emailDate);

    return { message: 'Monthly report generation initiated' };
  }
}
```

## 🔄 Task Management & Monitoring

### **Task Cancellation**

```typescript
// Cancel a specific task
const task = await backgroundTaskService.schedule('long-running-task', {
  data: 'some data'
}, futureDate);

// Later, cancel if needed
await backgroundTaskService.cancelTask(task.id);
```

### **Task Status Checking**

```typescript
// Check task status
const taskStatus = await backgroundTaskService.getTaskStatus(taskId);
console.log(`Task ${taskId} is ${taskStatus.status}`);

// Get all tasks
const allTasksResult = await backgroundTaskService.getTasks();
console.log(`Found ${allTasksResult.tasks.length} tasks`);
console.log('Pagination:', allTasksResult.pagination);
```

### **Error Handling & Retries**

```typescript
// Task with custom retry settings
await backgroundTaskService.runNow('risky-task', {
  apiUrl: 'https://external-api.com/data'
}, {
  maxAttempts: 5,  // Retry up to 5 times
  priority: 10     // High priority
});
```

## 🎯 Best Practices

### **1. Task Payload Design**
```typescript
// ✅ Good: Specific, typed payloads
interface EmailTaskPayload {
  to: string;
  subject: string;
  template: string;
  data?: Record<string, any>;
}

// ❌ Avoid: Generic, untyped payloads
const payload = { stuff: 'anything' };
```

### **2. Error Handling in Handlers**
```typescript
@Injectable()
export class EmailSenderHandler implements TaskHandler {
  async handle(payload: EmailPayload): Promise<void> {
    try {
      await this.emailService.send(payload);
      this.logger.log(`Email sent to ${payload.to}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${payload.to}:`, error);
      throw error; // Re-throw to trigger retry mechanism
    }
  }
}
```

### **3. Idempotent Tasks**
```typescript
// Design tasks to be safely retryable
@Injectable()
export class DataSyncHandler implements TaskHandler {
  async handle(payload: { recordId: string }): Promise<void> {
    // Check if already processed
    const existing = await this.findExistingSync(payload.recordId);
    if (existing?.status === 'completed') {
      this.logger.log(`Record ${payload.recordId} already synced`);
      return;
    }

    // Perform sync operation
    await this.syncRecord(payload.recordId);
  }
}
```

## 🔧 Troubleshooting

### **Common Issues**

1. **Tasks not executing**: Check scheduler container logs
2. **Handler not found**: Verify handler is registered in TaskHandlerRegistry
3. **Database connection**: Ensure PostgreSQL is accessible
4. **Queue issues**: Check Redis connection

### **Debugging Commands**

```bash
# Check container status
docker compose ps

# View scheduler logs
docker compose logs nest-scheduler

# View worker logs
docker compose logs nest-worker

# Check database tasks
docker compose exec postgres psql -U postgres -d kb_tracker -c "SELECT * FROM scheduled_tasks ORDER BY created_at DESC LIMIT 10;"
```

## 📈 Performance Considerations

- **Scheduler polls every minute** - adjust if needed for higher frequency
- **Worker concurrency** can be configured via environment variables
- **Database indexing** on `run_at` and `status` columns for efficient queries
- **Queue priorities** help manage task execution order
- **Task cleanup** removes old completed tasks automatically

## 🔐 Security Notes

- Task payloads are stored in database - avoid sensitive data
- Use environment variables for API keys and secrets
- Implement proper authentication for task management endpoints
- Consider encryption for sensitive task data

---

**🎉 You now have a complete understanding of the KB Tracker scheduled tasks system! Start creating background tasks and automating your workflows.**
