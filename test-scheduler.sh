#!/bin/bash

# Test script to verify scheduler functionality
# Usage: ./test-scheduler.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}🔍 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to detect docker compose command
detect_docker_compose() {
    if command -v "docker" >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "docker compose"
    elif command -v "docker-compose" >/dev/null 2>&1; then
        echo "docker-compose"
    else
        print_error "Neither 'docker compose' nor 'docker-compose' is available"
        exit 1
    fi
}

# Test scheduler container
test_scheduler() {
    local compose_cmd=$(detect_docker_compose)
    
    print_status "Testing scheduler container..."
    
    # Check if scheduler container is running
    if $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml ps nest-scheduler | grep -q "Up"; then
        print_success "Scheduler container is running"
        
        # Check scheduler logs for activity
        print_status "Checking scheduler logs (last 20 lines)..."
        echo "----------------------------------------"
        $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml logs --tail=20 nest-scheduler
        echo "----------------------------------------"
        
        # Check if scheduler is actually scheduling jobs
        print_status "Checking for scheduler activity in the last 2 minutes..."
        recent_logs=$($compose_cmd -f docker-compose.yml -f docker-compose.dev.yml logs --since=2m nest-scheduler 2>/dev/null || echo "")
        
        if echo "$recent_logs" | grep -q -E "(Starting|Scheduler|Job|Task|Cron)"; then
            print_success "Scheduler appears to be active"
        else
            print_warning "No recent scheduler activity detected"
            print_status "This might be normal if no scheduled tasks are due"
        fi
        
        # Check database connection from scheduler
        print_status "Testing database connectivity from scheduler..."
        $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml exec nest-scheduler sh -c "
            echo 'Testing database connection...'
            node -e \"
                const { PrismaClient } = require('@prisma/client');
                const prisma = new PrismaClient();
                prisma.\$connect()
                  .then(() => {
                    console.log('✅ Database connection successful');
                    return prisma.\$disconnect();
                  })
                  .catch((err) => {
                    console.error('❌ Database connection failed:', err.message);
                    process.exit(1);
                  });
            \"
        " 2>/dev/null || print_warning "Could not test database connection (container might not have node available)"
        
    else
        print_error "Scheduler container is not running"
        print_status "Container status:"
        $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml ps nest-scheduler
        return 1
    fi
}

# Test worker container
test_worker() {
    local compose_cmd=$(detect_docker_compose)
    
    print_status "Testing worker container..."
    
    # Check if worker container is running
    if $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml ps nest-worker | grep -q "Up"; then
        print_success "Worker container is running"
        
        # Check worker logs for activity
        print_status "Checking worker logs (last 10 lines)..."
        echo "----------------------------------------"
        $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml logs --tail=10 nest-worker
        echo "----------------------------------------"
        
    else
        print_error "Worker container is not running"
        print_status "Container status:"
        $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml ps nest-worker
        return 1
    fi
}

# Main test execution
echo "KB Tracker Backend - Scheduler & Worker Test"
echo "============================================="
echo ""

# Test scheduler
test_scheduler
echo ""

# Test worker
test_worker
echo ""

print_success "Scheduler and Worker tests completed!"
print_status "If you see any issues, check the container logs with:"
echo "  ./docker-dev.sh logs"
