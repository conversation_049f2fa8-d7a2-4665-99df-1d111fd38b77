# Memory Optimization Guide for KB Tracker Backend

This document outlines the memory optimization changes made to resolve JavaScript heap out of memory errors in the scheduler and worker processes.

## Problem Analysis

The original issue was caused by:
1. **Insufficient heap size**: Node.js default heap limit (~1.4GB) was too small
2. **Memory leaks**: Frequent database queries and job processing without proper cleanup
3. **Large data processing**: Excel imports and bulk operations consuming excessive memory
4. **Queue accumulation**: BullMQ jobs accumulating without regular cleanup

## Solutions Implemented

### 1. Increased Node.js Heap Size

**Files Modified**: `package.json`

Updated scripts to allocate 2GB heap size and enable garbage collection:
```json
{
  "start:worker": "node --max-old-space-size=2048 dist/src/worker.js",
  "start:worker:dev": "node --max-old-space-size=2048 -r ts-node/register src/worker.ts",
  "start:scheduler": "node --max-old-space-size=2048 dist/src/scheduler.js",
  "start:scheduler:dev": "node --max-old-space-size=2048 -r ts-node/register src/scheduler.ts"
}
```

### 2. Memory Monitoring System

**New File**: `src/common/utils/memory-monitor.ts`

Features:
- Real-time memory usage monitoring
- Automatic garbage collection when thresholds are exceeded
- Memory statistics logging
- Emergency cleanup triggers

### 3. Queue Cleanup Service

**New File**: `src/queue/queue-cleanup.service.ts`

Features:
- Automatic cleanup of completed/failed jobs
- Configurable retention policies
- Emergency cleanup for critical memory situations
- Queue statistics monitoring

### 4. Batch Processing Optimizations

**Files Modified**: 
- `src/queue/processors/data.processor.ts`
- `src/scheduler/scheduler.service.ts`

Improvements:
- Reduced batch sizes (100 → 50 for data processing)
- Added garbage collection after each batch
- Implemented processing delays to prevent system overload
- Better error handling and memory management

### 5. Docker Memory Limits

**New File**: `docker-compose.memory.yml`

Provides:
- Container memory limits (3GB limit, 1GB reservation)
- Redis memory optimization
- Proper restart policies

## Environment Variables

Add these to your `.env` file for optimal configuration:

```env
# Memory and Performance Settings
NODE_OPTIONS=--max-old-space-size=2048 --expose-gc
WORKER_CONCURRENCY=3
WORKER_MAX_STALLED_COUNT=3
WORKER_STALLED_INTERVAL=30000

# Queue Cleanup Settings
QUEUE_CLEANUP_MAX_AGE_HOURS=24
QUEUE_CLEANUP_MAX_JOBS=100

# Redis Settings (if using external Redis)
QUEUE_REDIS_HOST=redis
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1
```

## Deployment Instructions

### Option 1: Using Docker Compose Override

```bash
# Use the memory-optimized configuration
docker-compose -f docker-compose.yml -f docker-compose.memory.yml up -d
```

### Option 2: Manual Environment Setup

1. Update your existing docker-compose.yml:
```yaml
services:
  kb-tracker-scheduler:
    environment:
      - NODE_OPTIONS=--max-old-space-size=2048 --expose-gc
    deploy:
      resources:
        limits:
          memory: 3G
```

2. Restart the services:
```bash
docker-compose down
docker-compose up -d
```

## Monitoring and Maintenance

### Memory Monitoring

The system now automatically:
- Monitors memory usage every 30 seconds
- Logs warnings when usage exceeds 1.5GB
- Forces garbage collection when needed
- Performs emergency cleanup at 1.8GB usage

### Queue Cleanup

Automatic cleanup runs:
- Every 2 hours for regular maintenance
- Every 10 minutes for memory checks
- On-demand during high memory usage

### Manual Monitoring Commands

```bash
# Check container memory usage
docker stats

# View application logs
docker-compose logs -f kb-tracker-scheduler
docker-compose logs -f nest-worker-1

# Check queue statistics (if you add an endpoint)
curl http://localhost:3000/health/queues
```

## Performance Tuning

### For High-Volume Environments

1. **Increase worker concurrency** (but monitor memory):
```env
WORKER_CONCURRENCY=5
```

2. **Adjust cleanup frequency**:
```env
QUEUE_CLEANUP_MAX_AGE_HOURS=12  # More frequent cleanup
QUEUE_CLEANUP_MAX_JOBS=50       # Keep fewer jobs
```

3. **Scale horizontally**:
```yaml
# Add more worker instances
nest-worker-2:
  extends:
    service: nest-worker-1
  container_name: nest-worker-2
```

### For Low-Memory Environments

1. **Reduce heap size**:
```env
NODE_OPTIONS=--max-old-space-size=1536 --expose-gc
```

2. **Lower batch sizes** in data processor:
```typescript
const batchSize = 25; // Reduce from 50
```

## Troubleshooting

### Still Getting Memory Errors?

1. **Check actual memory usage**:
```bash
docker exec -it kb-tracker-scheduler node -e "console.log(process.memoryUsage())"
```

2. **Increase heap size further**:
```env
NODE_OPTIONS=--max-old-space-size=3072 --expose-gc
```

3. **Enable more aggressive cleanup**:
```env
QUEUE_CLEANUP_MAX_AGE_HOURS=6
QUEUE_CLEANUP_MAX_JOBS=25
```

### Performance Issues?

1. **Monitor queue backlogs**
2. **Check database connection pooling**
3. **Consider Redis memory limits**
4. **Review scheduled task frequency**

## Next Steps

1. Deploy the changes using one of the deployment options above
2. Monitor memory usage for 24-48 hours
3. Adjust configuration based on your specific workload
4. Consider implementing additional monitoring dashboards
5. Set up alerts for critical memory usage

## Files Changed Summary

- `package.json` - Updated Node.js memory settings
- `src/scheduler.ts` - Added memory monitoring
- `src/worker.ts` - Added memory monitoring  
- `src/scheduler/scheduler.service.ts` - Added cleanup tasks and batch processing
- `src/queue/processors/data.processor.ts` - Optimized batch processing
- `src/queue/queue.module.ts` - Added cleanup service
- `docker-compose.memory.yml` - Docker memory configuration
- `src/common/utils/memory-monitor.ts` - New memory monitoring utility
- `src/queue/queue-cleanup.service.ts` - New queue cleanup service
