# My Permissions API Documentation

## Overview

This document describes the new API endpoint for retrieving the permissions of the currently logged-in user.

## Endpoint

### Get Current User Permissions

**GET** `/api/v1/permissions/my-permissions`

Returns all permissions assigned to the currently authenticated user through their role.

#### Authentication

- **Required**: Yes
- **Type**: <PERSON><PERSON> (JWT)
- **Header**: `Authorization: Bearer <your-jwt-token>`

#### Request

No request body or query parameters required.

#### Response

**Success Response (200 OK)**

```json
[
  {
    "id": "users.create",
    "name": "Create Users",
    "description": "Allows creating new user accounts in the system"
  },
  {
    "id": "users.read",
    "name": "Read Users", 
    "description": "Allows viewing user information"
  },
  {
    "id": "leads.manage",
    "name": "Manage Leads",
    "description": "Allows full management of leads"
  }
]
```

**Error Responses**

- **401 Unauthorized**: Invalid or missing authentication token
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```

- **404 Not Found**: User not found
  ```json
  {
    "statusCode": 404,
    "message": "User not found"
  }
  ```

- **400 Bad Request**: Database connection failed
  ```json
  {
    "statusCode": 400,
    "message": "Database connection failed. Please try again later."
  }
  ```

#### Response Schema

Each permission object contains:

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique permission identifier (e.g., "users.create") |
| `name` | string | Human-readable permission name |
| `description` | string \| null | Optional description of what the permission allows |

#### Notes

- Permissions are returned sorted alphabetically by name
- If the user has no role assigned, an empty array is returned
- The endpoint only returns permissions directly assigned to the user's role
- Permissions are automatically extracted from the user's role during authentication

## Usage Examples

### JavaScript/Fetch

```javascript
const response = await fetch('/api/v1/permissions/my-permissions', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${yourJwtToken}`,
    'Content-Type': 'application/json'
  }
});

if (response.ok) {
  const permissions = await response.json();
  console.log('User permissions:', permissions);
} else {
  console.error('Failed to fetch permissions:', response.status);
}
```

### cURL

```bash
curl -X GET \
  http://localhost:3000/api/v1/permissions/my-permissions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Axios

```javascript
import axios from 'axios';

try {
  const response = await axios.get('/api/v1/permissions/my-permissions', {
    headers: {
      'Authorization': `Bearer ${yourJwtToken}`
    }
  });
  
  console.log('User permissions:', response.data);
} catch (error) {
  console.error('Error fetching permissions:', error.response?.data);
}
```

## Integration with Frontend

This endpoint is particularly useful for:

1. **Role-based UI rendering**: Show/hide UI elements based on user permissions
2. **Route protection**: Determine which routes a user can access
3. **Feature toggles**: Enable/disable features based on permissions
4. **Permission checks**: Validate user actions before making API calls

### Example Permission Check

```javascript
// Fetch user permissions on app initialization
const userPermissions = await fetchUserPermissions();

// Check if user can create users
const canCreateUsers = userPermissions.some(p => p.id === 'users.create');

// Conditionally render create user button
if (canCreateUsers) {
  renderCreateUserButton();
}
```

## Implementation Details

- **Service Method**: `PermissionsService.getUserPermissions(userId: string)`
- **Controller Method**: `PermissionsController.getMyPermissions(@Request() req)`
- **Database Query**: Joins User → Role → RolePermission → Permission
- **Caching**: No caching implemented (permissions are fetched fresh each time)
- **Performance**: Single database query with joins for optimal performance

## Related Endpoints

- `GET /api/v1/permissions` - List all permissions (with pagination)
- `GET /api/v1/permissions/all` - Get all permissions without pagination
- `GET /api/v1/permissions/:id` - Get specific permission details
- `GET /api/v1/roles/:id` - Get role details including permissions

## Security Considerations

- Endpoint requires valid JWT authentication
- Only returns permissions for the authenticated user
- No sensitive information is exposed beyond permission names and descriptions
- User ID is extracted from the JWT token, preventing unauthorized access to other users' permissions
