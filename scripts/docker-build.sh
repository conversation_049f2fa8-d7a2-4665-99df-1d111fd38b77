#!/bin/bash

# KB Tracker Backend - Universal Docker Build Script
# This script works with both docker compose and docker-compose commands

set -e

# Colors for output
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m' # No Color

# Function to detect docker compose command
detect_docker_compose() {
    if command -v "docker" >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "docker compose"
    elif command -v "docker-compose" >/dev/null 2>&1; then
        echo "docker-compose"
    else
        echo -e "${RED}Error: Neither 'docker compose' nor 'docker-compose' is available${NC}"
        exit 1
    fi
}

# Function to build images using standard docker build
build_with_docker() {
    local target=$1
    local tag=$2
    
    echo -e "${BLUE}Building image with target: $target, tag: $tag${NC}"
    
    if [ "$target" = "dependencies" ]; then
        docker build --target dependencies -t "$tag" .
    elif [ "$target" = "production" ]; then
        docker build --target production -t "$tag" .
    else
        docker build -t "$tag" .
    fi
}

# Main function
main() {
    local mode=${1:-production}
    local compose_cmd
    
    echo -e "${BLUE}KB Tracker Backend - Universal Build Script${NC}"
    echo -e "${YELLOW}Mode: $mode${NC}"
    
    # Detect docker compose command
    compose_cmd=$(detect_docker_compose)
    echo -e "${GREEN}Using: $compose_cmd${NC}"
    
    case $mode in
        "dev"|"development")
            echo -e "${BLUE}Building development images...${NC}"
            
            # Try compose build first, fallback to docker build
            if ! $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml build; then
                echo -e "${YELLOW}Compose build failed, trying direct docker build...${NC}"
                build_with_docker "dependencies" "kb-tracker-backend:dev"
            fi
            ;;
            
        "prod"|"production")
            echo -e "${BLUE}Building production images...${NC}"
            
            # Try compose build first, fallback to docker build
            if ! $compose_cmd build; then
                echo -e "${YELLOW}Compose build failed, trying direct docker build...${NC}"
                build_with_docker "production" "kb-tracker-backend:prod"
            fi
            ;;
            
        "both")
            echo -e "${BLUE}Building both development and production images...${NC}"
            
            # Build development
            if ! $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml build; then
                echo -e "${YELLOW}Dev compose build failed, trying direct docker build...${NC}"
                build_with_docker "dependencies" "kb-tracker-backend:dev"
            fi
            
            # Build production
            if ! $compose_cmd build; then
                echo -e "${YELLOW}Prod compose build failed, trying direct docker build...${NC}"
                build_with_docker "production" "kb-tracker-backend:prod"
            fi
            ;;
            
        *)
            echo -e "${RED}Invalid mode: $mode${NC}"
            echo -e "${YELLOW}Usage: $0 [dev|prod|both]${NC}"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}Build completed successfully!${NC}"
    
    # Show built images
    echo -e "${BLUE}Built images:${NC}"
    docker images | grep kb-tracker-backend || echo "No kb-tracker-backend images found"
}

# Run main function with all arguments
main "$@"
