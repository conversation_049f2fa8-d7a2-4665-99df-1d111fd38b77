#!/bin/bash

# KB Tracker Backend - Universal Docker Run Script
# This script works with both docker compose and docker-compose commands

set -e

# Colors for output
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m' # No Color

# Function to detect docker compose command
detect_docker_compose() {
    if command -v "docker" >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "docker compose"
    elif command -v "docker-compose" >/dev/null 2>&1; then
        echo "docker-compose"
    else
        echo -e "${RED}Error: Neither 'docker compose' nor 'docker-compose' is available${NC}"
        exit 1
    fi
}

# Function to check if images exist
check_images() {
    local mode=$1
    local required_image
    
    if [ "$mode" = "dev" ] || [ "$mode" = "development" ]; then
        required_image="kb-tracker-backend:dev"
    else
        required_image="kb-tracker-backend:prod"
    fi
    
    if ! docker images | grep -q "$required_image"; then
        echo -e "${YELLOW}Warning: Required image $required_image not found${NC}"
        echo -e "${BLUE}Building image first...${NC}"
        ./scripts/docker-build.sh "$mode"
    fi
}

# Function to wait for services to be healthy
wait_for_services() {
    local compose_cmd=$1
    local max_wait=60
    local wait_time=0
    
    echo -e "${BLUE}Waiting for services to be healthy...${NC}"
    
    while [ $wait_time -lt $max_wait ]; do
        if $compose_cmd ps | grep -q "healthy"; then
            echo -e "${GREEN}Services are healthy!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}Waiting... ($wait_time/$max_wait seconds)${NC}"
        sleep 5
        wait_time=$((wait_time + 5))
    done
    
    echo -e "${YELLOW}Services may still be starting up. Check logs with: make logs${NC}"
}

# Main function
main() {
    local mode=${1:-production}
    local action=${2:-start}
    local compose_cmd
    
    echo -e "${BLUE}KB Tracker Backend - Universal Run Script${NC}"
    echo -e "${YELLOW}Mode: $mode, Action: $action${NC}"
    
    # Detect docker compose command
    compose_cmd=$(detect_docker_compose)
    echo -e "${GREEN}Using: $compose_cmd${NC}"
    
    case $action in
        "start")
            case $mode in
                "dev"|"development")
                    echo -e "${BLUE}Starting in development mode...${NC}"
                    check_images "dev"
                    $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml up -d
                    wait_for_services "$compose_cmd"
                    ;;
                    
                "prod"|"production")
                    echo -e "${BLUE}Starting in production mode...${NC}"
                    check_images "prod"
                    $compose_cmd up -d
                    wait_for_services "$compose_cmd"
                    ;;
                    
                *)
                    echo -e "${RED}Invalid mode: $mode${NC}"
                    echo -e "${YELLOW}Usage: $0 [dev|prod] [start|stop|restart|logs|status]${NC}"
                    exit 1
                    ;;
            esac
            ;;
            
        "stop")
            echo -e "${BLUE}Stopping services...${NC}"
            $compose_cmd down
            ;;
            
        "restart")
            echo -e "${BLUE}Restarting services...${NC}"
            $compose_cmd restart
            ;;
            
        "logs")
            echo -e "${BLUE}Showing logs...${NC}"
            $compose_cmd logs -f
            ;;
            
        "status")
            echo -e "${BLUE}Service status:${NC}"
            $compose_cmd ps
            echo ""
            echo -e "${GREEN}Available endpoints:${NC}"
            echo "  API: http://localhost:3000"
            echo "  Docs: http://localhost:3000/api/docs"
            echo "  Health: http://localhost:3000/api/v1/health"
            if [ "$mode" = "dev" ] || [ "$mode" = "development" ]; then
                echo "  pgAdmin: http://localhost:8080"
                echo "  Redis Commander: http://localhost:8081"
            fi
            ;;
            
        "build-and-start")
            case $mode in
                "dev"|"development")
                    echo -e "${BLUE}Building and starting in development mode...${NC}"
                    # Try compose build first, fallback to build script
                    if ! $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml up --build -d; then
                        echo -e "${YELLOW}Compose build failed, using build script...${NC}"
                        ./scripts/docker-build.sh dev
                        $compose_cmd -f docker-compose.yml -f docker-compose.dev.yml up -d
                    fi
                    wait_for_services "$compose_cmd"
                    ;;

                "prod"|"production")
                    echo -e "${BLUE}Building and starting in production mode...${NC}"
                    # Try compose build first, fallback to build script
                    if ! $compose_cmd up --build -d; then
                        echo -e "${YELLOW}Compose build failed, using build script...${NC}"
                        ./scripts/docker-build.sh prod
                        $compose_cmd up -d
                    fi
                    wait_for_services "$compose_cmd"
                    ;;
            esac
            ;;
            
        *)
            echo -e "${RED}Invalid action: $action${NC}"
            echo -e "${YELLOW}Usage: $0 [dev|prod] [start|stop|restart|logs|status|build-and-start]${NC}"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}Operation completed successfully!${NC}"
}

# Run main function with all arguments
main "$@"
