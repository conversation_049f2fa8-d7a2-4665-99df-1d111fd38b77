#!/bin/bash

# KB Tracker Docker Setup Script
# This script helps set up the complete Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker is installed: $(docker --version)"
    
    # Check Docker Compose (both old and new syntax)
    if command -v $DOCKER_COMPOSE_CMD &> /dev/null; then
        DOCKER_COMPOSE_CMD="$DOCKER_COMPOSE_CMD"
        print_success "Docker Compose is installed: $($DOCKER_COMPOSE_CMD --version)"
    elif docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        print_success "Docker Compose is installed: $(docker compose version)"
    else
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker daemon is running"
    
    # Check available disk space (at least 5GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 5242880 ]; then # 5GB in KB
        print_warning "Less than 5GB disk space available. You may encounter issues."
    else
        print_success "Sufficient disk space available"
    fi
    
    # Check available memory
    if command -v free &> /dev/null; then
        available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
        if [ "$available_memory" -lt 2048 ]; then # 2GB
            print_warning "Less than 2GB memory available. Performance may be affected."
        else
            print_success "Sufficient memory available"
        fi
    fi
}

# Function to setup environment
setup_environment() {
    print_header "Setting Up Environment"
    
    if [ ! -f .env ]; then
        print_step "Creating .env file from template..."
        cp .env.example .env
        print_success ".env file created"
        print_warning "Please review and update the .env file with your configuration"
    else
        print_success ".env file already exists"
    fi
    
    # Create necessary directories
    print_step "Creating necessary directories..."
    mkdir -p uploads reports logs
    print_success "Directories created"
    
    # Set permissions
    print_step "Setting permissions..."
    chmod +x scripts/*.sh
    print_success "Permissions set"
}

# Function to build images
build_images() {
    print_header "Building Docker Images"
    
    print_step "Building application images..."
    if $DOCKER_COMPOSE_CMD build; then
        print_success "Images built successfully"
    else
        print_error "Failed to build images"
        exit 1
    fi
}

# Function to start services
start_services() {
    print_header "Starting Services"
    
    print_step "Starting PostgreSQL and Redis..."
    $DOCKER_COMPOSE_CMD up -d postgres redis
    
    print_step "Waiting for services to be ready..."
    sleep 10
    
    # Wait for PostgreSQL
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
            print_success "PostgreSQL is ready"
            break
        fi
        print_status "Waiting for PostgreSQL... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    
    # Wait for Redis
    if $DOCKER_COMPOSE_CMD exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is ready"
    else
        print_error "Redis failed to start"
        exit 1
    fi
}

# Function to run migrations
run_migrations() {
    print_header "Running Database Migrations"
    
    print_step "Running Prisma migrations..."
    if ./scripts/docker-migrate.sh migrate; then
        print_success "Migrations completed"
    else
        print_error "Migrations failed"
        exit 1
    fi
    
    print_step "Running database seeds..."
    if ./scripts/docker-migrate.sh seed; then
        print_success "Seeds completed"
    else
        print_warning "Seeds failed or no seed data available"
    fi
}

# Function to start application services
start_application() {
    print_header "Starting Application Services"
    
    print_step "Starting NestJS services..."
    $DOCKER_COMPOSE_CMD up -d nest-api nest-worker nest-scheduler
    
    print_step "Waiting for API to be ready..."
    sleep 15
    
    # Check API health
    max_attempts=20
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:3000/api/v1/health > /dev/null 2>&1; then
            print_success "API is ready and responding"
            break
        fi
        print_status "Waiting for API... (attempt $attempt/$max_attempts)"
        sleep 3
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_warning "API health check failed, but services may still be starting"
    fi
}

# Function to show status
show_status() {
    print_header "Service Status"
    
    $DOCKER_COMPOSE_CMD ps
    
    echo ""
    print_header "Available Services"
    echo -e "${GREEN}✅ API Server:${NC} http://localhost:3000"
    echo -e "${GREEN}✅ API Documentation:${NC} http://localhost:3000/api/docs"
    echo -e "${GREEN}✅ PostgreSQL:${NC} localhost:5432"
    echo -e "${GREEN}✅ Redis:${NC} localhost:6379"
    
    if $DOCKER_COMPOSE_CMD ps | grep -q "redis-commander.*Up"; then
        echo -e "${GREEN}✅ Redis Commander:${NC} http://localhost:8081"
    fi
    
    if $DOCKER_COMPOSE_CMD ps | grep -q "pgadmin.*Up"; then
        echo -e "${GREEN}✅ pgAdmin:${NC} http://localhost:8080"
    fi
    
    echo ""
    print_header "Useful Commands"
    echo "View logs:           $DOCKER_COMPOSE_CMD logs -f"
    echo "Stop services:       $DOCKER_COMPOSE_CMD down"
    echo "Restart services:    $DOCKER_COMPOSE_CMD restart"
    echo "Run migrations:      ./scripts/docker-migrate.sh migrate"
    echo "Open Prisma Studio:  ./scripts/docker-migrate.sh studio"
    echo ""
}

# Function to show help
show_help() {
    echo "KB Tracker Docker Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  full        Complete setup (default)"
    echo "  check       Check prerequisites only"
    echo "  env         Setup environment only"
    echo "  build       Build images only"
    echo "  start       Start services only"
    echo "  migrate     Run migrations only"
    echo "  status      Show service status"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Full setup"
    echo "  $0 full         # Full setup"
    echo "  $0 check        # Check prerequisites"
    echo "  $0 status       # Show current status"
    echo ""
}

# Function for full setup
full_setup() {
    print_header "KB Tracker Docker Setup"
    print_status "Starting complete setup process..."
    
    check_prerequisites
    setup_environment
    build_images
    start_services
    run_migrations
    start_application
    show_status
    
    print_header "Setup Complete!"
    print_success "KB Tracker is now running!"
    print_status "Check the service status above for available endpoints."
}

# Main script logic
case "${1:-full}" in
    "full")
        full_setup
        ;;
    "check")
        check_prerequisites
        ;;
    "env")
        setup_environment
        ;;
    "build")
        build_images
        ;;
    "start")
        start_services
        ;;
    "migrate")
        run_migrations
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        show_help
        ;;
esac
