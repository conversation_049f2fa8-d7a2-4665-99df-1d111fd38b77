#!/bin/bash

# Docker Migration Script for KB Tracker
# This script handles database migrations in Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if services are running
check_services() {
    print_status "Checking if required services are running..."
    
    if ! docker compose ps postgres | grep -q "Up"; then
        print_error "PostgreSQL service is not running. Please start it first:"
        echo "docker compose up -d postgres"
        exit 1
    fi
    
    print_success "Required services are running"
}

# Function to wait for database to be ready
wait_for_db() {
    print_status "Waiting for database to be ready..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker compose exec -T postgres pg_isready -U postgres -d kb_tracker > /dev/null 2>&1; then
            print_success "Database is ready"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - Database not ready yet, waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Database failed to become ready after $max_attempts attempts"
    exit 1
}

# Function to run migrations
run_migrations() {
    print_status "Running Prisma migrations..."
    
    if docker compose -f docker-compose.yml -f docker-compose.migrate.yml run --rm migrate; then
        print_success "Migrations completed successfully"
    else
        print_error "Migration failed"
        exit 1
    fi
}

# Function to run seeds
run_seeds() {
    print_status "Running database seeds..."
    
    if docker compose -f docker-compose.yml -f docker-compose.migrate.yml run --rm seed; then
        print_success "Seeds completed successfully"
    else
        print_warning "Seeds failed or no seed data available"
    fi
}

# Function to reset database (development only)
reset_database() {
    print_warning "This will completely reset the database. All data will be lost!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting database..."
        
        if docker compose -f docker-compose.yml -f docker-compose.migrate.yml run --rm db-reset; then
            print_success "Database reset completed successfully"
        else
            print_error "Database reset failed"
            exit 1
        fi
    else
        print_status "Database reset cancelled"
    fi
}

# Function to show help
show_help() {
    echo "KB Tracker Docker Migration Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  migrate     Run Prisma migrations"
    echo "  seed        Run database seeds"
    echo "  reset       Reset database (development only)"
    echo "  full        Run migrations and seeds"
    echo "  studio      Start Prisma Studio"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 migrate              # Run migrations only"
    echo "  $0 full                 # Run migrations and seeds"
    echo "  $0 studio               # Start Prisma Studio on port 5555"
    echo ""
}

# Function to start Prisma Studio
start_studio() {
    print_status "Starting Prisma Studio..."
    print_status "Prisma Studio will be available at http://localhost:5555"
    print_status "Press Ctrl+C to stop"
    
    docker compose -f docker-compose.yml -f docker-compose.migrate.yml up studio
}

# Main script logic
case "${1:-help}" in
    "migrate")
        check_services
        wait_for_db
        run_migrations
        ;;
    "seed")
        check_services
        wait_for_db
        run_seeds
        ;;
    "reset")
        check_services
        wait_for_db
        reset_database
        ;;
    "full")
        check_services
        wait_for_db
        run_migrations
        run_seeds
        ;;
    "studio")
        check_services
        wait_for_db
        start_studio
        ;;
    "help"|*)
        show_help
        ;;
esac
