#!/bin/bash

# Scheduled Tasks Testing Script
# This script demonstrates all the functionality of the scheduled task system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:3000/api/v1"
TOKEN=""

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to get authentication token
get_auth_token() {
    print_header "Getting Authentication Token"
    
    # You'll need to replace these with actual credentials
    local email="<EMAIL>"
    local password="Elwanex447$"
    
    print_status "Attempting to login with email: $email"
    
    local response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$email\",\"password\":\"$password\"}" \
        2>/dev/null || echo '{"error":"failed"}')

    TOKEN=$(echo "$response" | jq -r '.accessToken // empty' 2>/dev/null)
    
    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        print_warning "Could not get auth token. Using mock token for demonstration."
        TOKEN="mock-jwt-token-for-testing"
    else
        print_success "Authentication successful"
    fi
}

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    local url="$API_BASE_URL/$endpoint"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" "$url" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $TOKEN" \
            -d "$data"
    else
        curl -s -X "$method" "$url" \
            -H "Authorization: Bearer $TOKEN"
    fi
}

# Function to create console log task (every minute for testing)
create_console_log_task() {
    print_header "Creating Console Log Task (Hi I work)"
    
    local run_at=$(date -u -d '+1 minute' +%Y-%m-%dT%H:%M:%S.000Z)
    
    local payload='{
        "type": "console-log",
        "name": "Hi I Work Logger",
        "description": "Logs Hi I work every minute",
        "payload": {
            "message": "Hi I work",
            "level": "info",
            "metadata": {
                "source": "scheduled-task-test",
                "environment": "development"
            }
        },
        "runAt": "'$run_at'",
        "intervalType": "MINUTES",
        "intervalValue": 1,
        "priority": 5,
        "maxAttempts": 3
    }'
    
    print_status "Creating console log task that runs every minute..."
    local response=$(api_call "POST" "scheduled-tasks" "$payload")
    
    local task_id=$(echo "$response" | jq -r '.id // empty' 2>/dev/null || echo "")
    
    if [ -n "$task_id" ] && [ "$task_id" != "null" ]; then
        print_success "Console log task created with ID: $task_id"
        echo "$task_id" > /tmp/console_log_task_id
    else
        print_error "Failed to create console log task"
        echo "$response" | jq . 2>/dev/null || echo "$response"
    fi
}

# Function to create daily Nairobi task
create_nairobi_task() {
    print_header "Creating Daily 11:05 PM Nairobi Task"
    
    # Calculate 10:45 PM Nairobi time in UTC (Nairobi is UTC+3)
    # 10:45 PM EAT = 7:45 PM UTC
    local run_at=$(TZ=UTC date -d 'today 20:10' +%Y-%m-%dT%H:%M:%S.000Z)
    
    # If it's already past 7:45 PM UTC today, schedule for tomorrow
    local current_hour=$(date -u +%H)
    local current_minute=$(date -u +%M)
    if [ "$current_hour" -gt 19 ] || ([ "$current_hour" -eq 19 ] && [ "$current_minute" -gt 45 ]); then
        run_at=$(TZ=UTC date -d 'tomorrow 20:10' +%Y-%m-%dT%H:%M:%S.000Z)
    fi
    
    local payload='{
        "type": "daily-nairobi-task",
        "name": "Daily 11:05 PM Nairobi Task",
        "description": "Runs daily at 11:05 PM Africa/Nairobi timezone",
        "payload": {
            "message": "Daily task executed at 11:05 PM Nairobi time",
            "timezone": "Africa/Nairobi",
            "localTime": "23:10",
            "metadata": {
                "taskType": "daily-reminder",
                "location": "Nairobi",
                "purpose": "End of day processing"
            }
        },
        "runAt": "'$run_at'",
        "cronExpression": "45 19 * * *",
        "priority": 7,
        "maxAttempts": 2
    }'
    
    print_status "Creating daily Nairobi task (10:45 PM EAT = 7:45 PM UTC)..."
    print_status "Next run time: $run_at"
    
    local response=$(api_call "POST" "scheduled-tasks" "$payload")
    
    local task_id=$(echo "$response" | jq -r '.id // empty' 2>/dev/null || echo "")
    
    if [ -n "$task_id" ] && [ "$task_id" != "null" ]; then
        print_success "Daily Nairobi task created with ID: $task_id"
        echo "$task_id" > /tmp/nairobi_task_id
    else
        print_error "Failed to create daily Nairobi task"
        echo "$response" | jq . 2>/dev/null || echo "$response"
    fi
}

# Function to create various test tasks
create_test_tasks() {
    print_header "Creating Additional Test Tasks"
    
    # Email task
    print_status "Creating email task..."
    local email_payload='{
        "type": "send-email",
        "name": "Test Email",
        "payload": {
            "to": "<EMAIL>",
            "subject": "Test Email from Scheduled Task",
            "template": "test",
            "context": {"name": "Test User"}
        },
        "runAt": "'$(date -u -d '+2 minutes' +%Y-%m-%dT%H:%M:%S.000Z)'",
        "priority": 8
    }'
    
    api_call "POST" "scheduled-tasks" "$email_payload" > /dev/null
    print_success "Email task created"
    
    # Report generation task
    print_status "Creating report generation task..."
    local report_payload='{
        "type": "generate-report",
        "name": "Weekly Test Report",
        "payload": {
            "reportType": "test",
            "userId": "test-user-123",
            "format": "excel"
        },
        "runAt": "'$(date -u -d '+3 minutes' +%Y-%m-%dT%H:%M:%S.000Z)'",
        "intervalType": "WEEKS",
        "intervalValue": 1,
        "priority": 6
    }'
    
    api_call "POST" "scheduled-tasks" "$report_payload" > /dev/null
    print_success "Report generation task created"
    
    # Cleanup task
    print_status "Creating cleanup task..."
    local cleanup_payload='{
        "type": "cleanup",
        "name": "System Cleanup",
        "payload": {
            "cleanupType": "system-maintenance",
            "targets": ["old-logs", "temp-files"]
        },
        "runAt": "'$(date -u -d '+4 minutes' +%Y-%m-%dT%H:%M:%S.000Z)'",
        "priority": 3
    }'
    
    api_call "POST" "scheduled-tasks" "$cleanup_payload" > /dev/null
    print_success "Cleanup task created"
}

# Function to list tasks
list_tasks() {
    print_header "Listing All Scheduled Tasks"
    
    local response=$(api_call "GET" "scheduled-tasks?limit=20")
    
    print_status "Current scheduled tasks:"
    echo "$response" | jq '.tasks[] | {id: .id, type: .type, name: .name, status: .status, runAt: .run_at, priority: .priority}' 2>/dev/null || echo "$response"
}

# Function to get task types
get_task_types() {
    print_header "Available Task Types"
    
    local response=$(api_call "GET" "scheduled-tasks/task-types")
    
    print_status "Available task types:"
    echo "$response" | jq '.taskTypes[] | {type: .type, description: .description}' 2>/dev/null || echo "$response"
}

# Function to monitor task execution
monitor_tasks() {
    print_header "Monitoring Task Execution"
    
    print_status "Monitoring logs for task execution..."
    print_status "You can watch the logs with:"
    echo "docker compose logs -f nest-worker"
    echo "docker compose logs -f nest-scheduler"
    
    # Check if we have task IDs to monitor
    if [ -f /tmp/console_log_task_id ]; then
        local console_task_id=$(cat /tmp/console_log_task_id)
        print_status "Console log task ID: $console_task_id"
        
        # Get task details
        local task_details=$(api_call "GET" "scheduled-tasks/$console_task_id")
        echo "$task_details" | jq '{id: .id, status: .status, attempts: .attempts, lastRunAt: .last_run_at, nextRunAt: .next_run_at}' 2>/dev/null || echo "$task_details"
    fi
    
    if [ -f /tmp/nairobi_task_id ]; then
        local nairobi_task_id=$(cat /tmp/nairobi_task_id)
        print_status "Nairobi task ID: $nairobi_task_id"
        
        # Get task details
        local task_details=$(api_call "GET" "scheduled-tasks/$nairobi_task_id")
        echo "$task_details" | jq '{id: .id, status: .status, runAt: .run_at, cronExpression: .cron_expression}' 2>/dev/null || echo "$task_details"
    fi
}

# Function to demonstrate task management
demonstrate_task_management() {
    print_header "Demonstrating Task Management"
    
    if [ -f /tmp/console_log_task_id ]; then
        local task_id=$(cat /tmp/console_log_task_id)
        
        print_status "Pausing console log task..."
        api_call "POST" "scheduled-tasks/$task_id/pause" > /dev/null
        print_success "Task paused"
        
        sleep 2
        
        print_status "Resuming console log task..."
        api_call "POST" "scheduled-tasks/$task_id/resume" > /dev/null
        print_success "Task resumed"
        
        print_status "Getting task execution history..."
        local executions=$(api_call "GET" "scheduled-tasks/$task_id/executions")
        echo "$executions" | jq '.executions[] | {status: .status, startedAt: .started_at, durationMs: .duration_ms}' 2>/dev/null || echo "$executions"
    fi
}

# Main execution
main() {
    print_header "Scheduled Tasks Testing Script"
    
    # Check if required tools are available
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed. JSON output will not be formatted."
    fi
    
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed."
        exit 1
    fi
    
    # Get authentication token
    get_auth_token
    
    # Get available task types
    get_task_types
    
    # Create the specific tasks requested
    create_console_log_task
    create_nairobi_task
    
    # Create additional test tasks
    create_test_tasks
    
    # List all tasks
    list_tasks
    
    # Monitor tasks
    monitor_tasks
    
    # Demonstrate task management
    demonstrate_task_management
    
    print_header "Testing Complete"
    print_success "All test tasks have been created!"
    print_status "Monitor the logs to see task execution:"
    echo "  docker compose logs -f nest-worker"
    echo "  docker compose logs -f nest-scheduler"
    print_status "Check the database to see task records:"
    echo "  SELECT * FROM scheduled_tasks ORDER BY created_at DESC;"
    echo "  SELECT * FROM scheduled_task_executions ORDER BY started_at DESC;"
}

# Run the main function
main "$@"
