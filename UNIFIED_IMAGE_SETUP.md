# KB Tracker Backend - Unified Image Setup

## 🎯 Problem Solved

**Issue**: Each service (API, Worker, Scheduler) was building its own Docker image, resulting in multiple images:
- `kb-tracker-backend-nest-api:latest`
- `kb-tracker-backend-nest-worker:latest` 
- `kb-tracker-backend-nest-scheduler:latest`

**Solution**: All services now use the same Docker image with different entry points.

## ✅ Changes Made

### 1. Docker Compose Configuration Updates

#### Production (`docker-compose.yml`)
```yaml
# Before: Each service had its own build section
nest-worker:
  build:
    context: .
    dockerfile: Dockerfile
    target: production

# After: Services reference the same image
nest-api:
  build:
    context: .
    dockerfile: Dockerfile
    target: production
  image: kb-tracker-backend:latest

nest-worker:
  image: kb-tracker-backend:latest
  command: ['node', 'dist/src/worker.js']
  depends_on:
    nest-api:
      condition: service_started

nest-scheduler:
  image: kb-tracker-backend:latest
  command: ['node', 'dist/src/scheduler.js']
  depends_on:
    nest-api:
      condition: service_started
```

#### Development (`docker-compose.dev.yml`)
```yaml
# Similar approach for development
nest-api:
  build:
    context: .
    dockerfile: Dockerfile
    target: dependencies
  image: kb-tracker-backend-dev:latest

nest-worker:
  image: kb-tracker-backend-dev:latest
  command: npm run start:worker:dev

nest-scheduler:
  image: kb-tracker-backend-dev:latest
  command: npm run start:scheduler:dev
```

### 2. Updated All Commands to Use `docker compose`

Changed from legacy `docker-compose` to modern `docker compose` in:
- `Makefile` - All make targets
- Documentation examples
- Script references

### 3. Added Dependency Management

Worker and Scheduler services now depend on the API service to ensure proper build order:
```yaml
depends_on:
  nest-api:
    condition: service_started
```

## 🚀 How to Use

### Development Mode (Hot Reload)
```bash
# Start all services with hot reload
make dev

# Or using docker compose directly
docker compose -f docker-compose.yml -f docker-compose.dev.yml up

# Build and start (if dependencies changed)
make dev-build
```

### Production Mode
```bash
# Start production services
make prod

# Or using docker compose directly
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Build and start
make prod-build
```

### Verify Single Image Usage

After starting, check that only one image exists per environment:

```bash
# Check images
docker images | grep kb-tracker

# Expected output (production):
# kb-tracker-backend    latest    abc123    X minutes ago    1.42GB

# Expected output (development):
# kb-tracker-backend-dev    latest    def456    X minutes ago    1.42GB
```

## 🔧 Service Management

### Individual Service Control
```bash
# Restart specific services
docker compose restart nest-api
docker compose restart nest-worker
docker compose restart nest-scheduler

# View logs for specific services
docker compose logs -f nest-api
docker compose logs -f nest-worker
docker compose logs -f nest-scheduler

# Scale services (API and Worker only - Scheduler should remain single instance)
docker compose up -d --scale nest-worker=3
docker compose up -d --scale nest-api=2
```

### Database Operations
```bash
# Run migrations
make migrate

# Run seeds
make seed

# Start Prisma Studio
make studio

# Reset database (development only)
make reset
```

## 📊 Benefits Achieved

1. **Storage Efficiency**: Single image instead of three separate images
2. **Build Speed**: Faster builds as only one image needs to be built
3. **Consistency**: All services run identical code and dependencies
4. **Maintenance**: Easier to maintain and update single image
5. **Deployment**: Simplified deployment process

## 🔍 Troubleshooting

### If You Still See Multiple Images

1. **Clean existing images**:
   ```bash
   # Stop all services
   make down
   
   # Remove old images
   docker rmi kb-tracker-backend-nest-worker:latest
   docker rmi kb-tracker-backend-nest-scheduler:latest
   
   # Rebuild
   make build
   ```

2. **Force rebuild**:
   ```bash
   docker compose build --no-cache
   docker compose up -d
   ```

### Service Dependencies

If services fail to start:
1. Check that the API service builds successfully first
2. Verify the image name matches in all service definitions
3. Ensure `depends_on` is properly configured

## 📚 Documentation Updated

- **DEPLOYMENT_GUIDE.md**: Comprehensive deployment guide with unified image approach
- **DOCKER_SETUP.md**: Updated with unified image information and quick reference
- **Makefile**: All commands updated to use `docker compose`
- **Scripts**: Already using `docker compose` (no changes needed)

## 🎉 Ready for Production Launch

The system is now optimized with:
- ✅ Single image for all services
- ✅ Modern `docker compose` commands
- ✅ Comprehensive documentation
- ✅ Development and production configurations
- ✅ Hot reload for development
- ✅ Scaling capabilities for production
- ✅ Proper service dependencies

Your KB Tracker backend is ready for launch! 🚀
