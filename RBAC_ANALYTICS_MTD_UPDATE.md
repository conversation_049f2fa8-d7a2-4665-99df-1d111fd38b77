# RBAC Analytics MTD Update Documentation

## Overview

Enhanced the existing `GET /api/v1/leads/rbac-analytics` endpoint to include Month-to-Date (MTD) activity data for visits and calls made by the logged-in user.

## Changes Made

### 1. Service Layer Updates (`src/leads/leads.service.ts`)

#### Added MTD Calculation Logic
- **Start of Month**: Calculated as the first day of the current month at 00:00:00
- **End of Month**: Calculated as the last day of the current month at 23:59:59.999

#### New Database Queries
Added two new parallel queries to the existing `getRbacLeadAnalytics` method:

1. **Total Visits MTD**: Counts activities where:
   - `performed_by_user_id` = logged-in user's ID
   - `interaction_type` = 'visit'
   - `created_at` is within the current month

2. **Total Calls MTD**: Counts activities where:
   - `performed_by_user_id` = logged-in user's ID
   - `interaction_type` = 'call'
   - `created_at` is within the current month

#### Updated Response Structure
Added new `user_activity_mtd` object to the response containing:
- `total_visits`: Number of visits made by the user in the current month
- `total_calls`: Number of calls made by the user in the current month
- `month_year`: Current month and year in YYYY-MM format

### 2. Controller Layer Updates (`src/leads/leads.controller.ts`)

#### Updated Swagger Documentation
- Enhanced API operation description to mention MTD activity data
- Updated response schema to include the new `user_activity_mtd` object
- Added detailed descriptions for each MTD field

## API Response Structure

### Before (Original Response)
```json
{
  "total_leads": 150,
  "contacted_leads": 85,
  "leads_by_status": {
    "pending": 45,
    "warm": 30,
    "hot": 25,
    "cold": 50
  },
  "user_permissions": {
    "can_view_all_leads": true,
    "applied_filter": "all_leads"
  },
  "filters_applied": {
    "branch_id": null,
    "start_date": null,
    "end_date": null
  }
}
```

### After (Enhanced Response)
```json
{
  "total_leads": 150,
  "contacted_leads": 85,
  "leads_by_status": {
    "pending": 45,
    "warm": 30,
    "hot": 25,
    "cold": 50
  },
  "user_activity_mtd": {
    "total_visits": 12,
    "total_calls": 25,
    "month_year": "2025-08"
  },
  "user_permissions": {
    "can_view_all_leads": true,
    "applied_filter": "all_leads"
  },
  "filters_applied": {
    "branch_id": null,
    "start_date": null,
    "end_date": null
  }
}
```

## Technical Implementation Details

### Database Schema Dependencies
- **Activity Model**: Uses `interaction_type` field to distinguish between 'visit' and 'call'
- **User Association**: Uses `performed_by_user_id` to link activities to users
- **Timestamp**: Uses `created_at` field for date range filtering

### Performance Considerations
- MTD queries are executed in parallel with existing queries using `Promise.all()`
- Queries are optimized with specific indexes on:
  - `performed_by_user_id`
  - `interaction_type`
  - `created_at`

### Date Handling
- Uses JavaScript Date object for month calculations
- Handles month boundaries correctly (including leap years)
- Timezone-aware calculations based on server timezone

## Usage Examples

### JavaScript/Fetch
```javascript
const response = await fetch('/api/v1/leads/rbac-analytics', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${yourJwtToken}`,
    'Content-Type': 'application/json'
  }
});

const analytics = await response.json();
console.log(`Visits this month: ${analytics.user_activity_mtd.total_visits}`);
console.log(`Calls this month: ${analytics.user_activity_mtd.total_calls}`);
```

### cURL
```bash
curl -X GET \
  "http://localhost:3000/api/v1/leads/rbac-analytics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Frontend Integration Suggestions

### Activity Dashboard
```javascript
// Display MTD activity metrics
const { user_activity_mtd } = analyticsData;

const activityMetrics = {
  visits: user_activity_mtd.total_visits,
  calls: user_activity_mtd.total_calls,
  month: user_activity_mtd.month_year
};

// Example: Show progress towards monthly goals
const monthlyVisitGoal = 50;
const monthlyCallGoal = 100;

const visitProgress = (activityMetrics.visits / monthlyVisitGoal) * 100;
const callProgress = (activityMetrics.calls / monthlyCallGoal) * 100;
```

### Performance Tracking
```javascript
// Track user performance trends
const performanceData = {
  currentMonth: {
    visits: user_activity_mtd.total_visits,
    calls: user_activity_mtd.total_calls
  },
  // Compare with previous months or targets
  efficiency: {
    visitsPerLead: user_activity_mtd.total_visits / total_leads,
    callsPerLead: user_activity_mtd.total_calls / total_leads
  }
};
```

## Security & Permissions

- **Authentication Required**: JWT token must be provided
- **User-Specific Data**: MTD data is filtered by the logged-in user's ID
- **RBAC Compliance**: Respects existing permission structure for lead analytics
- **Data Isolation**: Users can only see their own activity data

## Testing Recommendations

1. **Unit Tests**: Test MTD calculation logic with different month scenarios
2. **Integration Tests**: Verify correct activity counting and user filtering
3. **Edge Cases**: Test month boundaries, leap years, and timezone handling
4. **Performance Tests**: Ensure parallel queries don't impact response time

## Backward Compatibility

- **Fully Backward Compatible**: Existing API consumers will continue to work
- **Additive Changes Only**: No existing fields were modified or removed
- **Optional Data**: New MTD data is additional information that can be ignored if not needed

## Future Enhancements

1. **Configurable Date Ranges**: Allow custom date ranges for activity metrics
2. **Activity Type Expansion**: Support additional interaction types beyond visits/calls
3. **Comparative Analytics**: Add previous month comparison data
4. **Goal Tracking**: Integrate with user-defined monthly targets
5. **Caching**: Implement caching for frequently accessed MTD data
