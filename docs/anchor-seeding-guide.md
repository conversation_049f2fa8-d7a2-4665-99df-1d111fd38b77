# Anchor Seeding Guide

## Overview

This guide documents the anchor seeding implementation for the KB Tracker Backend application. The seeding creates realistic test data for anchor entities that can be used for development, testing, and demonstration purposes.

## Seeded Anchors

The seed creates **10 diverse anchor organizations** representing different business sectors and regions in Kenya:

### 🏢 Corporate Entities
1. **ABC Corporation**
   - Email: `<EMAIL>`
   - Phone: `0712345678`
   - Type: Large corporation

2. **XYZ Holdings Ltd**
   - Email: `<EMAIL>`
   - Phone: `0723456789`
   - Type: Holdings company

### 🤝 Partnership Organizations
3. **Kenya Business Partners**
   - Email: `<EMAIL>`
   - Phone: `0734567890`
   - Type: Business partnership network

### 🌍 Regional Enterprises
4. **East Africa Enterprises**
   - Email: `<EMAIL>`
   - Phone: `0745678901`
   - Type: Regional business entity

5. **Nairobi Investment Group**
   - Email: `<EMAIL>`
   - Phone: `0756789012`
   - Type: Investment group

### 🏭 Industry-Specific Organizations
6. **Mombasa Trade Center**
   - Email: `<EMAIL>`
   - Phone: `0767890123`
   - Type: Trade and commerce hub

7. **Nakuru Manufacturing Alliance**
   - Email: `<EMAIL>`
   - Phone: `0790123456`
   - Type: Manufacturing consortium

8. **Thika Industrial Park**
   - Email: `<EMAIL>`
   - Phone: `0701234567`
   - Type: Industrial park

### 🌾 Agricultural Organizations
9. **Eldoret Agricultural Hub**
   - Email: `<EMAIL>`
   - Phone: `0789012345`
   - Type: Agricultural cooperative

### 🏗️ Development Organizations
10. **Kisumu Development Corp**
    - Email: `<EMAIL>`
    - Phone: `0778901234`
    - Type: Development corporation

## Seeding Implementation

### Location
The anchor seeding is implemented in `prisma/seed.ts` and is integrated with the existing seeding system.

### Code Structure
```typescript
// Check if anchors already exist
const existingAnchors = await prisma.anchor.findMany();

if (existingAnchors.length === 0) {
  console.log('⚓ Creating test anchors...');

  const anchor1 = await prisma.anchor.create({
    data: {
      name: 'ABC Corporation',
      email: '<EMAIL>',
      phone_number: '0712345678',
    },
  });
  
  // ... additional anchors
  
  console.log(`✅ Created anchors: ${anchor1.name}, ...`);
} else {
  console.log(`⚓ Anchors already exist (${existingAnchors.length} found)`);
}
```

### Key Features
- **Idempotent**: Only creates anchors if none exist
- **Realistic Data**: Uses authentic Kenyan business names and contact formats
- **Diverse Sectors**: Covers various business types and industries
- **Regional Coverage**: Includes organizations from different Kenyan cities
- **Valid Formats**: All emails and phone numbers follow proper formats

## Running the Seed

### Command
```bash
npm run db:seed
```

### Expected Output
```
🌱 Starting database seeding...
🔐 Permissions already exist (8 found)
📍 Using existing regions: Central Region, North Eastern Region, ...
🏢 Branches already exist (26 found)
🏷️ Customer categories already exist (7 found)
🏭 Employers already exist (7 found)
👥 Users already exist (2 found)
⚓ Creating test anchors...
✅ Created anchors: ABC Corporation, XYZ Holdings Ltd, Kenya Business Partners, ...
🏭 ISIC sectors already exist (6 found)
🎉 Database seeding completed!
```

## Verification

### Database Verification
After seeding, you can verify the anchors were created:

```sql
SELECT id, name, email, phone_number, created_at 
FROM anchors 
ORDER BY name;
```

### API Verification
With the server running, you can test the API endpoints:

```bash
# Get all anchors
curl http://localhost:3000/api/v1/anchors

# Get usage statistics
curl http://localhost:3000/api/v1/anchors/statistics

# Search anchors
curl "http://localhost:3000/api/v1/anchors?search=ABC"
```

## Sample Lead Relationships

The verification script also creates sample leads linked to anchors to demonstrate the relationship functionality:

### Created Leads
- **John Doe** → ABC Corporation (New Business, Active)
- **Jane Smith** → ABC Corporation (Existing Business, Active)
- **Michael Johnson** → XYZ Holdings Ltd (Referral, Active)
- **Sarah Wilson** → Kenya Business Partners (New Business, Pending)
- **David Brown** → XYZ Holdings Ltd (Existing Business, Active)

### Usage Statistics After Sample Data
- **Total Anchors**: 10
- **Anchors in Use**: 3 (30% usage rate)
- **Unused Anchors**: 7
- **Total Leads with Anchors**: 5

## Data Characteristics

### Email Formats
- Corporate domains: `.com`, `.co.ke`
- Descriptive prefixes: `contact@`, `info@`, `partnerships@`, etc.
- Professional naming conventions

### Phone Numbers
- All follow Kenyan mobile format: `07XXXXXXXX`
- Sequential numbering for easy identification
- Valid format for validation testing

### Names
- Realistic Kenyan business names
- Geographic references (Nairobi, Mombasa, Kisumu, etc.)
- Industry-specific terminology
- Professional naming conventions

## Integration with Existing System

### Dependencies
The anchor seeding runs after:
- Permissions
- Regions
- Branches
- Customer categories
- Employers
- Users

### Foreign Key Relationships
- Anchors can be referenced by leads via `anchor_id`
- Maintains referential integrity
- Supports cascade operations

## Testing Scenarios

### Positive Test Cases
1. **Creation**: All 10 anchors created successfully
2. **Uniqueness**: No duplicate emails or phone numbers
3. **Relationships**: Leads can be linked to anchors
4. **Queries**: All API endpoints work with seeded data

### Edge Cases
1. **Re-running**: Seed is idempotent (won't create duplicates)
2. **Deletion**: Anchors with leads cannot be deleted
3. **Updates**: Anchor information can be modified
4. **Search**: All search functionality works with seeded data

## Business Value

### Development Benefits
- **Realistic Testing**: Provides authentic data for development
- **API Testing**: Enables comprehensive endpoint testing
- **Relationship Testing**: Demonstrates anchor-lead relationships
- **Performance Testing**: Provides sufficient data volume

### Demonstration Benefits
- **Diverse Examples**: Shows various business types
- **Regional Representation**: Covers different Kenyan regions
- **Industry Coverage**: Represents multiple business sectors
- **Professional Appearance**: Uses realistic business names

## Maintenance

### Adding New Anchors
To add new anchors to the seed:

1. Add new anchor data to the `prisma/seed.ts` file
2. Follow the existing naming and format conventions
3. Ensure unique email and phone numbers
4. Update the success message to include new anchors

### Modifying Existing Anchors
To modify existing anchor data:

1. Update the data in `prisma/seed.ts`
2. Clear existing anchors if needed: `DELETE FROM anchors;`
3. Re-run the seed: `npm run db:seed`

### Regional Expansion
When expanding to new regions:

1. Add anchors representing those regions
2. Use appropriate domain extensions (.co.ke, .com, etc.)
3. Include regional business naming conventions
4. Update documentation accordingly

## Conclusion

The anchor seeding provides a robust foundation for testing and demonstrating the anchor management functionality. It creates realistic, diverse data that represents the Kenyan business landscape and enables comprehensive testing of all anchor-related features.

The seeded data supports:
- ✅ API endpoint testing
- ✅ Relationship functionality
- ✅ Search and filtering
- ✅ Usage analytics
- ✅ Business demonstrations
- ✅ Development workflows
