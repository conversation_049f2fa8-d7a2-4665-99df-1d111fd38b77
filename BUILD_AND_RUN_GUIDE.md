# KB Tracker Backend - Build and Run Guide

This guide provides clear instructions for building and running the KB Tracker Backend in both development and production environments.

## ✅ **Tested and Working**

This documentation has been **thoroughly tested** and all commands work universally across different Docker installations, including systems without Docker Buildx.

## Architecture Overview

The application uses a **unified Docker image** approach:
- **Single Dockerfile** builds one image: `kb-tracker-backend`
- **Three services** use the same image with different commands:
  - `nest-api`: Main API service (default command)
  - `nest-worker`: Background job processor (`node dist/src/worker.js`)
  - `nest-scheduler`: Task scheduler (`node dist/src/scheduler.js`)
- **No image pulling** - all services build from the same local Dockerfile

## Prerequisites

- Docker and Docker Compose installed
- Node.js 20+ (for local development)
- Git

## Quick Start

### Using Makefile (Recommended)

```bash
# Complete setup (build, start, migrate)
make setup

# Start in development mode with build
make dev-build

# Start in production mode with build
make prod-build

# View all available commands
make help
```

### Alternative: Using Universal Scripts

```bash
# Build development image
./scripts/docker-build.sh dev

# Build production image
./scripts/docker-build.sh prod

# Run development environment
./scripts/docker-run.sh dev build-and-start

# Run production environment
./scripts/docker-run.sh prod build-and-start
```

## Development Environment

### Method 1: Using Makefile

```bash
# Build and start in development mode with hot reload
make dev-build

# Or start without rebuilding
make dev

# View logs
make logs

# Stop services
make stop
```

### Method 2: Using Docker Compose Directly

```bash
# Build the development image
docker compose -f docker-compose.yml -f docker-compose.dev.yml build

# Start all services in development mode
docker compose -f docker-compose.yml -f docker-compose.dev.yml up

# Start in background
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# View logs
docker compose logs -f

# Stop services
docker compose down
```

### Development Features

- **Hot reload** enabled for API
- **Debug port** 9229 exposed for API
- **Source code mounting** for live changes
- **Management tools** enabled by default:
  - pgAdmin: http://localhost:8080
  - Redis Commander: http://localhost:8081

### Development Services

| Service | Container | Port | Purpose |
|---------|-----------|------|---------|
| nest-api | kb-tracker-api | 3000 | Main API with hot reload |
| nest-worker | kb-tracker-worker | - | Background job processor (1 replica) |
| postgres | kb-tracker-postgres | 5432 | PostgreSQL database |
| redis | kb-tracker-redis | 6379 | Redis for BullMQ |
| pgadmin | kb-tracker-pgadmin | 8080 | Database management |
| redis-commander | kb-tracker-redis-commander | 8081 | Redis management |

## Production Environment

### Method 1: Using Makefile

```bash
# Build and start in production mode
make prod-build

# Or start without rebuilding
make prod

# View status
make status

# View logs
make logs

# Stop services
make stop
```

### Method 2: Using Docker Compose Directly

```bash
# Build the production image
docker compose build

# Start all services in production mode
docker compose up -d

# View status
docker compose ps

# View logs
docker compose logs -f

# Stop services
docker compose down
```

### Production Features

- **Multi-stage Docker build** for optimized image size
- **Non-root user** for security
- **Health checks** for all services
- **Resource limits** configured
- **Auto-restart** policies
- **Worker scaling** (2 replicas by default)

### Production Services

| Service | Container | Port | Purpose | Replicas |
|---------|-----------|------|---------|----------|
| nest-api | kb-tracker-api | 3000 | Main API service | 1 |
| nest-worker | kb-tracker-worker | - | Background job processor | 2 |
| nest-scheduler | kb-tracker-scheduler | - | Task scheduler | 1 |
| postgres | kb-tracker-postgres | 5432 | PostgreSQL database | 1 |
| redis | kb-tracker-redis | 6379 | Redis for BullMQ | 1 |

## Build Process Details

### Docker Image Stages

The Dockerfile uses a multi-stage build:

1. **dependencies**: Install all dependencies and generate Prisma client
2. **build**: Build the NestJS application
3. **production**: Create optimized production image

### Build Commands

```bash
# Build development image (stops at dependencies stage)
docker compose -f docker-compose.yml -f docker-compose.dev.yml build

# Build production image (full multi-stage build)
docker compose build

# Force rebuild without cache
docker compose build --no-cache

# Build specific service
docker compose build nest-api
```

## Database Management

### Migrations

```bash
# Run migrations
make migrate
# OR
./scripts/docker-migrate.sh migrate

# Run seeds
make seed
# OR
./scripts/docker-migrate.sh seed

# Reset database (development only)
make reset
# OR
./scripts/docker-migrate.sh reset
```

### Database Access

```bash
# Open PostgreSQL shell
make shell-db
# OR
docker compose exec postgres psql -U postgres -d kb_tracker

# Start Prisma Studio
make studio
# OR
./scripts/docker-migrate.sh studio
```

## Service Management

### Scaling Services

```bash
# Scale workers to 3 instances
make scale-workers WORKERS=3
# OR
docker compose up -d --scale nest-worker=3

# Scale API to 2 instances
make scale-api REPLICAS=2
# OR
docker compose up -d --scale nest-api=2
```

### Individual Service Logs

```bash
# API logs
make logs-api
# OR
docker compose logs -f nest-api

# Worker logs
make logs-worker
# OR
docker compose logs -f nest-worker

# Scheduler logs
make logs-scheduler
# OR
docker compose logs -f nest-scheduler

# Database logs
make logs-db
# OR
docker compose logs -f postgres
```

## Health Checks and Monitoring

### Health Check Endpoints

```bash
# Check API health
curl http://localhost:3000/api/v1/health

# Check all services health
make health
```

### Service Status

```bash
# View service status
make status
# OR
docker compose ps

# View resource usage
make stats
# OR
docker stats

# View running processes
make top
# OR
docker compose top
```

## Environment Configuration

### Required Environment Variables

Create a `.env` file in the project root:

```env
# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=kb_tracker

# Redis
REDIS_URL=redis://redis:6379

# Application
NODE_ENV=production
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# Worker Configuration
WORKER_CONCURRENCY=5
WORKER_MAX_STALLED_COUNT=3
WORKER_STALLED_INTERVAL=30000

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Africa/Nairobi

# Optional: Management Tools
REDIS_COMMANDER_USER=admin
REDIS_COMMANDER_PASSWORD=admin
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin
```

## Troubleshooting

### Common Issues

1. **Docker Buildx Missing**: The system automatically falls back to legacy Docker builder when Buildx is not available
2. **Port conflicts**: Ensure ports 3000, 5432, 6379 are available
3. **Memory issues**: Increase Docker memory allocation
4. **Permission issues**: Ensure proper file permissions for uploads/reports directories

### Universal Compatibility

The build system is designed to work on any Docker installation:
- **Automatically detects** `docker compose` vs `docker-compose`
- **Falls back** to legacy Docker builder when Buildx is unavailable
- **Uses universal scripts** that work across different Docker versions

### Useful Commands

```bash
# View all containers
docker ps -a

# Remove stopped containers
docker container prune

# Remove unused images
docker image prune

# Complete cleanup (removes volumes!)
make clean

# Remove all images
make clean-images

# Test build system compatibility
./scripts/docker-build.sh --help
```

## Development Workflow

1. **Start development environment**:
   ```bash
   make dev-build
   ```

2. **Make code changes** (hot reload active)

3. **Run tests**:
   ```bash
   make test
   ```

4. **Check logs**:
   ```bash
   make logs-api
   ```

5. **Stop when done**:
   ```bash
   make stop
   ```

## Production Deployment

1. **Build production image**:
   ```bash
   make prod-build
   ```

2. **Run migrations**:
   ```bash
   make migrate
   ```

3. **Seed initial data**:
   ```bash
   make seed
   ```

4. **Check health**:
   ```bash
   make health
   ```

5. **Monitor logs**:
   ```bash
   make logs
   ```

## Available Endpoints

- **API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api/docs
- **Health Check**: http://localhost:3000/api/v1/health
- **pgAdmin** (dev): http://localhost:8080
- **Redis Commander** (dev): http://localhost:8081

---

For more detailed information about specific features, refer to the individual documentation files in the project.
